package com.aliyun.dba.commonkindcode.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamMinorVerionHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.service.DuckDBService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessParamsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.resource.support.ResourceSupport.RESOURCE_DB_TYPE;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SERVERLESS_CATEGORY_TYPE;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ParamConstants.DB_INSTANCE_NAME;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeParameterTemplateListImpl")
public class DescribeParameterTemplateListImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeParameterTemplateListImpl.class);

    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    private MysqlParamMinorVerionHelper mysqlParamMinorVerionHelper;
    @Autowired
    private ServerlessParamsSupport serverlessParamsSupport;
    @Autowired
    private MysqlParamSupport mysqlParamSupport;
    @Autowired
    private DuckDBService duckDBService;

    /**
     * 已处理
     */
    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        try {

            // 设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            // 获取参数
            String dbType = mysqlParaHelper.getAndChangeEngine();
            if (Validator.isNull(dbType) || !resourceService.getResourceRealValueList(RESOURCE_DB_TYPE).contains(dbType)) {
                return createErrorResponse(ErrorCode.INVALID_ENGINE);
            }
            String dbVersion = mysqlParaHelper.getAndCheckDBVersion(dbType, false);

            // mycnf_template.character_type ## add on sphinx
            String characterType = mysqlParaHelper.getAndCheckCharacterType(true);


            String category = mysqlParaHelper.getParameterValue("category", "standard");
            String paramGroupId = mysqlParaHelper.getParameterValue("paramGroupId");
            String dbInstance = mysqlParaHelper.getDBInstanceName();
            String isAliGroup = mysqlParaHelper.getParameterValue("isAliGroup", "false");
            String instructionSetArch = mysqlParaHelper.getParameterValue("instructionSetArch", "x86");
            String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
            if (StringUtils.isNotBlank(dbInstance)) {
                custins = mysqlParaHelper.getCustInstance();
            }
            // 如果传入了实例ID，则尝试从实例信息中获取参数模板信息
            if (StringUtils.isNotBlank(dbInstance) && Strings.isBlank(paramGroupId)) {
                if (custins != null) {
                    Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, custins.getInsName());
                    if (StringUtils.isNotBlank(labels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID))) {
                        paramGroupId = labels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
                    }
                    if (StringUtils.isNotBlank(labels.get(instructionSetArch))) {
                        instructionSetArch = labels.get("instructionSetArch");
                    }
                    if (StringUtils.isNotBlank(labels.get(instructionSetArch))) {
                        if ("aligroup".equalsIgnoreCase(labels.get("bizType"))) {
                            isAliGroup = "true";
                        }
                    }
                }
            }

            boolean isPolarxHatp = custins != null && custinsParamService.getCustinsParam(custins.getId(), "is_polarx_hatp") != null;
            ParamContext paramContext = new ParamContext(custins, paramGroupId);
            boolean ignoreVisible = false;
            String accessId = mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID);
            if (ParamConstants.DUKANG_ACCESS.equalsIgnoreCase(accessId)) {
                ignoreVisible = true;
            }
            paramContext.setIgnoreVisible(ignoreVisible);
            paramContext.setAliGroup("true".equalsIgnoreCase(isAliGroup));
            paramContext.setArm("arm".equalsIgnoreCase(instructionSetArch));
            paramContext.setPolarxHatp(isPolarxHatp);
            paramContext.setCategory(category);
            if (custins == null || custins.getId() == null) {
                paramContext.setDbType(dbType);
                paramContext.setDbVersion(dbVersion);
                paramContext.setCharacterType(characterType);
            }

//            if (custins != null && custins.isMariaDB() && custins.isCustinsDockerOnEcs()) {
//                paramContext.setDbType(dbType);
//                paramContext.setDbVersion(dbVersion);
//                paramContext.setCharacterType(CHARACTER_TYPE_NORMAL);
//            }

            if (custins != null && KindCodeParser.KIND_CODE_NEW_ARCH.equals(custins.getKindCode())) {
                ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(
                        requestId, dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(
                                requestId, custins.getInsName()));
                String tag = serviceSpec.getTag();
                paramContext.set80Beta(StringUtils.startsWith(tag,
                        MinorVersionServiceHelper.ServiceTag.TAG_ALISQL_BETA_DOCKER_IMAGE.getTagPrefix())
                );

                if ((custins.getInsType() == 3 || custins.getInsType() == 4) && custins.getPrimaryCustinsId() != 0) {
                    CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                    if (primaryCustins != null) {
                        InstanceLevelDO primaryLevel = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
                        category = primaryLevel.getCategory();
                        paramContext.setCategory(category);
                    }
                }
            }

            if (custins != null && mysqlParamSupport.isXdbReadCustins(custins)) {
                paramContext.setCategory(InstanceSupport.CATEGORY_ENTERPRISE);
            }

            List<Map<String, Object>> returnList = parameterGroupTemplateGenerator
                    .getSysBaseParamTempMapList(paramContext);

            // 屏蔽当前实例 小版本不兼容的参数
            List<String> versionUnsupportParamsList = mysqlParamMinorVerionHelper.getInstanceMinVersionUnsupportParams(custins);
            if (CollectionUtils.isNotEmpty(versionUnsupportParamsList)) {
                for (int i=returnList.size()-1; i>=0; i--) {
                    if (null != returnList.get(i) &&
                            versionUnsupportParamsList.contains(returnList.get(i).get("ParameterName"))) {
                        returnList.remove(i);
                    }
                }
            }
            // 连接数参数特殊拦截
            CustInstanceDO custInstance = StringUtils.isNotEmpty(mysqlParaHelper.getParameterValue(DB_INSTANCE_NAME)) ? mysqlParaHelper.getAndCheckCustInstance() : null;
            if (DB_TYPE_MYSQL.equals(dbType) && null != custInstance) {
                if (!KIND_CODE_NEW_ARCH.equals(custInstance.getKindCode()) && mysqlParaHelper.isFilterMaxConn()) {
                    // 参数模板 or mysql kind_code = 1 暂时不展示连接数参数
                    mysqlParaHelper.filterMaxConnParams(returnList);
                } else {
                    InstanceLevelDO levelDO = instanceService.getInstanceLevelByLevelId(custInstance.getLevelId());
                    // serverless 暂时不展示连接数参数
                    if (null != levelDO && SERVERLESS_CATEGORY_TYPE.contains(levelDO.getCategory())) {
                        Set<String> filterParams = serverlessParamsSupport.getKernelParamsNoSupportEdit();
                        mysqlParaHelper.filterTemplteParams(returnList, filterParams);
                    }
                }
            }

            // 分析型只读实例参数特殊拦截
            if (DB_TYPE_MYSQL.equals(dbType) && null != custInstance) {
                boolean isAnalyticReadOnlyIns = false;
                try {
                    Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, custInstance.getInsName());
                    String isAnalyticReadOnlyInsValue = labels.get("isAnalyticReadOnlyIns");
                    isAnalyticReadOnlyIns = "true".equalsIgnoreCase(isAnalyticReadOnlyInsValue);
                } catch (Exception e) {
                    logger.warn("Failed to get instance labels for DuckDB parameter filtering", e);
                }

                if (isAnalyticReadOnlyIns) {
                    // 如果是分析型只读实例，只保留分析型相关的 14 个参数
                    mysqlParaHelper.filterAnalyticReadOnlyParams(returnList);
                    logger.info("Analytic read-only instance: filtered to keep only analytic parameters");
                } else {
                    // 如果不是分析型只读实例，过滤掉分析型相关的 14 个参数
                    mysqlParaHelper.filterNonAnalyticReadOnlyParams(returnList);
                    logger.info("Non-analytic read-only instance: filtered to remove analytic parameters");
                }
            }

            //输出结果
            Map<String, Object> data = new HashMap<>(5);
            data.put("Engine", dbType);
            data.put("EngineVersion", dbVersion);
            data.put("ParameterNumbers", returnList.size());
            data.put("Parameters", returnList);
            data.put("blackParamList", versionUnsupportParamsList);
            return data;

        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }
}
