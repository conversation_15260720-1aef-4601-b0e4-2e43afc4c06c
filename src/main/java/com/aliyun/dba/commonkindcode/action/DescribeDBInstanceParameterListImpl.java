package com.aliyun.dba.commonkindcode.action;

import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.idao.MysqlCustinsParamIDao;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamMinorVerionHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.commonkindcode.support.ParamRuntimeCoverHelper;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MycnfCustinstanceDO;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessParamsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SERVERLESS_CATEGORY_TYPE;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeDescribeDBInstanceParameterListImpl")
public class DescribeDBInstanceParameterListImpl implements IAction{

    private static final LogAgent logger = LogFactory.getLogAgent(DescribeDBInstanceParameterListImpl.class);

    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MycnfService mycnfService;
    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    private MysqlParamMinorVerionHelper mysqlParamMinorVerionHelper;
    @Autowired
    private ParamRuntimeCoverHelper paramRuntimeCoverHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    private ServerlessParamsSupport serverlessParamsSupport;

    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected CustinsParamGroupsService custinsParamGroupsService;

    @Autowired
    private MysqlCustinsParamIDao MysqlCustinsParamIDao;

    @Autowired
    protected DBaasMetaService dBaasMetaService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        try {

            //设置参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(actionParams);

            custins = mysqlParaHelper.getAndCheckCustInstance();
            Map<String, Object> data = new HashMap<>(10);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("Engine", custins.getDbType());
            data.put("EngineVersion", custins.getDbVersion());
            String regionId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REGION_ID);
            String requestId = mysqlParamSupport.getParameterValue(actionParams, ParamConstants.REQUEST_ID);


            List<String> blackParamList = new ArrayList<>();
            // 获取当前实例 小版本不兼容的参数
            List<String> versionUnsupportParamsList = mysqlParamMinorVerionHelper.getInstanceMinVersionUnsupportParams(custins);
            if (CollectionUtils.isNotEmpty(versionUnsupportParamsList)) {
                blackParamList.addAll(versionUnsupportParamsList);
            }
            data.put("blackParamList", blackParamList);

            //获取参数模板名称
            CustinsParamDO paramGroupIdDO = custinsParamService.getCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            Map<String,Object> paramGroupInfo = new HashMap<>();
            if (paramGroupIdDO != null && paramGroupIdDO.getValue() !=null) {
                String paramGroupId = paramGroupIdDO.getValue();
                List<Map<String, Object>> paramGroupList = MysqlCustinsParamIDao.getInsParamGroupByCondition(paramGroupId, custins.getDbType(), custins.getDbVersion());
                if (paramGroupList !=null && !paramGroupList.isEmpty()) {
                    Map<String, Object> map = paramGroupList.get(0);
                    paramGroupInfo.put("ParameterGroupName",map.get("ParameterGroupName"));
                    paramGroupInfo.put("ParameterGroupDesc",map.get("ParameterGroupDesc"));
                    paramGroupInfo.put("ParameterGroupType",map.get("ParameterGroupType"));
                    paramGroupInfo.put("ParamGroupId",map.get("ParamGroupId"));
                }
            }
            data.put("ParamGroupInfo",paramGroupInfo);

            if (custins.isMysqlLogic() && !custins.isCustinsDockerOnEcs()){

                data.put("Datas", new ArrayList<Map<String, Object>>());
                List<CustInstanceDO> uniqueCharacterCustinsList = new ArrayList<>();

                CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
                custInstanceQuery.setParentId(custins.getId());
                custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_PRIMARY);
                List<CustInstanceDO> characterCustinsList = custinsService.getCustIns(custInstanceQuery);

                for (CustInstanceDO characterCustins: characterCustinsList){
                    String characterType = characterCustins.getCharacterType();
                    String characterTypeCnf = characterType;
                    if (characterType.equals(CustinsSupport.CHARACTER_TYPE_MYSQL_DB)){
                        characterTypeCnf = CustinsSupport.CHARACTER_TYPE_NORMAL;
                    }
                    boolean uniqueFlag = false;
                    for (CustInstanceDO uniqueCustins: uniqueCharacterCustinsList){
                        if (uniqueCustins.getCharacterType().equals(characterType)){
                            uniqueFlag = true;
                            break;
                        }
                    }
                    if (!uniqueFlag){
                        uniqueCharacterCustinsList.add(characterCustins);
                        Map<String, Object> characterTypeMap = new HashMap<>();
                        characterTypeMap.put("CharacterType", characterType);
                        // runningParams
                        characterTypeMap.put("RunningParameters", instanceService.getMycnfCustinsRunningList(
                                characterCustins.getId(),
                                characterCustins.getDbType(),
                                characterCustins.getDbVersion(),
                                characterTypeCnf));

                        // configParams
                        Map<String, List<Map>> mycnfConfigParams = new HashMap<>();
                        characterTypeMap.put("ConfigParameters", instanceService.getMycnfCustinsConfigList(
                                characterCustins.getId(),
                                characterCustins.getDbType(),
                                characterCustins.getDbVersion(),
                                characterTypeCnf));
                        ((ArrayList<Map<String, Object>>)data.get("Datas")).add(characterTypeMap);
                    }
                }
            } else {

                //输出结果
                String characterType = custins.getCharacterType();
                if (custins.isMysql() && custins.isCustinsDockerOnEcs() || custins.isCustinsDockerOnEcsLocalSSD()){
//                if ((custins.isMysql() || custins.isMariaDB()) && custins.isCustinsDockerOnEcs() || custins.isCustinsDockerOnEcsLocalSSD()){
                    characterType = CustinsSupport.CHARACTER_TYPE_NORMAL;
                }

                //实例参数是从mycnf_custinstance和mycnf_template查询出来的交集，还需要比较mycnf_template_extra中用户可见的参数，如果不存在，则加上
                List<Map<String,Object>> runningParameters = ParamTransHelper.getMapListFromMycnfRunningList(
                        instanceService.getMycnfCustinsRunningList(
                                custins.getId(),
                                custins.getDbType(),
                                mysqlParaHelper.getCfgDbVersion(custins),
                                characterType));

                List<Map<String,Object>> configParameters = ParamTransHelper.getMapListFromMycnfCustinsConfigList(
                        instanceService.getMycnfCustinsConfigList(
                                custins.getId(),
                                custins.getDbType(),
                                mysqlParaHelper.getCfgDbVersion(custins),
                                characterType));


                if(isMysql(custins.getDbType())){
                    boolean useMycnfTemplateExtra = false;

                    InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                    String category = instanceLevelDO.getCategory();
                    InstanceLevelDO effectiveLevel = instanceLevelDO;

                    if(useMycnfTemplateExtra(custins.getDbType(), custins.getDbVersion(), instanceLevelDO.getCategory())){
                        useMycnfTemplateExtra = true;
                    }
                    //XDB只读
                    else if(custins.getInsType() == 3 && custins.getPrimaryCustinsId() != 0){
                        CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                        if(primaryCustins != null){
                            InstanceLevelDO primaryLevel = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
                            effectiveLevel = primaryLevel;
                            if(useMycnfTemplateExtra(custins.getDbType(), custins.getDbVersion(), primaryLevel.getCategory())){
                                useMycnfTemplateExtra = true;
                                category = primaryLevel.getCategory();
                            }
                        }
                    }

                    //使用mycnf_template_extra表
                    if(useMycnfTemplateExtra){

                        runningParameters = ParamTransHelper.getMapListFromMycnfRunningList(
                                instanceService.getMycnfCustinsRunningListWithMycnfTemplateExtra(
                                        custins.getId(),
                                        custins.getDbType(),
                                        mysqlParaHelper.getCfgDbVersion(custins),
                                        category,
                                        characterType));
                        configParameters = ParamTransHelper.getMapListFromMycnfCustinsConfigList(
                                instanceService.getMycnfCustinsConfigListWithMycnfTemplateExtra(
                                        custins.getId(),
                                        custins.getDbType(),
                                        mysqlParaHelper.getCfgDbVersion(custins),
                                        category,
                                        characterType));
                    }
                    // 用运行态参数值 覆盖 元数据快照参数值
                    if (paramRuntimeCoverHelper.paramCoverCheck(custins, effectiveLevel, regionId)) {
                        runningParameters = paramRuntimeCoverHelper.coverRuntimeParam(custins, runningParameters,
                                instanceService.getMycnfTemplateMap(custins.getDbType(), custins.getDbVersion(), characterType)
                                , requestId);
                    }
                }
                // 过滤掉小版本不兼容的参数列表
                if (CollectionUtils.isNotEmpty(blackParamList)) {
                    for (int i=runningParameters.size()-1; i>=0; i--) {
                        if (null != runningParameters.get(i)
                                && blackParamList.contains(runningParameters.get(i).get("ParameterName"))) {
                            runningParameters.remove(i);
                        }
                    }
                    for (int i=configParameters.size()-1; i>=0; i--) {
                        if (null != configParameters.get(i)
                                && blackParamList.contains(configParameters.get(i).get("ParameterName"))) {
                            configParameters.remove(i);
                        }
                    }
                }

                // 连接数参数特殊拦截
                if (custins.isMysql()) {
                    if (!KIND_CODE_NEW_ARCH.equals(custins.getKindCode()) && mysqlParaHelper.isFilterMaxConn()) {
                        // mysql kind_code!=18 暂时不展示连接数参数
                        mysqlParaHelper.filterMaxConnParams(runningParameters);
                        mysqlParaHelper.filterMaxConnParams(configParameters);
                    } else {
                        InstanceLevelDO levelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                        // serverless 暂时不展示连接数参数
                        if (null != levelDO && SERVERLESS_CATEGORY_TYPE.contains(levelDO.getCategory())) {
                            Set<String> filterParams = serverlessParamsSupport.getKernelParamsNoSupportEdit();
                            mysqlParaHelper.filterTemplteParams(runningParameters, filterParams);
                            mysqlParaHelper.filterTemplteParams(configParameters, filterParams);

                        }

                        //内核侧提的集群版本不开放这两个参数
                        if (null != levelDO && (MysqlParamSupport.isCluster(levelDO.getCategory())) ){
                            Set<String> needIgnoreSet = new HashSet<>(Arrays.asList("sync_binlog", "innodb_flush_log_at_trx_commit"));
                            mysqlParaHelper.filterTemplteParams(runningParameters, needIgnoreSet);
                            mysqlParaHelper.filterTemplteParams(configParameters, needIgnoreSet);
                        }
                    }
                }

                // 分析型只读实例参数处理 - 合并 DuckDB 参数并排序
                if (custins.isMysql()) {
                    try {
                        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, custins.getInsName());
                        String isAnalyticReadOnlyInsValue = labels.get("isAnalyticReadOnlyIns");
                        boolean isAnalyticReadOnlyIns = StringUtils.isNotBlank(isAnalyticReadOnlyInsValue) &&
                            ("true".equalsIgnoreCase(isAnalyticReadOnlyInsValue) || "TRUE".equals(isAnalyticReadOnlyInsValue));

                        if (isAnalyticReadOnlyIns) {
                            // 如果是分析型只读实例，需要额外查询 DuckDB 参数并合并
                            InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
                            String category = instanceLevelDO.getCategory();

                            // 查询包含 DuckDB 参数的完整参数列表
                            List<Map<String,Object>> duckdbParameters = ParamTransHelper.getMapListFromMycnfRunningList(
                                instanceService.getMycnfCustinsRunningListWithMycnfTemplateExtra(
                                    custins.getId(),
                                    custins.getDbType(),
                                    mysqlParaHelper.getCfgDbVersion(custins),
                                    category,
                                    characterType));

                            // 合并 DuckDB 参数到现有参数列表中
                            mysqlParaHelper.mergeDuckDBParams(runningParameters, duckdbParameters);

                            // 将 DuckDB 参数排在最前面
                            mysqlParaHelper.sortAnalyticReadOnlyParams(runningParameters);
                            logger.info("Analytic read-only instance: merged and sorted DuckDB parameters to front");
                        }
                    } catch (Exception e) {
                        logger.warn("Failed to get instance labels for analytic read-only parameter processing, skip processing: " + e.getMessage());
                        // 异常时跳过分析型只读的特殊处理
                    }
                }

                data.put("RunningParameters", runningParameters);
                // 取出所有mycnf_change_log中未应用的参数
                data.put("ConfigParameters", configParameters);

                if (custins.isCustinsOnDocker()) {
                    List<MycnfCustinstanceDO> externalMycnfList = mycnfService.getExternalMycnfCustinstances(
                            custins.getId(), custins.getDbType(), custins.getDbVersion());
                    List<Map<String, Object>> externalParameters = new ArrayList<>(externalMycnfList.size());
                    for (MycnfCustinstanceDO cnf : externalMycnfList) {
                        Map<String, Object> param = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(blackParamList) && blackParamList.contains(cnf.getName())) {
                            continue;
                        }
                        param.put("ParameterName", cnf.getName());
                        param.put("ParameterValue", cnf.getParaValue());
                        param.put("Effective", 0); // 0 : 需要重启实例才能生效， 1： 立即生效
                        param.put("CheckingCode", "");
                        param.put("Unit", "STRING");
                        param.put("Factor", 0);
                        param.put("ParameterDescription", "");
                        externalParameters.add(param);
                    }
                    data.put("ExternalParameters", externalParameters);
                }
            }
            return data;
        } catch (RdsException re) {
            logger.error(re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }

    }
}
