package com.aliyun.dba.commonkindcode.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.model.Host;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCode;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.DB_TYPE_MYSQL;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("commonkindcodeSwitchOverHostInstances")
@Slf4j
public class SwitchOverHostInstancesImpl implements IAction {
    protected static final LogAgent logger = LogFactory.getLogAgent(SwitchOverHostInstancesImpl.class);
    @Resource
    private HostIDao hostIDao;
    @Resource
    private CustinsService custinsService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;


    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custInstanceDO, Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String hostName = mysqlParamSupport.getParameterValue(params, ParamConstants.HOST_NAME);
        String actionReason = mysqlParamSupport.getParameterValue(params, "ActionReason", "");
        try {
            if (StringUtils.isBlank(hostName)) {
                throw new RdsException(ErrorCode.INVALID_HOST_ID);
            }
            Host host = dBaasMetaService.getDefaultClient().getHost(requestId, hostName, false);
            Map<String, Object> data = new HashMap<>();
            data.put(ParamConstants.HOST_ID, host.getId());
            data.put(ParamConstants.HOST_NAME, host.getHostName());
            List<Map<String, Object>> custinsList = hostIDao.getCustinsListByHostId(host.getId());
            boolean ignoreFlag = true;
            for (Map<String, Object> item : custinsList) {
                Object dbType = item.get("Engine");
                if (dbType != null && DB_TYPE_MYSQL.equalsIgnoreCase(String.valueOf(dbType))) {
                    Integer custinsId = Integer.parseInt(String.valueOf(item.get("DBInstanceID")));
                    CustInstanceDO custins = custinsService.getCustInstanceByCustinsId(custinsId);
                    if (custins.getKindCode() != KindCode.poddefault.getCode().intValue()) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
                    }
                    ignoreFlag = false;
                    break;
                }
            }
            if (ignoreFlag) {
                data.put("TaskId", -1);
                return data;
            }
            Date switchTime = mysqlParamSupport.getAndCheckSwitchTime(params);
            String switchMode = mysqlParamSupport.getAndCheckSwitchTimeMode(params, ParamConstants.SWITCH_TIME_MODE, switchTime, false);
            JSONObject taskParam = new JSONObject();
            taskParam.put("crash_host_name", host.getHostName());
            taskParam.put("switch_reason", actionReason);
            Map<String, Object> switchInfoMap = PodCommonSupport.getSwitchInfoTime(switchMode, switchTime);
            taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            Object taskId = workFlowService.dispatchTask("host", host.getHostName(),
                    PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_SWITCHOVER_HOST_INSTANCES, taskParam.toJSONString(), 0);
            data.put("TaskId", NumberUtils.isNumber(taskId.toString()) ? Double.valueOf(taskId.toString()).longValue() : taskId);
            return data;
        } catch (RdsException re) {
            log.warn(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            log.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}
