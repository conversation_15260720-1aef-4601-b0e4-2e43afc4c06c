package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.PerformanceLevelEnum;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.LABEL_PARAM_SSL_ENABLE;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.LABEL_SCALE_UPGRADE_FOR_V2;
import static com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServerlessUpgradeService extends ServerlessModifyDBInstanceService {
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;
    @Resource
    protected ResourceService resourceService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    public Map<String, Object> upgradeV1ToV2(Map<String, String> params, JSONObject taskParams) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        boolean isSuccess = false;
        boolean isAllocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);

            ServerlessSpec serverlessSpec = serverlessResourceService.getServerlessSpec(requestId, dbInstanceName);
            if (serverlessSpec == null) {
                log.error("can not find serverless spc");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            Double rcu = serverlessSpec.getScaleMax();

            params.put("rcu", rcu.toString());
            val modifyInsParam = initPodModifyInsParam(params);
            ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();

            if (!serverlessResourceService.isServerlessBasic(replicaSet) || !serverlessResourceService.isServerlessV1(replicaSet)) {
                log.error("replicaSet is not v1, not support!");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            Integer maxScaleId = serverlessResourceService.getMaxScaleID(requestId, replicaSet.getName());
            if (maxScaleId != null) {
                log.error("replicaSet has maxscale, not support!");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            List<String> vpcIdAndVswitchId = getVpcAndVswitchId(requestId, replicaSet.getName());

            // 申请临时实例
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
            isAllocated = result.isAllocated();
            resourceRequest = result.getResourceRequest();

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSet.getId().toString(),
                    RCU, rcu.toString()
            );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, resourceRequest.getReplicaSetName(), labels);

            // 下发任务
            taskParams.put(CustinsSupport.TRANS_ID, result.getTransList().getId());
            taskParams.put("srcReplicaSetName", replicaSet.getName());
            taskParams.put("destReplicaSetName", resourceRequest.getReplicaSetName());

            // 创建maxscale 所需参数
            taskParams.put("vpcId", vpcIdAndVswitchId.get(0));
            taskParams.put("vSwitchId", vpcIdAndVswitchId.get(1));

            // v2 启停所需参数
            ServerlessSpec defaultSpec = new ServerlessSpec();
            taskParams.put(ServerlessConstant.SCALE_MIN, serverlessSpec.getScaleMin());
            taskParams.put(ServerlessConstant.SCALE_MAX, serverlessSpec.getScaleMax());
            taskParams.put(ServerlessConstant.AUTO_PAUSE, defaultSpec.getAutoPause());
            taskParams.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, defaultSpec.getSecondsUntilAutoPause());
            taskParams.put(ServerlessConstant.DAS_AUTO_PAUSE, defaultSpec.getDasAutoPause());
            taskParams.put(ServerlessConstant.KEEP_RUNNING_TIME, defaultSpec.getKeepRunningTime());

            String taskParamString = taskParams.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = ServerlessConstant.TASK_UPGRADE_SERVERLESS_V1_TO_V2;
            Object taskId = workFlowService.dispatchTask(
                    "custins", dbInstanceName, domain, taskKey, taskParamString, 0);

            // 增加标签，升级期间可以弹升
            Map<String, String> scaleLabels = ImmutableMap.of(LABEL_SCALE_UPGRADE_FOR_V2, "true");
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(), scaleLabels);
            // 更新实例状态为小版本升级中
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    requestId, replicaSet.getName(), ReplicaSet.StatusEnum.VERSION_TRANSING.toString());

            isSuccess = true;

            // 返回值
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (isAllocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error(requestId + " release resource for upgrade to serverless v2 failed: " + e.getMessage(), e);
                }
            }
        }
    }

    public boolean isProvisionToServerless(ReplicaSet replicaSet, String targetCategory) {
        boolean basicMigrate = CategoryEnum.BASIC.toString().equals(replicaSet.getCategory()) &&
                CategoryEnum.SERVERLESS_BASIC.toString().equals(targetCategory);
        boolean standardMigrate = CategoryEnum.STANDARD.toString().equals(replicaSet.getCategory()) &&
                CategoryEnum.SERVERLESS_STANDARD.toString().equals(targetCategory);
        return basicMigrate || standardMigrate;
    }


    public String getTargetServiceSpecTag(PodModifyInsParam modifyInsParam) throws RdsException {
        ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();
        return minorVersionServiceHelper.tryGetServiceSpecTag(
                modifyInsParam.getSrcMinorVersion(),
                replicaSet.getBizType(),
                replicaSet.getService(),
                replicaSet.getServiceVersion(),
                "MySQL",
                KIND_CODE_NEW_ARCH,
                modifyInsParam.getTargetInstanceLevel(),
                modifyInsParam.getTargetDiskType(),
                modifyInsParam.isDHG(),
                false);
    }


    public Map<String, Object> migrateProvisionToServerless(Map<String, String> params) throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        if (!enableProvisionServerlessMigrate()) {
            log.error("ENABLE_MYSQL_PROVISION_SERVERLESS_MIGRATE is configured to false, can not migrate!");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }

        ReplicaSet replicaSet = null;
        boolean isSuccess = false;
        boolean allocated = false;
        boolean maxscaleAllocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);

            val modifyInsParam = initServerlessSccModifyInsParam(params);
            replicaSet = modifyInsParam.getReplicaSetMeta();

            // 过滤非主实例
            if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType())) {
                log.error("replicaset type is {}, only support main!", replicaSet.getInsType());
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 仅支持新架构基础版升级至serverless 基础版
            String targetCategory = modifyInsParam.getTargetInstanceLevel().getCategory().toString();
            if (!isProvisionToServerless(replicaSet, targetCategory)) {
                log.error("src level {}, target level {}, not support migrate!", replicaSet.getCategory(), targetCategory);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 不支持arm 实例升级
            if (PodCommonSupport.isArm(modifyInsParam.getSrcInstanceLevel())) {
                log.error("replicaSet is arm, not support migrate to serverless");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            Map<String, String> replicaSetLabels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, dbInstanceName);
            // Serverless不支持XEngine引擎
            if (dependency.getPodCommonSupport().isXEngine(replicaSetLabels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_STORAGE_ENGINE);
            }

            if ("1".equalsIgnoreCase(replicaSetLabels.get(LABEL_PARAM_SSL_ENABLE))) {
                log.error("replicaSet ssl is enabled, not support!"); //注意无论是云证书还是自定义证书开启的SSL，都暂不支持
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 不支持已有代理实例升级
            Integer maxScaleId = serverlessResourceService.getMaxScaleID(requestId, replicaSet.getName());
            if (maxScaleId != null) {
                log.error("replicaSet has maxscale, not support!");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 不支持带只读实例升级
            List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId,
                    replicaSet.getName(), ReplicaSet.InsTypeEnum.READONLY.toString()).getItems();
            if (CollectionUtils.isNotEmpty(readOnlyReplicaSetList)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE, "current instance has read ins, not supported!");
            }

            // only support cloud_auto and essd_pl1
            if (!isStorageSupportToServerless(modifyInsParam)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 检查serverless 是否支持当前小版本
            String requiredMinorVersion = ServerlessConstant.MINOR_VERSION_SERVERLESS_SUPPORTED.getOrDefault(modifyInsParam.getDbVersion(), "99");
            if (requiredMinorVersion.compareTo(modifyInsParam.getSrcMinorVersion()) > 0) {
                log.error("replicaSet version is {}, serverless require version > {}, please upgrade first!", modifyInsParam.getSrcMinorVersion(), requiredMinorVersion);
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }

            String serviceSpecTag = getTargetServiceSpecTag(modifyInsParam);
            int serviceSpecId = minorVersionServiceHelper.checkAndGetServiceSpecId(requestId, modifyInsParam.getSrcMinorVersion(),
                    replicaSet.getService(), replicaSet.getServiceVersion(), serviceSpecTag, modifyInsParam.getTargetInstanceLevel().getCategory().toString());
            log.info("target serviceSpecTag {}, target serviceSpecId {}", serviceSpecId, serviceSpecTag);

            List<String> vpcIdAndVswitchId = getVpcAndVswitchId(requestId, replicaSet.getName());
            String vpcId = vpcIdAndVswitchId.get(0);
            String vswitchId = vpcIdAndVswitchId.get(1);

            // 申请临时实例
            modifyInsParam.setRcu(modifyInsParam.getTargetServerlessSpec().getScaleMax());
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
            allocated = true;
            resourceRequest = result.getResourceRequest();
            ReplicaSet tmpReplicaSet = result.getReplicaSet();

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSet.getId().toString(),
                    RCU, modifyInsParam.getRcu().toString()
            );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSet.getName(), labels);
            // 下发任务
            JSONObject taskParams = new JSONObject();
            if (modifyInsParam.getPodType() != PodType.POD_ECS_RUND) {
                // 申请 maxscale
                maxscaleAllocated = true;
                Long maxScaleTaskId = createMaxScaleIns(params, tmpReplicaSet.getName(), vpcId, vswitchId);
                taskParams.put("maxScaleTaskId", maxScaleTaskId);
            }
            taskParams.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParams.put(CustinsSupport.TRANS_ID, result.getTransList().getId());
            taskParams.put("srcReplicaSetName", replicaSet.getName());
            taskParams.put("destReplicaSetName", resourceRequest.getReplicaSetName());
            taskParams.put("destClassCode", modifyInsParam.getTargetClassCode());
            taskParams.put("serviceSpecId", serviceSpecId);

            // serverless 参数
            taskParams.put(ServerlessConstant.SCALE_MIN, modifyInsParam.getTargetServerlessSpec().getScaleMin());
            taskParams.put(ServerlessConstant.SCALE_MAX, modifyInsParam.getTargetServerlessSpec().getScaleMax());
            taskParams.put(ServerlessConstant.AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getAutoPause());
            taskParams.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getSecondsUntilAutoPause());
            taskParams.put(ServerlessConstant.DAS_AUTO_PAUSE, modifyInsParam.getTargetServerlessSpec().getDasAutoPause());
            taskParams.put(ServerlessConstant.KEEP_RUNNING_TIME, modifyInsParam.getTargetServerlessSpec().getKeepRunningTime());
            taskParams.put(ServerlessConstant.SWITCH_FORCE, modifyInsParam.getTargetServerlessSpec().getSwitchForce());
            taskParams.put("storageAutoScale", ServerlessConstant.SERVERLESS_STORAGE_AUTO_SCALE);
            taskParams.put("storageUpperBound", ServerlessConstant.SERVERLESS_STORAGE_UPPER_BOND);
            taskParams.put("storageThreshold", ServerlessConstant.SERVERLESS_STORAGE_THRESHOLD);
            taskParams.put("orderId", modifyInsParam.getOrderId());


            String taskParamString = taskParams.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = ServerlessConstant.TASK_MIGRATE_PROVISION_TO_SERVERLESS;
            Object taskId = workFlowService.dispatchTask(
                    "custins", dbInstanceName, domain, taskKey, taskParamString, 0);

            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    requestId, replicaSet.getName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());

            isSuccess = true;

            // 返回值
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocated && !isSuccess && maxscaleAllocated) {
                try {
                    deleteMaxscaleIns(params, resourceRequest.getReplicaSetName());
                } catch (Exception e) {
                    if (!e.getMessage().contains("not found")) {
                        logger.error("release maxscale for migrate to serverless failed: {}", e);
                    }
                }
            }

            if (allocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error("release resource for migrate to serverless failed: {}", e);
                }
            }
        }
    }


    public boolean isServerlessToProvision(ReplicaSet replicaSet, String targetCategory) {
        boolean basicMigrate = CategoryEnum.SERVERLESS_BASIC.toString().equals(replicaSet.getCategory()) &&
                CategoryEnum.BASIC.toString().equals(targetCategory);
        boolean standardMigrate = CategoryEnum.SERVERLESS_STANDARD.toString().equals(replicaSet.getCategory()) &&
                CategoryEnum.STANDARD.toString().equals(targetCategory);
        return basicMigrate || standardMigrate;
    }

    public boolean isStorageSupportToServerless(PodModifyInsParam modifyInsParam) {
        if (StringUtils.equalsIgnoreCase(PerformanceLevelEnum.PL1.getValue(), modifyInsParam.getSrcPerformanceLevel())) {
            return true;
        }

        // serverless support cloud_auto, but not support io acceleration
        if (StringUtils.equalsIgnoreCase(Replica.StorageTypeEnum.CLOUD_AUTO.getValue(), modifyInsParam.getSrcDiskType())) {
            if (modifyInsParam.isSrcIoAccelerationEnabled() || modifyInsParam.isSrcColdDataEnabled()) {
                log.error("replicaSet disk ioAcceleration is {}, coldData is {}, not support!", modifyInsParam.isSrcIoAccelerationEnabled(), modifyInsParam.isSrcColdDataEnabled());
                return false;
            }
            return true;
        }
        log.error("replicaSet disk type is {}, pl is {}, not support!", modifyInsParam.getSrcDiskType(), modifyInsParam.getSrcPerformanceLevel());
        return false;
    }

    public Map<String, Object> migrateServerlessToProvision(Map<String, String> params)  throws RdsException {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        if (!enableProvisionServerlessMigrate()) {
            log.error("ENABLE_MYSQL_PROVISION_SERVERLESS_MIGRATE is configured to false, can not migrate!");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }

        ReplicaSet replicaSet = null;
        boolean isSuccess = false;
        boolean allocated = false;

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();

        try {
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);

            val modifyInsParam = initServerlessSccModifyInsParam(params);
            replicaSet = modifyInsParam.getReplicaSetMeta();

            // 仅支持新架构基础版升级至serverless 基础版
            String targetCategory = modifyInsParam.getTargetInstanceLevel().getCategory().toString();
            if (!isServerlessToProvision(replicaSet, targetCategory)) {
                log.error("src level {}, target level {}, not support migrate!", replicaSet.getCategory(), targetCategory);
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // 不支持arm 实例升级
            if (PodCommonSupport.isArm(modifyInsParam.getSrcInstanceLevel())) {
                log.error("replicaSet is arm, not support migrate");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            String maxscaleName = serverlessResourceService.getMaxScaleName(requestId, dbInstanceName);
            String sslLabel = maxscaleName != null ? dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, maxscaleName, LABEL_PARAM_SSL_ENABLE) : null;
            if ("1".equalsIgnoreCase(sslLabel)) {
                log.error("replicaSet ssl is enabled, not support migrate");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }

            // check service_spec for target category
            String serviceSpecTag = getTargetServiceSpecTag(modifyInsParam);
            int serviceSpecId = minorVersionServiceHelper.checkAndGetServiceSpecId(requestId, modifyInsParam.getSrcMinorVersion(),
                    replicaSet.getService(), replicaSet.getServiceVersion(), serviceSpecTag, modifyInsParam.getTargetInstanceLevel().getCategory().toString());
            log.info("target serviceSpecTag {}, target serviceSpecId {}", serviceSpecId, serviceSpecTag);

            // 申请临时实例
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
            allocated = true;
            resourceRequest = result.getResourceRequest();
            ReplicaSet tmpReplicaSet = result.getReplicaSet();

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSet.getId().toString()
            );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, tmpReplicaSet.getName(), labels);

            // 下发任务
            JSONObject taskParams = new JSONObject();
            taskParams.put("switchInfo", modifyInsParam.getSwitchInfo());
            taskParams.put(CustinsSupport.TRANS_ID, result.getTransList().getId());
            taskParams.put("srcReplicaSetName", replicaSet.getName());
            taskParams.put("destReplicaSetName", resourceRequest.getReplicaSetName());
            taskParams.put("destClassCode", modifyInsParam.getTargetClassCode());
            taskParams.put("orderId", modifyInsParam.getOrderId());
            taskParams.put("serviceSpecId", serviceSpecId);

            String taskParamString = taskParams.toJSONString();
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            String taskKey = ServerlessConstant.TASK_MIGRATE_SERVERLESS_TO_PROVISION;
            Object taskId = workFlowService.dispatchTask(
                    "custins", dbInstanceName, domain, taskKey, taskParamString, 0);

            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(
                    requestId, replicaSet.getName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());
            isSuccess = true;

            // 返回值
            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put(ParamConstants.DB_INSTANCE_NAME, replicaSet.getName());
            data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
            return data;

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocated && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    logger.error("release resource for migrate to provision failed: {}", e);
                }
            }
        }
    }


    public Boolean enableUpgradeMinorVersion() {
        ResourceDO resource = resourceService.getResourceByResKey(ServerlessConstant.ENABLE_MYSQL_SERVERLESS_UPGRADE_MINOR_VERSION);
        // set default to support upgrade
        if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
            return true;
        } else {
            return StringUtils.equalsIgnoreCase(resource.getRealValue(), "on") || StringUtils.equals(resource.getRealValue(), "1");
        }
    }

    public Boolean enableUpgradeV1ToV2() {
        ResourceDO resource = resourceService.getResourceByResKey(ServerlessConstant.ENABLE_MYSQL_SERVERLESS_UPGRADE_V1_TO_V2);
        if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
            return true;
        } else {
            return StringUtils.equalsIgnoreCase(resource.getRealValue(), "on") || StringUtils.equals(resource.getRealValue(), "1");
        }
    }

    public Boolean enableProvisionServerlessMigrate() {
        ResourceDO resource = resourceService.getResourceByResKey(ServerlessConstant.ENABLE_MYSQL_PROVISION_SERVERLESS_MIGRATE);
        if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
            return true;
        } else {
            return StringUtils.equalsIgnoreCase(resource.getRealValue(), "on") || StringUtils.equals(resource.getRealValue(), "1");
        }
    }

    private List<String> getVpcAndVswitchId(String requestId, String replicaSetName) throws RdsException, ApiException {
        // 查询实例连接信息
        EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSetName, null, null, null, null);
        if (CollectionUtils.isEmpty(endpointListResult.getItems())) {
            log.error("can not find replicaSet's endpoint");
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        List<Endpoint> vpcEndpointList = endpointListResult.getItems().stream()
                .filter(v -> (v.getNetType() == Endpoint.NetTypeEnum.VPC && v.getType() == Endpoint.TypeEnum.NORMAL))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vpcEndpointList) || vpcEndpointList.size() > 1) {
            log.error("replicaSet has {} vpc endpoint: {}", vpcEndpointList.size(), vpcEndpointList);
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
        Endpoint vpcEndPoint = vpcEndpointList.get(0);
        String vpcId = vpcEndPoint.getVpcId();


        IpResourceDO ipResource = resourceService.getIpResourceByIpAndVpc(vpcEndPoint.getVip(), vpcId);
        String vSwitchId = ipResource.getvSwitchId();

        return Arrays.asList(vpcId, vSwitchId);
    }


    /**
     * 创建Serverless MaxScale实例
     * */
    public Long createMaxScaleIns(Map<String, String> params, String dbInstanceName, String vpcId, String vSwitchId) throws Exception {
        String connStr = String.format("%s", dbInstanceName);
        Map<String, String> proxyParam = serverlessResourceService.getProxyParam(params);
        proxyParam.put(ParamConstants.ACTION, "CreateDBProxy");
        proxyParam.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
        proxyParam.put("ProxyVPCId", vpcId);
        proxyParam.put("ProxyVSwitchId", vSwitchId);
        proxyParam.put("ConnectionString", connStr);
        // Create 2 core maxscale proxy for serverless
        proxyParam.put("SliceNum", "2");

        Map<String, Object> result = serverlessResourceService.invokeProxyApi(proxyParam);
        logger.info("MaxScale Response {}", result);

        Map taskInfo = JSON.parseObject(JSON.toJSONString(result.get("Data")), Map.class);
        return Long.parseLong(taskInfo.get("TaskId").toString());
    }


    public void deleteMaxscaleIns(Map<String, String> params, String dbInstanceName) throws RdsException {
        Map<String, String> proxyParam = serverlessResourceService.getProxyParam(params);
        proxyParam.put(ParamConstants.ACTION, "DeleteDBProxy");
        proxyParam.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);

        Map<String, Object> result = serverlessResourceService.invokeProxyApi(proxyParam);
        logger.info("MaxScale Response {}", result);
    }

}
