package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.CreateReplicaSetDto;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet.BizTypeEnum;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.service.MysqlEngineCheckService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.service.ServerlessUpgradeService;
import com.aliyun.dba.serverless.action.service.StoppedServerlessMaintainService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;

@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessUpgradeDBVersionImpl")
public class UpgradeDBVersionImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(UpgradeDBVersionImpl.class);
    @Resource
    protected MysqlParamSupport paramSupport;
    @Resource
    protected DBaasMetaService metaService;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private MySQLServiceImpl mySQLService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private KmsService kmsService;
    @Resource
    private MysqlEngineCheckService mysqlEngineCheckService;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    private ServerlessUpgradeService serverlessUpgradeService;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private AllocateTmpResourceService allocateTmpResourceService;
    @Resource
    private StoppedServerlessMaintainService stoppedServerlessMaintainService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String engineType =paramSupport.getParameterValue(params,"engineType");

        /**
        engineType为maxscale,对maxscale进行版本升级
         */
        if("maxscale".equalsIgnoreCase(engineType)){
            try{
                ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
                Map<String, Object> proxyApiData=serverlessResourceService.upgradeMaxscaleDBVersion(replicaSet,requestId,params);
                return JSON.parseObject(JSON.toJSONString(proxyApiData.get("Data")), Map.class);
            }catch (ApiException re){
                logger.error(requestId + " MetaApi error: ", re);
                throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
            }catch (Exception e) {
                logger.error("{} upgrade maxscale minorversion failed. Exception: {}", requestId, e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
        }

        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            if (!replicaSetService.isActive(replicaSet) && !ReplicaSet.StatusEnum.STOPPED.equals(replicaSet.getStatus())) {
                return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }
            String minorVersion = paramSupport.getParameterValue(params, "MinorVersion");
            if (StringUtils.isEmpty(minorVersion)) {
                minorVersion = paramSupport.getParameterValue(params, "TargetMinorVersion");
            }
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

            BizTypeEnum bizType = replicaSet.getBizType();

            JSONObject taskParam = new JSONObject();
            taskParam.put("requestId", requestId);
            taskParam.put(CustinsSupport.SWITCH_KEY, switchInfoMap);

            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
            boolean isArm =  CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(labels.get("instructionSetArch"));

            ReplicaSet primaryIns = getPrimaryReplicaSet(requestId, replicaSet).getRight();
            taskParam.put("primaryReplicaSetName", primaryIns.getName());
            String dbType = replicaSet.getService();
            String dbVersion = replicaSet.getServiceVersion();
            String classCode = primaryIns.getClassCode();
            String dbEngine = "MySQL";
            String diskType = replicaSetService.getReplicaSetStorageType(replicaSet.getName(), requestId);
            String clusterName = replicaSet.getResourceGroupName();
            Boolean isDhg = paramSupport.isDHGCluster(clusterName);
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType,
                    dbVersion, classCode, null);
            logger.info(MessageFormat.format(
                    "dbEngine:{0},dbType:{1},dbVersion:{2},classCode:{3},diskType:{4},clusterName:{5},isDhg:{6},"
                            + "instanceLevel:{7}",
                    dbEngine, dbType, dbVersion, classCode, diskType, clusterName, isDhg, instanceLevel));

            String serviceSpecTag;
            serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(replicaSet.getName(),
                    minorVersion, bizType, dbType,
                    dbVersion, dbEngine, KindCodeParser.KIND_CODE_NEW_ARCH, instanceLevel, diskType, isDhg, null);
            taskParam.put("serviceSpecTag", serviceSpecTag);
            logger.info("serviceSpecTag:{}", serviceSpecTag);

            int serviceSpecId = minorVersionServiceHelper.checkAndGetServiceSpecId(requestId, minorVersion, dbType,
                    dbVersion, serviceSpecTag, instanceLevel.getCategory().toString());
            taskParam.put("serviceSpecId", serviceSpecId);
            logger.info("serviceSpecId:{}", serviceSpecId);

            String expectedReleaseDate = minorVersionServiceHelper.checkAndGetReleaseDate(requestId, replicaSet,
                    primaryIns.getCategory(), serviceSpecTag, minorVersion);
            taskParam.put("releaseDate", expectedReleaseDate);
            logger.info("expectedReleaseDate:{}", expectedReleaseDate);
            logger.info("taskParam:{}", taskParam);

            //serverless小版本升级任务流中会去自动升级maxscale到最新内核版本当中，无需在api层校验maxscale内核小版本
//            if (!mysqlEngineCheckService.checkCanUpgradeMinorVersionWithMaxScale(custins, expectedReleaseDate)) {
//                //从rdsapi下来的请求会将custins对象传入
//                return ResponseSupport.createErrorResponse(new Object[]{ResultCode.CODE_UNSUPPORTED, "MaxscaleMinorVersionNotSupport", "The Maxscale version used by the instance is too low, please upgrade the Maxscale version first."});
//            }

            taskParam.put("minor_version", minorVersion);

            boolean isPrimary = ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType());
            boolean isBasicCategory = InstanceLevel.CategoryEnum.BASIC.toString().equalsIgnoreCase(replicaSet.getCategory());


            if (bizType == BizTypeEnum.ALIYUN && isPrimary && !isBasicCategory) {
                // Passthough switch time params into workflow for upgrading attached read ins
                // These params should NEVER be used directly in workflow

                taskParam.put("switch_time_mode", paramSupport.getParameterValue(params, ParamConstants.SWITCH_TIME_MODE));
                taskParam.put("switch_time", paramSupport.getParameterValue(params, ParamConstants.SWITCH_TIME));
            }

            if (serverlessResourceService.isServerlessV1(replicaSet) && serverlessUpgradeService.enableUpgradeV1ToV2()) {
                logger.info("replicaSet is serverless v1, so upgrade to v2");
                return serverlessUpgradeService.upgradeV1ToV2(params, taskParam);
            }

            if (!serverlessUpgradeService.enableUpgradeMinorVersion()) {
                logger.error("serverless upgrade minor version not enabled");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            Object taskId = doUpgradeDBVersion(params, taskParam);

            // 更新实例状态为小版本升级中
            metaService.getDefaultClient().updateReplicaSetStatus(
                    requestId, replicaSet.getName(), ReplicaSet.StatusEnum.MINOR_VERSION_TRANSING.toString());

            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TargetMinorVersion", minorVersion);
            data.put("TaskId", taskId);
            return data;
        } catch (RdsException re) {
            logger.error(re.getMessage(), re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "RequestMetaDataFailed", "Request Meta API failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 获取主实例
     */
    public Pair<String, ReplicaSet> getPrimaryReplicaSet(String requestId, ReplicaSet replicaSet) throws ApiException {
        ReplicaSet primaryReplicaSet = replicaSet;
        if (ReplicaSet.InsTypeEnum.TMP.equals(replicaSet.getInsType())) {
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
        }

        Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, primaryReplicaSet.getName());
        String centerReplicaSetName = labels.get("CenterReplicaSetName");
        String centerRegionId = labels.get("CenterRegionId");
        if (StringUtils.isNotEmpty(centerReplicaSetName)) {
            // 跨region只读
            primaryReplicaSet = dBaasMetaService.getRegionClient(centerRegionId).getReplicaSet(requestId, centerReplicaSetName, false);
        } else if (primaryReplicaSet.getPrimaryInsName() != null) {
            // 同region只读
            primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, primaryReplicaSet.getPrimaryInsName(), false);
        }
        return Pair.of(centerRegionId, primaryReplicaSet);
    }

    /**
     * 基础版小版本升级
     * 独立函数，便于维护
     */
    public Object doUpgradeDBVersion(Map<String, String> params, JSONObject taskParam) throws Exception {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        ReplicaSetResourceRequest resourceRequest = null;
        boolean isSuccess = false;
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            Replica masterReplica = mySQLService.getReplicaByRole(requestId, replicaSet.getName(), Replica.RoleEnum.MASTER);
            // 申请资源
            Double rcu = serverlessResourceService.getServerlessSpec(requestId, replicaSet.getName()).getScaleMax();

            params.put(RCU.toLowerCase(), rcu.toString());
            val modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);
            modifyInsParam.setTargetComposeTag(taskParam.get("serviceSpecTag").toString());

            // 申请临时实例
            AllocateTmpResourceResult result = allocateTmpResourceService.make(requestId, modifyInsParam.getCustins(), modifyInsParam);
            resourceRequest = result.getResourceRequest();

            // 临时实例关联主实例白名单标签，以及RCU信息
            Map<String, String> labels = ImmutableMap.of(
                    PodDefaultConstants.SG_USE_OTHER_CUSTINS, replicaSet.getId().toString(),
                    RCU, rcu.toString()
            );
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, resourceRequest.getReplicaSetName(), labels);
            if(ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType()){
                // 只读实例配置白名单同步label
                podParameterHelper.setReadInsSgLabel(requestId, replicaSet, resourceRequest.getReplicaSetName());
            }
            // 下发任务 & 更新状态
            taskParam.put("srcReplicaSetName", replicaSet.getName());
            taskParam.put("destReplicaSetName", resourceRequest.getReplicaSetName());
            taskParam.put("srcReplicaId", masterReplica.getId());
            taskParam.put("destReplicaId", mySQLService.getReplicaByRole(
                    requestId, resourceRequest.getReplicaSetName(), Replica.RoleEnum.MASTER).getId());

            if (replicaSetService.isStopped(replicaSet)) {
                stoppedServerlessMaintainService.dispatchStartTask(requestId, replicaSet, null);
            }

            String taskKey = ReplicaSet.InsTypeEnum.READONLY == replicaSet.getInsType() ? "upgrade_minor_version_for_serverless_basic_read"
                    : ServerlessConstant.TASK_UPGRADE_MINOR_VERSION_FOR_SERVERLESS;
            Object taskId = workFlowService.dispatchTask(
                    "custins", replicaSet.getName(), "mysql", taskKey, taskParam.toString(), 0);
            isSuccess = true;
            return taskId;
        } catch (Exception e) {
            logger.error(requestId + " Exception: ", e);
            throw e;
        } finally {
            // 处理失败时释放资源
            if (null != resourceRequest && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, resourceRequest.getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }
}
