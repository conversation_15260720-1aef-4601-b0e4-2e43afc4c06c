package com.aliyun.dba.serverless.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.PodAccountService;
import com.aliyun.dba.poddefault.action.service.PodIpWhiteListService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessCreateDBInstanceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.bak.support.BakSupport.*;
import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_BACK;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.RCU;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;


/**
 * 包括整个备份集恢复到新实例，库表恢复到新实例
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("serverlessCloneDBInstanceImpl")
public class CloneDBInstanceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(CloneDBInstanceImpl.class);
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodAvzSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    protected CustinsService custinsService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MySQLAvzService mySQLAvzService;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private BakService bakService;
    @Resource
    private PodIpWhiteListService podIpWhiteListService;
    @Resource
    private PodAccountService podAccountService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    protected DTZSupport dtzSupport;
    @Resource
    private PodDateTimeUtils podDateTimeUtils;
    @Resource
    private BackupService backupService;
    @Resource
    private KmsService kmsService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private AliyunInstanceDependency dependency;
    @Resource
    private PodCommonSupport podCommonSupport;

    @Resource
    private ServerlessCreateDBInstanceService serverlessCreateDBInstanceService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        boolean isSuccess = false;
        try {
            CustInstanceDO srcCustins = mysqlParameterHelper.getAndCheckSourceCustInstance();
            String sourceReplicaSetName = mysqlParamSupport.getAndCheckSourceDBInstanceName(params);
            ReplicaSet sourceReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, sourceReplicaSetName, true);
            CustinsParamDO minorVersionParam = custinsParamService.getCustinsParam(srcCustins.getId(), "minor_version");
            String clusterName = mysqlParamSupport.getParameterValue(params, ParamConstants.CLUSTER_NAME);
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
            String minorVersion = minorVersionParam.getValue();
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);

            params.put(ParamConstants.DB_INSTANCE_CONN_TYPE.toLowerCase(), "lvs");
            PodCreateInsParam metaParam = new PodCreateInsParam(dependency, params);
            metaParam.setServerlessInfo()
                    .setServerlessStorageAutoScale()
                    .setUserId()
                    .setConnType()
                    .setIsPhysical()
                    .setIsTDDL()
                    .setDBType()
                    .setDBVersion()
                    .setClassCode()
                    .setInstanceLevel()
                    .setDiskSize()
                    .setDiskType()
                    .setAutoPLConfig()
                    .setDBEngine()
                    .setDispenseModeForce()
                    .setAvzInfo()
                    .setVpcId()
                    .setVswitchId()
                    .setInstanceName()
                    .setConnectionString()
                    .setIpAddress()
                    .setRegionId()
                    .setExternalReplication()
                    .setPodType();

            String serviceSpecTag = minorVersionServiceHelper.tryGetServiceSpecTag(
                    minorVersion,
                    sourceReplicaSet.getBizType(),
                    metaParam.getDbType(),
                    metaParam.getDbVersion(),
                    metaParam.getDbEngine(),
                    KindCodeParser.KIND_CODE_NEW_ARCH,
                    metaParam.getInstanceLevel(),
                    metaParam.getDiskType(),
                    isDhg,
                    null);

            logger.info("clone serverless instance minorVersion:{}, serviceSpecTag: {}", minorVersion, serviceSpecTag);
            if (StringUtils.isEmpty(serviceSpecTag)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }

            Integer userId = mysqlParameterHelper.getAndCreateUserId();
            mysqlParameterHelper.checkUserOperatorCluster(userId);
            String restoreType = mysqlParamSupport.getParameterValue(params, ParamConstants.RESTORE_TYPE);
            String targetStorageType = getParameterValue(params, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
            String restoreUsage = getParameterValue(params, MySQLParamConstants.RESTORE_USAGE);  //恢复的使用用途
            boolean isRestoreByTime = RESTORE_TYPE_TIME.equals(restoreType);
            if (sourceReplicaSet == null) {
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }
            Map<String, String> srcReplicaSetLabels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, sourceReplicaSetName);
            // Serverless不支持XEngine引擎
            if (dependency.getPodCommonSupport().isXEngine(srcReplicaSetLabels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID))) {
                throw new RdsException(ErrorCode.UNSUPPORTED_STORAGE_ENGINE);
            }

            User user = dBaasMetaService.getDefaultClient().getUser(requestId, sourceReplicaSet.getUserId(), null);
            String bid = user.getBid();
            String uid = user.getAliUid();
            boolean isXDB = replicaSetService.isReplicaSetXDB(requestId, sourceReplicaSet.getName());

            BakhistoryDO bakHistory = null;
            Date restoreTimeUTC = null;
            if (RESTORE_TYPE_BAKID.equals(restoreType)) {
                Long bakId = CheckUtils.parseLong(getParameterValue(params, ParamConstants.BACKUP_SET_ID), null,
                        null, ErrorCode.BACKUPSET_NOT_FOUND);
                bakHistory = bakService.getBakhistoryByBackupSetId(sourceReplicaSet.getId().intValue(), bakId);
            } else if (isRestoreByTime) {
                restoreTimeUTC = podParameterHelper.getAndCheckRestoreTime(requestId, srcCustins);
                bakHistory = podParameterHelper.getBakhistoryByRecoverTime(srcCustins.getId(), restoreTimeUTC);
            }
            if (bakHistory == null) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }

            boolean isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);
            GetBackupSetResponse backupSet = backupService.getBackupSet(
                    BackupSetParam.builder()
                            .uid(uid)
                            .user_id(bid)
                            .dBInstanceId(srcCustins.getId())
                            .backupSetId(Long.valueOf(bakHistory.getHisId())).build());

            this.lockBakResource(srcCustins, bakHistory, restoreTimeUTC);

            String resourceGroupId = getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim();

            Long realBacksetSize = bakHistory.getBaksetSize();
            Integer diskSize = CheckUtils.parseInt(
                    mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                    5, 102400, ErrorCode.INVALID_STORAGE);
            if (diskSize != null && Long.valueOf(diskSize) * CustinsSupport.GB_TO_KB < realBacksetSize) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            String snapshotId = null;
            String performanceLevel = null;
            String diskType = StringUtils.isEmpty(targetStorageType) ? replicaSetService.getReplicaSetStorageType(sourceReplicaSetName, requestId) : targetStorageType;

            if (ReplicaSetService.isStorageTypeCloudDisk(PodParameterHelper.transferDiskTypeParam(diskType))) {
                snapshotId = backupSet.getSlaveStatusObj().getSnapshotId();
                if (StringUtils.isBlank(snapshotId)) {
                    logger.error("cannot find snapshotId from bak_history");
                    return createErrorResponse(ErrorCode.INVALID_BAKSET);
                }
                if (StringUtils.isEmpty(targetStorageType)) {
                    // 没有设置目标存储类型，取原实例的数据盘的CloudESSD性能等级
                    performanceLevel = replicaSetService.getVolumePerfLevel(requestId, sourceReplicaSetName, diskType);
                } else {
                    // 设置了目标存储类型
                    PodParameterHelper.checkCloudEssdStorageValid(diskType, diskSize);
                    performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
                    diskType = PodParameterHelper.transferDiskTypeParam(diskType);
                }
            }
            String bizType = sourceReplicaSet.getBizType().toString();
            String dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String connType = sourceReplicaSet.getConnType().toString();
            String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(params, ParamConstants.VSWITCH_ID));
            String connectionString = CheckUtils
                    .checkValidForConnAddrCust(getParameterValue(params, ParamConstants.CONNECTION_STRING));
            String ipAddress = mysqlParamSupport.getParameterValue(params, ParamConstants.IP_ADDRESS);
            String vpcInstanceId = mysqlParamSupport.getParameterValue(params, ParamConstants.VPC_INSTANCE_ID);

            String dbType = sourceReplicaSet.getService();
            String dbVersion = sourceReplicaSet.getServiceVersion();
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);
            IPWhiteList ipWhiteList = podParameterHelper.getAndCheckReplicaSetIpWhiteList();  //支持传入自定义的白名单
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
            String dbEngine = isXDB ? "XDB" : "MySQL";
            podParameterHelper.resetDispenseMode(params, targetInstanceLevel, dbEngine, false);
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);
            Replica.RoleEnum[] nodeRoles = PodCommonSupport.getRoles(dbEngine, targetInstanceLevel, false, avzInfo);

            // 写优化克隆实例直接继承源实例
            String optimizedWritesInfo = podCommonSupport.getOptimizedWritesInfo(requestId, dbVersion, diskType, sourceReplicaSet);
            boolean isInitOptimizedWrites = podCommonSupport.isInitOptimizedWrites(optimizedWritesInfo);

            String insTypeDesc = ReplicaSet.InsTypeEnum.MAIN.toString();
            // 检验实例实例已经存在
            if (dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, true) != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, sourceReplicaSet.getService(), sourceReplicaSet.getServiceVersion(), sourceReplicaSet.getClassCode(), null);
            if (!Objects.equals(targetInstanceLevel.getHostType(), srcInstanceLevel.getHostType())) {
                // 新架构不支持不同hostType的规格之间克隆
                throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL);
            }
            // 备份相关
            Integer bakRetention = CheckUtils.parseInt(getParameterValue(params, ParamConstants.BACKUP_RETENTION, 7),
                    1, 730,
                    ErrorCode.INVALID_BACKUPRETENTIONPERIOD);
            String preferredBackupTime = mysqlParamSupport.getParameterValue(params, "PreferredBackupTime");
            if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PREFERREDBACKUPTIME);
            }
            String preferredBackupPeriod = CheckUtils.checkValidForBackupPeriod(getParameterValue(params, "preferredbackupperiod"));
            String enableBackup = getParameterValue(params, "EnableBackup");
            String enableBackupLog = getParameterValue(params, ParamConstants.ENABLE_BACKUP_LOG);

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(sourceReplicaSet, podParameterHelper.getParameterValue(ParamConstants.UID))) {
                return ResponseSupport.createErrorResponse(ErrorCode.INVALID_KMS_KEY);
            }

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setServerlessRcu(getCloneTmpRcu(requestId, sourceReplicaSet, metaParam.getServerlessSpec()));
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setPort(portStr);
            replicaSetResourceRequest.setInsType(insTypeDesc);
            replicaSetResourceRequest.setReplicaSetName(dbInstanceName);
            replicaSetResourceRequest.setDomainPrefix(connectionString);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setConnType(podParameterHelper.isServerlessV2(metaParam.getUid(), metaParam.getPodType()) ? CONN_TYPE_PHYSICAL : connType);
            replicaSetResourceRequest.setBizType(bizType);
            replicaSetResourceRequest.setClassCode(classCode);
            replicaSetResourceRequest.setStorageType(diskType);
            replicaSetResourceRequest.setDiskSize(diskSize);
            replicaSetResourceRequest.setProvisionedIops(metaParam.getProvisionedIops());
            replicaSetResourceRequest.setBurstingEnabled(metaParam.isBurstingEnabled());
            replicaSetResourceRequest.setVpcId(vpcId);
            replicaSetResourceRequest.setVswitchID(vswitchId);
            replicaSetResourceRequest.setCloudInstanceIp(ipAddress);
            replicaSetResourceRequest.setVpcInstanceId(vpcInstanceId);
            replicaSetResourceRequest.setSubDomain(avzInfo.getRegion());
            replicaSetResourceRequest.setRegionId(avzInfo.getRegionId());
            replicaSetResourceRequest.setVpcInstanceId(vpcInstanceId);
            replicaSetResourceRequest.setCloudInstanceIp(ipAddress);
            replicaSetResourceRequest.setInitOptimizedWrites(isInitOptimizedWrites);
            if (StringUtils.isNotBlank(clusterName) && isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
            }
            if (srcReplicaSetLabels.containsKey(PodDefaultConstants.ENCRYPTION_KEY_LABEL)) {
                replicaSetResourceRequest.setEncryptionKey(srcReplicaSetLabels.get(PodDefaultConstants.ENCRYPTION_KEY_LABEL));
                replicaSetResourceRequest.setEncryptionType(srcReplicaSetLabels.get(PodDefaultConstants.ENCRYPTION_TYPE_LABEL));
            }

            replicaSetResourceRequest.setComposeTag(serviceSpecTag);
            replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());

            List<ReplicaResourceRequest> replicas = new ArrayList<>();

            boolean isSingleTenant = replicaSetService.isCloudSingleTenant(sourceReplicaSet.getBizType(), diskType, targetInstanceLevel, isDhg);
            String sourceDbInstanceName = getParameterValue(params, "sourcedbinstancename");

            Pair<String, ScheduleTemplate> scheduleTemplatePair = podTemplateHelper
                    .getBizSysScheduleTemplate(
                            PodType.POD_RUNC,
                            sourceReplicaSet.getBizType(),
                            dbEngine,
                            targetInstanceLevel,
                            isSingleTenant,
                            insTypeDesc,
                            dbInstanceName, sourceDbInstanceName, mysqlParamSupport.getUID(params));
            replicaSetResourceRequest.setScheduleTemplate(scheduleTemplatePair.getValue());
            String rsTemplateName = scheduleTemplatePair.getKey();

            Map<Replica.RoleEnum, String> roleHostNameMapping = podParameterHelper.getRoleHostNameMapping();

            for (int i = 0; i < nodeRoles.length; i++) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                AvailableZoneInfoDO availableZoneInfoDO = mySQLAvzService.getRoleZoneId(avzInfo, nodeRoles[i].toString());

                if (dbEngine.equalsIgnoreCase("XDB") && nodeRoles[i].equals(Replica.RoleEnum.LOGGER)) {
                    InstanceLevelListResult instanceLevels = dBaasMetaService
                            .getDefaultClient().listInstanceLevelChildren(requestId, dbType, dbVersion, classCode);
                    InstanceLevel loggerLevel = instanceLevels.getItems().stream().filter(x -> x.getClassCode().contains("logger")).collect(Collectors.toList()).get(0);
                    replicaResourceRequest.setClassCode(loggerLevel.getClassCode());
                    replicaResourceRequest.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
                } else {
                    replicaResourceRequest.setClassCode(classCode);
                    replicaResourceRequest.setDiskSize(diskSize);  //云盘的赠送逻辑做在任务流中，通过在线扩容来实现
                    if (ReplicaSetService.isStorageTypeCloudDisk(diskType)) {
                        // 基于快照来数据恢复
                        VolumeSpec volumeSpec = new VolumeSpec();
                        volumeSpec.setSnapshotId(snapshotId);
                        volumeSpec.setName("data");
                        volumeSpec.setPerformanceLevel(performanceLevel);
                        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                    }
                }
                replicaResourceRequest.setHostName(roleHostNameMapping.get(nodeRoles[i]));
                replicaResourceRequest.setRole(nodeRoles[i].toString());
                replicaResourceRequest.setStorageType(diskType);
                replicaResourceRequest.setSingleTenant(isSingleTenant);
                replicaResourceRequest.setZoneId(availableZoneInfoDO.getZoneID());
                replicaResourceRequest.setVswId(availableZoneInfoDO.getVSwitchID());
                replicas.add(replicaResourceRequest);
            }

            replicaSetResourceRequest.setSingleTenant(isSingleTenant);

            // 专属集群和集团使用eni
            if (isDhg || PodParameterHelper.isAliGroup(bizType)) {
                replicaSetResourceRequest.setEniDirectLink(true);
            } else {
                replicaSetResourceRequest.setEniDirectLink(false);
            }

            replicaSetResourceRequest.setDiskSize(diskSize);  //用户可见的磁盘空间
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
            replicaSetResourceRequest.ignoreCreateVpcMapping(true); //反向VPC的资源申请下沉到任务流
            if (metaParam.getPodType() == PodType.POD_ECS_RUND) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }
            podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            try {
                isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, dbInstanceName, replicaSetResourceRequest);

                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);

                // 更新主可用区参数
                custinsParamService.updateAVZInfo(replicaSet.getId().intValue(), avzInfo);
                //账户和db同步
                podAccountService.syncReplicaSetSuperAccount(requestId, sourceReplicaSetName, replicaSet.getName());
                //白名单数据同步
                if (ipWhiteList != null) {
                    //如果传入了指定白名单，则只用用户设置的
                    dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, ipWhiteList);
                } else {
                    podIpWhiteListService.syncReplicaSetIpWhiteList(requestId, sourceReplicaSetName, replicaSet.getName());
                }
                // 克隆实例label配置
                setReplicaSetLabels(requestId, replicaSet, srcReplicaSetLabels, dbEngine, rsTemplateName, resourceGroupId, metaParam, optimizedWritesInfo);
                // 创建MaxScale实例
                Long maxScaleTaskId = -1L;
                if (replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.PHYSICAL) {
                    maxScaleTaskId = serverlessCreateDBInstanceService.createMaxScaleIns(requestId, metaParam, params);
                }

                String storageAutoScale = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_AUTO_SCALE);
                String storageUpperBound = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_UPPER_BOUND);
                String storageThreshold = podParameterHelper.getParameterValue(DockerOnEcsConstants.STORAGE_THRESHOLD);

                // Dispatch task
                String domain = PodCommonSupport.getTaskDomain(sourceReplicaSet.getBizType());
                String taskKey = ServerlessConstant.TASK_CLONE_SERVERLESS_INS;
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("replicaSetName", dbInstanceName);
                jsonObject.put("maxScaleTaskId", maxScaleTaskId);
                jsonObject.put("sourceReplicaSetName", sourceReplicaSetName);
                // 备份信息
                if (StringUtils.isNotBlank(enableBackup)) {
                    jsonObject.put("enableBackup", enableBackup);
                }
                if (StringUtils.isNotBlank(enableBackupLog)) {
                    jsonObject.put("enableBackupLog", enableBackupLog);
                }
                // 备份保留时间, 比如 7 天
                jsonObject.put("bakRetention", bakRetention);
                // 每周哪几天备份, 比如 0101010
                jsonObject.put("preferredBackupPeriod", preferredBackupPeriod);
                // 备份时间, UTC, 比如 06:35Z
                jsonObject.put("preferredBackupTime", preferredBackupTime);

                jsonObject.put("restoreType", restoreType);
                jsonObject.put("bakHisID", bakHistory.getHisId());
                jsonObject.put("backupSetHostId", bakHistory.getHostinsId());

                // UTC时间用于和各个备份服务进行交互
                jsonObject.put("backupSetStartTime", podDateTimeUtils.convert2UTCStr(bakHistory.getBakBegin()));
                jsonObject.put("restoreTime", podDateTimeUtils.convert2UTCStr(restoreTimeUTC));

                jsonObject.put("startBinlogFile", backupSet.getSlaveStatusObj().getBinLogFile());
                jsonObject.put("isPengineBackupSet", isPengineBackupSet);

                // DAS配置
                if (StringUtils.isNotBlank(metaParam.getStorageAutoScale())) {
                    jsonObject.put("storageAutoScale", metaParam.getStorageAutoScale());
                    jsonObject.put("storageUpperBound", metaParam.getStorageUpperBound());
                    jsonObject.put("storageThreshold", metaParam.getStorageThreshold());
                }

                // serverless信息
                jsonObject.put(ServerlessConstant.SCALE_MIN, metaParam.getServerlessSpec().getScaleMin());
                jsonObject.put(ServerlessConstant.SCALE_MAX, metaParam.getServerlessSpec().getScaleMax());
                jsonObject.put(ServerlessConstant.AUTO_PAUSE, metaParam.getServerlessSpec().getAutoPause());
                jsonObject.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, metaParam.getServerlessSpec().getSecondsUntilAutoPause());
                jsonObject.put(ServerlessConstant.DAS_AUTO_PAUSE, metaParam.getServerlessSpec().getDasAutoPause());
                jsonObject.put(ServerlessConstant.KEEP_RUNNING_TIME, metaParam.getServerlessSpec().getKeepRunningTime());

                if (ReplicaSet.StatusEnum.STOPPED.equals(sourceReplicaSet.getStatus())) {
                    jsonObject.put("srcReplicaSetStatus", "STOPPED");
                }

                String parameter = jsonObject.toJSONString();
                Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);

                boolean needUpdateSrcStatus = isRestoreByTime &&    //按时间点恢复需要订正原实例状态
                        !ReplicaSet.StatusEnum.STOPPED.equals(sourceReplicaSet.getStatus()) &&    //非停机状态克隆不需要订正原实例
                        !StringUtils.equalsIgnoreCase(restoreUsage, MySQLParamConstants.DOWNLOAD_BAKSET); //云盘备份集下载不需要订正原实例
                if (needUpdateSrcStatus) {
                    custinsService.updateCustInstanceStatusByCustinsId(
                            sourceReplicaSet.getId().intValue(), CUSTINS_STATUS_BACK, CustinsState.STATE_INS_CLONING.getComment());
                }

                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", taskId);
                data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
                data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
                isSuccess = true;
                return data;

            } catch (Exception e) {
                logger.error(e);
                isAllocate = false;
                if (e instanceof ApiException) {
                    logger.error(requestId + " Exception: {}", ((ApiException) e).getResponseBody());
                    return createErrorResponse(ErrorCode.RESOURCE_NOT_FOUND);
                }
                logger.error(requestId + " Exception: {}", e.getMessage());
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                    } catch (ApiException e) {
                        //ignore
                        logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }

        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess) {
                this.unLockBakResource();
            }
        }

    }

    /**
     * 锁定备份资源，避免克隆过程中被清理
     */
    private void lockBakResource(CustInstanceDO custInstance, BakhistoryDO bakHistory, Date restoreTimeBak) {
        if (restoreTimeBak != null) {
            bakService.lockBinlogForRestore(custInstance.getId(), bakHistory.getBakBegin(), restoreTimeBak); // lock binlog for restore
            ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBinlog",
                    JSON.toJSONString(ImmutableMap.of(
                            "custinsId", custInstance.getId().toString(),
                            "begin", bakHistory.getBakBegin().getTime(),
                            "end", restoreTimeBak.getTime())));
        }

        bakService.lockBakHisForRestore(bakHistory.getHisId());
        ActionParamsProvider.ACTION_PARAMS_MAP.get().put("LockBackup", String.valueOf(bakHistory.getHisId()));
    }

    // clone 是获取临时的rcu
    public double getCloneTmpRcu(String requestId, ReplicaSet sourceReplicaSet, ServerlessSpec serverlessSpec) {
        double currRcu = serverlessSpec.getRcu();
        try {
            String oriCurrRcu = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, sourceReplicaSet.getName(), RCU.toLowerCase());
            if (StringUtils.isEmpty(oriCurrRcu)) {
                return currRcu;
            }
            currRcu = Double.parseDouble(oriCurrRcu);
        } catch (Exception e) {
            logger.info("get current value failed");
            return currRcu;
        }
        return serverlessResourceService.getRcuForTmpInsByCurrent(requestId, currRcu, serverlessSpec);
    }


    /**
     * API流程异常时，解锁备份资源
     */
    private void unLockBakResource() {
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBinlog")) {
            JSONObject lockBinlog = JSON.parseObject(ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBinlog"));
            bakService.unlockBinlogForRestore(
                    Integer.valueOf(lockBinlog.get("custinsId").toString()),
                    new Date(Long.parseLong(lockBinlog.get("begin").toString())),
                    new Date(Long.parseLong(lockBinlog.get("end").toString()))
            );
        }
        if (ActionParamsProvider.ACTION_PARAMS_MAP.get().containsKey("LockBackup")) {
            String lockBackup = ActionParamsProvider.ACTION_PARAMS_MAP.get().get("LockBackup");
            bakService.unlockBakHisForRestore(Long.valueOf(lockBackup));
        }
    }

    /**
     * 克隆实例label配置
     */
    public void setReplicaSetLabels(String requestId, ReplicaSet replicaSet, Map<String, String> srcReplicaSetLabels,
                                    String dbEngine, String rsTemplateName, String resourceGroupId, PodCreateInsParam metaParam, String optimizedWritesInfo) throws com.aliyun.apsaradb.dbaasmetaapi.ApiException {
        Map<String, String> labels = new HashMap<>();



        // 继承label
        List<String> labelsFromSrc = Arrays.asList(
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID,
                CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO,
                CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS,
                CustinsParamSupport.CUSTINS_PARAM_NAME_FREQUENCY
        );

        for (String name : labelsFromSrc) {
            if (StringUtils.isNotEmpty(srcReplicaSetLabels.getOrDefault(name, null))) {
                labels.put(name, srcReplicaSetLabels.get(name));
            }
        }

        // 新增label
        labels.put(ServerlessConstant.SERVERLESS_RCU, String.valueOf(metaParam.getServerlessSpec().getRcu()));
        labels.put(ServerlessConstant.SERVERLESS_CATEGORY, ServerlessConstant.IS_SERVERLESS);
        labels.putAll(metaParam.getServerlessSpec().getLabels());
        labels.put(CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, resourceGroupId);

        // 原来的实例是否有跨级升级，没有增加默认值
        String newAllowRemote = srcReplicaSetLabels.getOrDefault(ServerlessConstant.ALLOW_REMOTE_SCALE, ServerlessConstant.DEFAULT_ALLOW_REMOTE_SCALE);
        labels.put(ServerlessConstant.ALLOW_REMOTE_SCALE, newAllowRemote);

        // 写优化参数传递 OPTIMIZED_WRITES_INFO来源于他的源实例
        if (StringUtils.isNoneBlank(optimizedWritesInfo)) {
            labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, optimizedWritesInfo);
        }

        if ("XDB".equals(dbEngine)) {
            //xdb 一定是强同步
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SYNC);
        }

        String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, replicaSet.getName());
        if (StringUtils.isNotBlank(minorVersion)) {
            labels.put("minor_version", minorVersion);
        }

        // 云上业务需要开启审计日志
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        }

        //指定实例资源调度模板的写入实例和模版名关联
        if (Strings.isNotBlank(rsTemplateName)) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, rsTemplateName);
        }

        // 克隆实例默认小版本自动升级，参考物理机实现
        labels.put(ParamConstants.AUTO_UPGRADE_MINOR_VERSION, "Auto");

        dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, replicaSet.getName(), labels);
    }
}
