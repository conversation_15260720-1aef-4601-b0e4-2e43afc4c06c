package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.poddefault.action.support.urd.URDWalker;
import com.aliyun.dba.poddefault.action.support.urd.URDZoneDescriptor;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;

/**
 * <AUTHOR> on 2020/6/12.
 */
@Service
public class ServerlessCreateDBInstanceService {

    private static final LogAgent logger = LogFactory.getLogAgent(ServerlessCreateDBInstanceService.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private AliyunInstanceDependency dependency;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Autowired
    private WhitelistTemplateService whitelistTemplateService;
    @Resource
    private RundPodSupport rundPodSupport;

    /**
     * 创建实例
     *
     * @category CreateReadDBInstance
     */
    public Map<String, Object> createDBInstance(CustInstanceDO custins, Map<String, String> params) {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            PodCreateInsParam metaParam = new PodCreateInsParam(dependency, params);
            // 初始化实例基础信息
            metaParam
                    .setUserId()                        // 初始化用户新消息
                    .setClusterName()                   // 初始化cluster信息，集团实例的cluster信息在判断TDDL后补录
                    .setInstanceName()                  // 初始化实例名
                    .setDBType()                        // 初始化数据库类型
                    .setDBVersion()                     // 初始化数据库版本
                    .setTargetMinorVersion()            // 初始化内核小版本
                    .setAutoUpgradeMinorVersion()       // 初始化版本升级策略
                    .setPortStr()                       // 初始化端口
                    .setDBName()                        // 设置数据库
                    .setDBEngine()                      // XDB | MySQL
                    .setConnType()                      // 连接类型
                    .setTimezone()                      // 初始化时区
                    .setTddlClusterName()               // 初始化集团TDDL配置
                    .setTddlRegionConfig()              // 初始化集团TDDL配置
                    .setClassCode()                     // 规格
                    .setInstanceLevel()                 // class code
                    .setInstructionSetArch()            // 初始化实例架构类型（x86 or arm）
                    .setDispenseModeForce()             // 强制设置为主可用区模式
                    .setAvzInfo()                       // 初始化AvzInfo
                    .setRegionId()                      // 获取region
                    .setSnapshotId()                    // 设置备份集信息（如果有）
                    .setBizType()                       // aligroup | aliyun
                    .setIsForMigrate()                  // 设置migrate标（如果有）
                    .setIsTDDL()                        // 设置TDDL标
                    .setIsPhysical()                    // 设置Physical标
                    .setIsDHG()                         // 设置主机组信息
                    .setOrderId()                       // 设置OrderID
                    .setAccessId()
                    .setIsSingleNode()                  // 设置单节点标
                    .setExternalReplication()
                    .setPodType();


            // 初始化安全相关参数
            metaParam
                    .setIPWhiteList()
                    .setWhitelistTemplateList()
                    .setTemplateIdList()
                    .setRoleArn();

            // 初始化链路相关参数
            metaParam
                    .setVpcId()
                    .setVswitchId()
                    .setConnectionString()
                    .setVpcInstanceId()
                    .setIpAddress();
            // 初始化参数相关参数
            metaParam
                    .setCharSet()
                    .setParamGroupId()
                    .setCustomMysqlParams();
            // 初始化资源相关参数
            metaParam
                    .setResourceGroupId()
                    .setDiskSize()
                    .setDiskType()
                    .setURD()
                    .resetBizTypeAndDiskTypeForAliGroup()
                    .setInsTypeDesc(ReplicaSet.InsTypeEnum.MAIN);

            // 备份相关
            metaParam
                    .setBakRetention()
                    .setPreferredBackupTime()
                    .setPreferredBackupPeriod();

            //DAS相关
            metaParam.setServerlessStorageAutoScale();

            //serverless
            metaParam.setServerlessInfo();

            //活动相关
            metaParam.setActivities();

            // AutoPL
            metaParam.setAutoPLConfig();

            // optimized writes
            metaParam.setInitOptimizedWrites();

            logger.info("meta param's Temple name is {}", metaParam.getRsTemplateName());
            logger.info("meta param's scheduleTemple is {}",JSON.toJSONString(metaParam.getScheduleTemplate()));
            logger.info("meta param's instanceLevel is {}",JSON.toJSONString(metaParam.getInstanceLevel()));


            URDZoneDescriptor masterZone = metaParam.getUrd().getMasterZoneDesc();

            metaParam.setEncryptionKey();

            boolean isSingleTenant = metaParam.isSingleTenant();
            boolean isArm =  CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch());

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(metaParam.getBid());
            replicaSetResourceRequest.setUid(metaParam.getUid());
            replicaSetResourceRequest.setPort(metaParam.getPortStr());
            replicaSetResourceRequest.setInsType(metaParam.getInsType().toString());
            replicaSetResourceRequest.setReplicaSetName(metaParam.getDbInstanceName());
            replicaSetResourceRequest.setDomainPrefix(metaParam.getConnectionString());
            replicaSetResourceRequest.setDbType(metaParam.getDbType());
            replicaSetResourceRequest.setDbVersion(metaParam.getDbVersion());

            // Serverless实例使用Physical链路
            replicaSetResourceRequest.setConnType(podParameterHelper.isServerlessV2(metaParam.getUid(), metaParam.getPodType()) ? CONN_TYPE_PHYSICAL : metaParam.getConnType());

            replicaSetResourceRequest.setBizType(metaParam.getBizType().toString());
            replicaSetResourceRequest.setClassCode(metaParam.getClassCode());
            replicaSetResourceRequest.setStorageType(metaParam.getDiskType());
            replicaSetResourceRequest.setDiskSize(metaParam.getDiskSize());
            replicaSetResourceRequest.setVpcId(metaParam.getVpcId());
            replicaSetResourceRequest.setVswitchID(metaParam.getVswitchId());
            replicaSetResourceRequest.setCloudInstanceIp(metaParam.getIpAddress());
            replicaSetResourceRequest.setVpcInstanceId(metaParam.getVpcInstanceId());
            replicaSetResourceRequest.setSubDomain(masterZone.getRegion());
            replicaSetResourceRequest.setRegionId(masterZone.getRegionId());
            replicaSetResourceRequest.setSingleTenant(isSingleTenant);
            replicaSetResourceRequest.ignoreCreateVpcMapping(true);                    //反向VPC的资源申请下沉到任务流

            replicaSetResourceRequest.setTrafficPolicy(metaParam.getTrafficPolicy());  // DBStack专用，公共云不感知

            //资源annotations相关
            Map<String, Boolean> activities = metaParam.getActivities();
            replicaSetResourceRequest.setAnnotations(serverlessResourceService.getAnnotations(activities,
                    metaParam.getServerlessSpec().getScaleMin(), metaParam.getServerlessSpec().getScaleMax()));

            // 云盘加密相关
            replicaSetResourceRequest.setEncryptionKey(metaParam.getEncryptionKey());
            replicaSetResourceRequest.setEncryptionType(metaParam.getEncryptionType());

            //serverless 相关
            replicaSetResourceRequest.setServerlessRcu(metaParam.getServerlessSpec().getScaleMin());

            replicaSetResourceRequest.setProvisionedIops(metaParam.getProvisionedIops());
            replicaSetResourceRequest.setBurstingEnabled(metaParam.isBurstingEnabled());

            // build labels
            replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());
            replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, metaParam.getOrderId());
            replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, metaParam.getAccessId());

            // Optimized Writes
            replicaSetResourceRequest.setInitOptimizedWrites(metaParam.isInitOptimizedWrites());

            // 选择对应版本镜像
            String serviceSpecTag = null;
            if (StringUtils.isNumeric(metaParam.getSourceDBInstanceId())) {
                //从回收站恢复的，直接取原实例的specTag, 如果原实例的Tag找不到，则尝试用最新的，防止实例恢复报错
                serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId,
                        Integer.valueOf(metaParam.getSourceDBInstanceId()));
            }
            if (StringUtils.isEmpty(serviceSpecTag)) {
                serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                        metaParam.getTargetMinorVersion(),
                        metaParam.getBizType(),
                        metaParam.getDbType(),
                        metaParam.getDbVersion(),
                        metaParam.getDbEngine(),
                        KindCodeParser.KIND_CODE_NEW_ARCH,
                        metaParam.getInstanceLevel(),
                        metaParam.getDiskType(),
                        metaParam.isDhg(),
                        false);  // just online
            }
            if (StringUtils.isEmpty(serviceSpecTag)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            replicaSetResourceRequest.setComposeTag(serviceSpecTag);

            // rund 有最低内核版本要求
            if (metaParam.getPodType() == PodType.POD_ECS_RUND && !rundPodSupport.minorVersionSupportRund(serviceSpecTag)) {
                //reset runtimeType to RunC
                metaParam.setPodType(PodType.POD_RUNC);
            }
            metaParam.setRsTemplate();
            // 资源模板相关
            replicaSetResourceRequest.setDedicatedHostGroupId(metaParam.getDHGHostGroupId());
            replicaSetResourceRequest.setScheduleTemplate(metaParam.getScheduleTemplate());

            // 专属集群和集团使用eni
            boolean isENI = metaParam.isDhg() || metaParam.getBizType() == ReplicaSet.BizTypeEnum.ALIGROUP;
            replicaSetResourceRequest.setEniDirectLink(isENI);
            replicaSetResourceRequest.setDiskSize(metaParam.getDiskSize());  //用户可见的磁盘空间

            Map<Replica.RoleEnum, String> roleHostNameMapping = podParameterHelper.getRoleHostNameMapping();

            // Build replicaResource
            // 遍历节点申请资源
            List<ReplicaResourceRequest> replicaResources = new ArrayList<>();
            metaParam.getUrd().walkNode(new URDWalker() {
                @Override
                public void walkNode(URDZoneDescriptor zoneDescriptor, Replica.RoleEnum role) throws Exception {
                    ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                    replicaResourceRequest.setStorageType(metaParam.getDiskType());
                    // 设置资源模板
                    replicaResourceRequest.setScheduleTemplate(metaParam.getScheduleTemplate());
                    replicaResourceRequest.setRole(role.toString());
                    // 大客户主机组指定机器
                    replicaResourceRequest.setHostName(roleHostNameMapping.get(role));
                    // 设置是否是单租户
                    replicaResourceRequest.setSingleTenant(isSingleTenant);
                    replicaResourceRequest.setZoneId(zoneDescriptor.getZoneId());
                    if (metaParam.getPodScheduleTemplate() != null) {
                        replicaResourceRequest.setScheduleTemplate(
                                dependency.getPodTemplateHelper().getReplicaScheduleTemplateByRole(metaParam.getPodScheduleTemplate(), role.toString()));
                    }
//                    replicaResourceRequest.setWeight(zoneDescriptor.getZoneWeight());
                    // 根据节点角色设置参数

                    setReplicaResourceForDataNode(metaParam, replicaResourceRequest);

                    // 设置rund 网络相关配置
                    if (metaParam.getPodType() == PodType.POD_ECS_RUND) {
                        rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, metaParam.getAvzInfo());
                    }
                    replicaResources.add(replicaResourceRequest);
                }
            });
            if (metaParam.getPodType() == PodType.POD_ECS_RUND) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            if (StringUtils.isNotEmpty(metaParam.getInstructionSetArch())) {
                if (!CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())
                        && !CreateReplicaSetDto.ArchEnum.X86.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())) {
                    return ResponseSupport.createErrorResponse(MysqlErrorCode.INVALID_INSTRUCTIONSET_ARCH.toArray());
                }
                replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.valueOf(metaParam.getInstructionSetArch()));
            }
            replicaSetResourceRequest.setReplicaResourceRequestList(replicaResources);
            podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            String dbInstanceName = metaParam.getDbInstanceName();
            IPWhiteList ipWhiteList = metaParam.getIpWhiteList();
            try {
                isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, dbInstanceName, replicaSetResourceRequest);
                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);

                //写入白名单数据
                dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, ipWhiteList);
                //写入白名单模版数据
                whitelistTemplateService.createWhitelistTemplateRecord(requestId,metaParam.getTemplateList(),metaParam.getTemplateIdList(),metaParam.getUserId(),dbInstanceName);
                // 更新主可用区参数
                custinsParamService.updateAVZInfo(replicaSet.getId().intValue(), metaParam.getAvzInfo());

                // 实例参数入库
                Map<String, String> labels = initReplicaSetLabels(requestId, metaParam, replicaSet);
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, dbInstanceName, labels);

                // 创建MaxScale实例
                Long maxScaleTaskId = -1L;
                if (replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.PHYSICAL) {
                    maxScaleTaskId = createMaxScaleIns(requestId, metaParam, params);
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("maxScaleTaskId", maxScaleTaskId);
                jsonObject.put("replicaSetName", dbInstanceName);
                // 备份信息
                // 备份保留时间, 比如 7 天
                jsonObject.put("bakRetention", metaParam.getBakRetention());
                // 每周哪几天备份, 比如 0101010
                jsonObject.put("preferredBackupPeriod", metaParam.getPreferredBackupPeriod());
                // 备份时间, UTC, 比如 06:35Z
                jsonObject.put("preferredBackupTime", metaParam.getPreferredBackupTime());
                jsonObject.put("instructionSetArch", metaParam.getInstructionSetArch());
                // 标记是否从老架构备份集恢复
                if (StringUtils.isNotEmpty(metaParam.getSnapshotId())) {
                    jsonObject.put("isPengineBackupSet", metaParam.isPEngineBackupSet());
                }
                if (StringUtils.isNotBlank(metaParam.getStorageAutoScale())) {
                    jsonObject.put("storageAutoScale", metaParam.getStorageAutoScale());
                    jsonObject.put("storageUpperBound", metaParam.getStorageUpperBound());
                    jsonObject.put("storageThreshold", metaParam.getStorageThreshold());
                }

                // serverless信息
                jsonObject.put(ServerlessConstant.SCALE_MIN, metaParam.getServerlessSpec().getScaleMin());
                jsonObject.put(ServerlessConstant.SCALE_MAX, metaParam.getServerlessSpec().getScaleMax());
                jsonObject.put(ServerlessConstant.AUTO_PAUSE, metaParam.getServerlessSpec().getAutoPause());
                jsonObject.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, metaParam.getServerlessSpec().getSecondsUntilAutoPause());
                jsonObject.put(ServerlessConstant.DAS_AUTO_PAUSE, metaParam.getServerlessSpec().getDasAutoPause());
                jsonObject.put(ServerlessConstant.KEEP_RUNNING_TIME, metaParam.getServerlessSpec().getKeepRunningTime());

                String parameter = jsonObject.toJSONString();

                // Dispatch task
                String domain = PodDefaultConstants.DOMAIN_MYSQL;
                String taskKey = getTaskKey(metaParam);
                Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, 0);
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", taskId);
                data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
                data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
                return data;
            } catch (Exception e) {
                logger.error(requestId + " Exception:" + e.getMessage(), e);
                isAllocate = false;
                if (e instanceof ApiException) {
                    logger.error("allocateReplicaSetResourceV1 failed requestId={} bizType={} region={} uid={} replicaSetName={}",
                            requestId, replicaSetResourceRequest.getBizType(), replicaSetResourceRequest.getRegionId(),
                            replicaSetResourceRequest.getUid(), replicaSetResourceRequest.getReplicaSetName());
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, (ApiException)e);
                }
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败或者其它异常的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                    } catch (ApiException e) {
                        //ignore
                        logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, String> initReplicaSetLabels(String requestId, PodCreateInsParam metaParam, ReplicaSet replicaSet) throws Exception {

        Map<String, String> labels = new HashMap<>();
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, metaParam.getParamGroupId());
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, JSON.toJSONString(SysParamGroupHelper.describeSysParamGroupId(metaParam.getParamGroupId())));
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, JSON.toJSONString(metaParam.getCustomMysqlParams()));
        labels.put(CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, metaParam.getResourceGroupId());
        labels.put(ServerlessConstant.SERVERLESS_RCU, String.valueOf(metaParam.getServerlessSpec().getRcu()));
        labels.put(ServerlessConstant.ALLOW_REMOTE_SCALE, ServerlessConstant.DEFAULT_ALLOW_REMOTE_SCALE);
        labels.put(ServerlessConstant.SERVERLESS_CATEGORY, ServerlessConstant.IS_SERVERLESS);
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_FREQUENCY, CustinsParamSupport.CUSTINS_PARAM_VALUE_PERF_FREQUENCY_VIP_INS_DEFAULT);
        labels.putAll(metaParam.getServerlessSpec().getLabels());
        //标记pfs盘
        if (metaParam.isUsePfs()) {
            labels.put(PodDefaultConstants.REPLICA_SET_LABEL_CLOUD_PFS, "true");
        }

        //指定实例资源调度模板的写入实例和模版名关联
        if (Strings.isNotEmpty(metaParam.getRsTemplateName())) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, metaParam.getRsTemplateName());
        }

        if(metaParam.getActivities() != null) {
            labels.put("activities", JSON.toJSONString(metaParam.getActivities()));
        }
        if ("XDB".equals(metaParam.getDbEngine())) {
            //xdb 一定是强同步
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SYNC);
            labels.put("dbEngine", metaParam.getDbEngine());
        }

//        //增加是否单租户的标
//        labels.put(PodDefaultConstants.REPLICA_SET_SINGLE_TENANT, String.valueOf(metaParam.isSingleTenant()));

        String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, replicaSet.getName());
        if (StringUtils.isNotBlank(minorVersion)) {
            labels.put("minor_version", minorVersion);
        }

        if (StringUtils.isNotEmpty(metaParam.getAutoUpgradeMinorVersion())) {
            labels.put(ParamConstants.AUTO_UPGRADE_MINOR_VERSION, metaParam.getAutoUpgradeMinorVersion());
        }

        // flag to enable write optimized
        Map<String, Boolean> optimizedWritesInfo = new HashMap<>();
        optimizedWritesInfo.put(CustinsParamSupport.INIT_OPTIMIZED_WRITES, metaParam.isInitOptimizedWrites());
        optimizedWritesInfo.put(CustinsParamSupport.OPTIMIZED_WRITES, metaParam.isInitOptimizedWrites());
        labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, JSON.toJSONString(optimizedWritesInfo));

        // 云上业务需要开启审计日志
        if (replicaSet.getBizType() == ReplicaSet.BizTypeEnum.ALIYUN) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        }

        if (CONN_TYPE_TDDL.equals(metaParam.getConnType())) {
            labels.put(TDDL_TASK_MIGRATE, "true");
            if (metaParam.isForMigrate()) {
                labels.put("without_tddl", "true");
            }
            labels.put("TddlBiztype", "non_unit");
            labels.put("TddlRegionConfig", metaParam.getTddlRegionConfig());
            labels.put("appDbList", JSON.toJSONString(metaParam.getDbName().split("\\|")));
            labels.put("bizType", metaParam.getBizType().toString());
            labels.put("charset", metaParam.getCharSet());
            labels.put("clusterName", metaParam.getTddlClusterName());
            labels.put("dbEngine", metaParam.getDbEngine());
            labels.put("dbType", metaParam.getDbType());
            labels.put("dbVersion", metaParam.getDbVersion());
            labels.put("requestId", requestId);
            labels.put("timeZone", metaParam.getTimeZone());
            labels.put("uid", metaParam.getUid());
            labels.put("userId", metaParam.getBid());
            labels.put("instructionSetArch", metaParam.getInstructionSetArch());
        }
        return labels;
    }

    private void setReplicaResourceForDataNode(PodCreateInsParam metaParam, ReplicaResourceRequest replicaResourceRequest) throws Exception {
        replicaResourceRequest.setClassCode(metaParam.getClassCode());
        if (ReplicaSetService.isStorageTypeCloudDisk(metaParam.getDiskType())) {
            //云上业务云盘需要有赠送，集团业务不需要
            int diskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(metaParam.getBizType(), metaParam.isSingleNode(), metaParam.getDiskSize());

            //cloud_pfs需要有分盘逻辑
            if (metaParam.isUsePfs()) {
                replicaResourceRequest.setVolumeSpecs(replicaSetService.getCloudDiskReplicaVolumeSpecList(diskSizeGB, null));
            }
            replicaResourceRequest.setDiskSize(diskSizeGB);
            if (metaParam.getSnapshotId() != null) {
                // 基于快照来数据恢复
                VolumeSpec volumeSpec = new VolumeSpec();
                volumeSpec.setSnapshotId(metaParam.getSnapshotId());
                volumeSpec.setName("data");
                replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                replicaResourceRequest.setDiskSize(metaParam.getDiskSize()); //通过快照恢复恢复的实例，需要使用在线扩容，所以放在任务流中
            }

            //设置云盘性能等级
            String performanceLevel = metaParam.getPerformanceLevel();
            if (StringUtils.isNotEmpty(performanceLevel)) {
                if (CollectionUtils.isNotEmpty(replicaResourceRequest.getVolumeSpecs())) {
                    replicaResourceRequest.getVolumeSpecs().stream().filter(v -> StringUtils.equalsIgnoreCase(v.getName(), "data")).forEach(v -> v.setPerformanceLevel(performanceLevel));
                } else {
                    VolumeSpec volumeSpec = new VolumeSpec();
                    volumeSpec.setName("data");
                    volumeSpec.setPerformanceLevel(performanceLevel);
                    replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                }
            }
        } else {
            replicaResourceRequest.setDiskSize(metaParam.getDiskSize());
        }
    }
    String getZoneKeyStr(String zoneId, String bizType) {
        return String.format("%s,%s", zoneId, bizType);
    }

    /**
     * 创建Serverless MaxScale实例
     * */
    public Long createMaxScaleIns(String requestId, PodCreateInsParam metaParam, Map<String, String> map) throws Exception {
        try {
            Map<String, String> params = serverlessResourceService.getProxyParam(map);
            params.put(ParamConstants.ACTION, "CreateDBProxy");
            params.put(ParamConstants.DB_INSTANCE_NAME, metaParam.getDbInstanceName());
            params.put("ProxyVPCId", metaParam.getVpcId());
            params.put("ProxyVSwitchId", metaParam.getVswitchId());
            params.put("ProxyIPAddress", metaParam.getIpAddress());
            params.put("ConnectionString", metaParam.getConnectionString());
            // Create 2 core maxscale proxy for serverless
            params.put("SliceNum", "2");
            logger.info("{} MaxScale Request {}", requestId, params);
            Map<String, Object> result = serverlessResourceService.invokeProxyApi(params);
            logger.info("{} MaxScale Response {}", requestId, result);
            Map taskInfo = JSON.parseObject(JSON.toJSONString(result.get("Data")), Map.class);
            return Long.parseLong(taskInfo.get("TaskId").toString());
        } catch (RdsException e) {
            logger.error("{} create maxScale Failed. RdsException: {}", requestId, Arrays.toString(e.getErrorCode()));
            throw new ApiException(Arrays.toString(e.getErrorCode()));
        } catch (Exception e) {
            logger.error("{} create maxScale Failed. Exception: {}", requestId, e);
            throw new ApiException(e.getMessage());
        }

    }

    private String getTaskKey(PodCreateInsParam metaParam) {
        String taskKey = null;
        if (metaParam.getSnapshotId() == null) {
            taskKey = ServerlessConstant.TASK_CREATE_SERVERLESS_INS;
        } else {
                taskKey = ServerlessConstant.TASK_CLONE_SERVERLESS_INS;
            }
        return taskKey;
    }

}
