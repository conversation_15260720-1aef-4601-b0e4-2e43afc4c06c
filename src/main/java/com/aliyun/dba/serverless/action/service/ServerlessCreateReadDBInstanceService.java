package com.aliyun.dba.serverless.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.aliyun.AliyunCreateReadOnlyInsImpl;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED_ON;

@Service
public class ServerlessCreateReadDBInstanceService extends AliyunCreateReadOnlyInsImpl {
    private static final LogAgent logger = LogFactory.getLogAgent(ServerlessCreateReadDBInstanceService.class);

    @Resource
    private DBaasMetaService metaService;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ServerlessResourceService serverlessResourceService;

    @Override
    public Object doCreateReadOnly(CreateReadOnlyInsRequest request, Map<String, String> params) throws RdsException {
        try {
            //step 1: build special request
            buildSpecialRequest(request, params);

            //step 2: do basic check
            Map<String, Object> checkResult = doBasicCheck(request);
            if (MapUtils.isNotEmpty(checkResult)) {
                return checkResult;
            }

            //step 3: do special check
            doSpecialCheck(request);

            //step 4: build allocate request
            ReplicaSetResourceRequest replicaSetResourceRequest = buildAllocateRequest(request);

            //step 5: allocate resource
            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            try {// 申请实例资源
                logger.info("allocateReplicaSetResourceV1:{}", JSONObject.toJSONString(replicaSetResourceRequest));
                isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(request.getRequestId(), request.getReadInsName(), replicaSetResourceRequest);
                ReplicaSet replicaSet = metaService.getDefaultClient().getReplicaSet(request.getRequestId(), request.getReadInsName(), false);
                if (replicaSet != null) {
                    logger.info("replicaSet insType is: {}", replicaSet.getInsType());
                } else {
                    logger.info("replicaSet is null");
                }

                // step 6: sync account meta
                syncDbAccountMeta(request);

                // step 7: sync instance lables
                addInstanceLables(request, replicaSet, replicaSetResourceRequest.getComposeTag());

                // step 8: 创建Serverless的隐藏MaxScale实例
                Long maxScaleTaskId = -1L;
                if (replicaSet.getConnType() == ReplicaSet.ConnTypeEnum.PHYSICAL) {
                    maxScaleTaskId = createMaxScaleIns(request.getRequestId(), request, params);
                }
                request.setMaxScaleTaskId(maxScaleTaskId);

                // step 9: Dispatch task
                return dispatchTask(request);
            } catch (Exception e) {
                logger.error(request.getRequestId() + " Exception: ", e);
                isAllocate = false;
                if (e instanceof com.aliyun.apsaradb.activityprovider.ApiException) {
                    return CommonProviderExceptionUtils.resourceWrapper(request.getRequestId(), (com.aliyun.apsaradb.activityprovider.ApiException)e);
                }
                logger.error(request.getRequestId() + " Exception: {}", e.getMessage());
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败或者其它异常的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(request.getRequestId(), request.getReadInsName());
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        //ignore
                        logger.error(request.getRequestId() + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }

        } catch (RdsException re) {
            logger.error(request.getRequestId() + " RdsException: ", re);
            throw new RdsException(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(request.getRequestId() + " Dbaas MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed: " + e.getMessage()});
        } catch (Exception ex) {
            logger.error(request.getRequestId() + " Exception: ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 创建Serverless MaxScale实例
     * */
    public Long createMaxScaleIns(String requestId, CreateReadOnlyInsRequest request, Map<String, String> map) throws Exception {
        try {
            Map<String, String> params = serverlessResourceService.getProxyParam(map);
            params.put(ParamConstants.ACTION, "CreateDBProxy");
            params.put(ParamConstants.DB_INSTANCE_NAME, request.getReadInsName());
            params.put("ProxyVPCId", request.getVpcId());
            params.put("ProxyVSwitchId", request.getVSwitchId());
            params.put("ProxyIPAddress", request.getIPAddress());
            params.put("ConnectionString", request.getConnectionString());
            // Create 2 core maxscale proxy for serverless
            params.put("SliceNum", "2");
            logger.info("{} MaxScale Request {}", requestId, params);
            Map<String, Object> result = serverlessResourceService.invokeProxyApi(params);
            logger.info("{} MaxScale Response {}", requestId, result);
            Map taskInfo = JSON.parseObject(JSON.toJSONString(result.get("Data")), Map.class);
            return Long.parseLong(taskInfo.get("TaskId").toString());
        } catch (RdsException e) {
            logger.error("{} create maxScale Failed. RdsException: {}", requestId, Arrays.toString(e.getErrorCode()));
            throw new com.aliyun.apsaradb.activityprovider.ApiException(Arrays.toString(e.getErrorCode()));
        } catch (Exception e) {
            logger.error("{} create maxScale Failed. Exception: {}", requestId, e);
            throw new com.aliyun.apsaradb.activityprovider.ApiException(e.getMessage());
        }

    }

    /**
     * submit workflow
     * @param request
     * @return
     */
    private Object dispatchTask(CreateReadOnlyInsRequest request) throws Exception {
        String domain = "mysql";
        String taskKey = getTaskKey();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", request.getRequestId());
        jsonObject.put("primaryReplicaSetName", request.getDbInstanceName());
        jsonObject.put("replicaSetName", request.getReadInsName());
        jsonObject.put("primaryDiskSizeMB", request.getPrimaryReplicaSet().getDiskSizeMB());
        jsonObject.put("destDiskSizeMB", request.getDiskSize() * 1024);
        jsonObject.put("maxScaleTaskId", request.getMaxScaleTaskId());

        // serverless信息
        jsonObject.put(ServerlessConstant.SCALE_MIN, request.getServerlessSpec().getScaleMin());
        jsonObject.put(ServerlessConstant.SCALE_MAX, request.getServerlessSpec().getScaleMax());
        jsonObject.put(ServerlessConstant.AUTO_PAUSE, request.getServerlessSpec().getAutoPause());
        jsonObject.put(ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE, request.getServerlessSpec().getSecondsUntilAutoPause());
        jsonObject.put(ServerlessConstant.DAS_AUTO_PAUSE, request.getServerlessSpec().getDasAutoPause());
        jsonObject.put(ServerlessConstant.KEEP_RUNNING_TIME, request.getServerlessSpec().getKeepRunningTime());
        if (request.getIsArmIns()) {
            jsonObject.put("instructionSetArch", "arm");
        }
        jsonObject.put("performanceLevel", request.getPerformanceLevel());
        String parameter = jsonObject.toJSONString();
        return workFlowService.dispatchTask("custins", request.getReadInsName(), domain, taskKey, parameter, 0);
    }

    /**
     * add instance lables
     */
    private void addInstanceLables(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet, String composeTag) throws ApiException, RdsException {
        Map<String, String> primaryInsLabels = metaService.getRegionClient(request.getCenterRegionId()).listReplicaSetLabels(request.getRequestId(), request.getDbInstanceName());
        Assert.notEmpty(primaryInsLabels, "primaryInsLabels not null");

        // 同步主实例标签
        Map<String, String> labels = new HashMap<>(primaryInsLabels);

        //指定实例资源调度模板的写入实例和模版名关联
        putLabels(labels, CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, request.getRsTemplateName(), true);

        // 小版本信息
        String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(request.getRequestId(), request.getReadInsName());
        podCommonSupport.checkReleaseDate(podCommonSupport.getReleaseDate(request.getRequestId(), null, minorVersion));
        putLabels(labels, "minor_version", minorVersionServiceHelper.resetReplicaSetMinorVersion(request.getRequestId(), request.getReadInsName()), true);
        //composeTag不能继承主实例，防止被使用
        putLabels(labels, "composeTag", composeTag, true);
        //IO加速不能继承主实例
        if (podCommonSupport.isIoAccelerationEnabled(request.getGeneralCloudDisk())) {
            putLabels(labels, IO_ACCELERATION_ENABLED, IO_ACCELERATION_ENABLED_ON, true);
        } else {
            labels.remove(IO_ACCELERATION_ENABLED);
        }

        //设置是否单租户标
        labels.put(PodDefaultConstants.REPLICA_SET_SINGLE_TENANT, String.valueOf(request.getIsSingleTenant()));

        putLabels(labels, "master_location", request.getAvzInfo().getMultiAVZExParamDO().getMasterLocation(), false);
        putLabels(labels, "multi_avz_ex_param", JSON.toJSONString(request.getAvzInfo().getMultiAVZExParamDO()), false);

        //高可用maz更新lave标签，基础版移除
        if (InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(readReplicaSet.getCategory())){
            String slaveLocation = String.join(",",request.getAvzInfo().getMultiAVZExParamDO().getSlaveLocations());
            putLabels(labels, "slave_location", slaveLocation, false);
        } else {
            labels.remove("slave_location");
        }

        labels.remove("migrateStage");

        // 移除开关类标签，功能类不继承
        labels.remove("ssl_enabled");
        labels.remove("enable_sql_log");

        // 移除主实例锁信息
        labels.remove("WF_LOCK");

        // 移除主实例 SQL 洞察相关参数
        labels.remove("sql_retention");
        labels.remove("sql_visible_time");
        labels.remove("enable_das_pro");

        // 资源类标签不继承
        labels.remove(PodDefaultConstants.PARAM_ARCH_LABEL);
        labels.remove(PodDefaultConstants.COMMON_K8S_CLUSTER_LABEL);

        // 移除主实例autopl配置参数，已在common组件中同步更新，不需要覆盖
        labels.remove(CustinsParamSupport.CUSTINS_PARAM_NAME_AUTOPL_PROVISIONED_IOPS);
        labels.remove(CustinsParamSupport.CUSTINS_PARAM_NAME_AUTOPL_BURSTING_ENABLED);

        addSpecialInstanceLables(request, readReplicaSet, composeTag, labels);

        labels.putAll(request.getServerlessSpec().getLabels());
        labels.put(ServerlessConstant.SERVERLESS_CATEGORY, ServerlessConstant.IS_SERVERLESS);

        // 写优化参数传递
        if (StringUtils.isNoneBlank(request.getOptimizedWritesInfo())) {
            labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, request.getOptimizedWritesInfo());
        }

        // 同步实例标签
        metaService.getDefaultClient().updateReplicaSetLabels(request.getRequestId(), request.getReadInsName(), labels);
    }

    /**
     * allocate resource request
     *
     * @param request
     * @return
     * @throws Exception
     */
    private ReplicaSetResourceRequest buildAllocateRequest(CreateReadOnlyInsRequest request) throws Exception {
        DefaultApi defaultApi = metaService.getDefaultClient();
        InstanceLevel instanceLevel = defaultApi.getInstanceLevel(request.getRequestId(), request.getDbType(), request.getDbVersion(), request.getClassCode(), null);
        Replica.RoleEnum[] roleEnumList = PodCommonSupport.getRoles(request.getDbEngine(), instanceLevel, true, null);
        // 根据主实例，获取category
        ReplicaSetResource primaryReplicaSetResource = metaService.getRegionClient(request.getCenterRegionId())
                .getReplicaSetBundleResource(request.getRequestId(), request.getDbInstanceName());
        Map<String, String> primaryInsLabels = primaryReplicaSetResource.getReplicaSet().getLabels();
        String category = primaryReplicaSetResource.getReplicaSet().getCategory();
        InstanceLevel primaryInstanceLevel = metaService.getRegionClient(request.getCenterRegionId()).getInstanceLevel(
                request.getRequestId(),
                request.getPrimaryReplicaSet().getService(),
                request.getPrimaryReplicaSet().getServiceVersion(),
                request.getPrimaryReplicaSet().getClassCode(),
                null);

        // Buid replicaSet resource
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        replicaSetResourceRequest.setUserId(request.getBid());
        replicaSetResourceRequest.setUid(request.getUid());
        replicaSetResourceRequest.setPort(request.getConnStrPortStr());
        replicaSetResourceRequest.setReplicaSetName(request.getReadInsName());
        replicaSetResourceRequest.setInsType(request.getInsTypeDesc());
        replicaSetResourceRequest.setCatagory(category);
        replicaSetResourceRequest.setEniDirectLink(request.getIsDhg());
        replicaSetResourceRequest.setComment(request.getComment());

        // build labels
        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, request.getOrderId());
        replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, request.getAccessId());

        ScheduleTemplate replicaSetTemplate = getReplicaSetTemplate(request, instanceLevel);
        replicaSetResourceRequest.setScheduleTemplate(replicaSetTemplate);
        replicaSetResourceRequest.setPrimaryInsName(request.getDbInstanceName());

        replicaSetResourceRequest.setDbType(request.getPrimaryReplicaSet().getService());
        replicaSetResourceRequest.setDbVersion(request.getDbVersion());
        replicaSetResourceRequest.setConnType(request.getConnType());
        replicaSetResourceRequest.setDedicatedHostGroupId(request.getClusterName());
        replicaSetResourceRequest.setClassCode(request.getClassCode());
        replicaSetResourceRequest.setStorageType(request.getDiskType());
        replicaSetResourceRequest.setDiskSize(request.getDiskSize());
        replicaSetResourceRequest.setBurstingEnabled(request.isBurstingEnabled());
        replicaSetResourceRequest.setProvisionedIops(request.getProvisionedIops());
        replicaSetResourceRequest.setGeneralCloudDisk(request.getGeneralCloudDisk());
        // 写优化参数完全继承主实例的参数 buildCommonRequest中进行的初始化
        replicaSetResourceRequest.setInitOptimizedWrites(podCommonSupport.isInitOptimizedWrites(request.getOptimizedWritesInfo()));
        replicaSetResourceRequest.setBizType(request.getBizType());
        replicaSetResourceRequest.setSubDomain(request.getAvzInfo().getRegion());
        replicaSetResourceRequest.setVpcId(request.getVpcId());
        replicaSetResourceRequest.setVswitchID(request.getVSwitchId());
        replicaSetResourceRequest.setCloudInstanceIp(request.getIPAddress());
        replicaSetResourceRequest.setVpcInstanceId(request.getVpcInstanceId());

        replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);

        // 云盘加密
        if (primaryInsLabels.containsKey(PodDefaultConstants.ENCRYPTION_KEY_LABEL)) {
            replicaSetResourceRequest.setEncryptionKey(primaryInsLabels.get(PodDefaultConstants.ENCRYPTION_KEY_LABEL));
            replicaSetResourceRequest.setEncryptionType(primaryInsLabels.get(PodDefaultConstants.ENCRYPTION_TYPE_LABEL));
        }

        // 为只读实例绑定composeTag
        // 只读实例的预期的composeTag与主实例相同，但由于规格都是高可用规格，所以需要用主实例的规格去获取tag
        String primaryMinorVersion = getTargetMinorVersionForReadIns(request, primaryInsLabels);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(primaryMinorVersion,
                ReplicaSet.BizTypeEnum.fromValue(request.getBizType()),
                request.getDbType(),
                request.getDbVersion(),
                request.getDbEngine(),
                KIND_CODE_NEW_ARCH,
                primaryInstanceLevel,
                request.getDiskType(),
                request.getIsDhg(),
                true); //需要查询到下线版本
        replicaSetResourceRequest.setComposeTag(serviceSpecTag);

        // todo：后续需要更新
        // IO加速的内核版本必须大于20221231
        if (podCommonSupport.isIoAccelerationEnabled(request.getGeneralCloudDisk())) {
            podCommonSupport.checkIoAccelerationSupportedMinorVersion(replicaSetResourceRequest.getComposeTag());
        }

        List<ReplicaResourceRequest> replicas = new ArrayList<>();
        logger.info("readInsReplicaCount is {}", request.getReadInsReplicaCount());

        if (!getAllocateDiskflag(request)) {
            replicaSetResourceRequest.setAllocateDisk(false);
        }

        // 基础版及高可用版，count=1，2
        // 高可用只读， slave尝试从maz字段里获取slave的可用区， 若没有则默认使用master的
        for (int i = 0; i < request.getReadInsReplicaCount(); i++) {
            String zoneId = request.getAvzInfo().getMasterZoneId();
            try{
                if (roleEnumList[i] == Replica.RoleEnum.SLAVE){
                    String slaveZoneId = request.getAvzInfo().getMultiAVZExParamDO().getSlaveAvailableZoneInfo().get(0).getZoneID();
                    if (!StringUtils.isBlank(slaveZoneId)){
                        zoneId = slaveZoneId;
                    }
                }
            } catch (Exception e){
                logger.warn("maz get slave info failed, use default master zoneId");
            }
            ReplicaResourceRequest replicaResourceRequest = buildReplicaResourceRequestUseZoneId(
                    request.getReadInsName(),
                    request.getDiskSize(),
                    request.getDiskType(),
                    request.getPerformanceLevel(),
                    request.getClassCode(),
                    zoneId,
                    roleEnumList[i],
                    null,
                    request.getIsSingleTenant(),
                    ReplicaSet.BizTypeEnum.fromValue(request.getBizType()));
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
        replicaSetResourceRequest.setSingleTenant(request.getIsSingleTenant());

        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        replicaSetResourceRequest.setRegionId(request.getAvzInfo().getRegionId());

        //资源annotations相关
        Map<String, Boolean> activities = request.getActivities();
        replicaSetResourceRequest.setAnnotations(serverlessResourceService.getAnnotations(activities,
                request.getServerlessSpec().getScaleMin(), request.getServerlessSpec().getScaleMax()));

        //serverless 相关
        replicaSetResourceRequest.setServerlessRcu(request.getServerlessSpec().getScaleMin());
        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.SCHEDULE_LABEL_SERVERLESS, Boolean.TRUE.toString());
        return replicaSetResourceRequest;
    }

    /**
     * get task key
     * @return String
     */
    private String getTaskKey() {
        return "create_serverless_read_ins";
    }

    /**
     * 只读实例节点个数
     *
     * @param request request
     * @return int
     * @throws ApiException ApiException
     */
    @Override
    public Integer getNodeCount(CreateReadOnlyInsRequest request) throws ApiException {
        InstanceLevel instanceLevel = metaService.getDefaultClient().getInstanceLevel(request.getRequestId(),
                request.getDbType(), request.getDbVersion(), request.getClassCode(), null);
        if (InstanceLevel.CategoryEnum.SERVERLESS_BASIC == instanceLevel.getCategory()) {
            return 1;
        } else if (InstanceLevel.CategoryEnum.SERVERLESS_STANDARD == instanceLevel.getCategory()) {
            return 2;
        }
        return 0;
    }
}
