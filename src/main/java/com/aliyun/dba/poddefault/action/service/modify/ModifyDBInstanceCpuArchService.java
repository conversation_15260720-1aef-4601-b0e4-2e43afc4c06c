package com.aliyun.dba.poddefault.action.service.modify;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Endpoint;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointChangeLog;
import com.aliyun.apsaradb.dbaasmetaapi.model.EndpointListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.MigrateDBInstanceImpl;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.migrate.MigrateDBInstanceAvzService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodAvzSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.poddefault.action.support.PodTemplateHelper;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.val;
import lombok.var;

@Service
public class ModifyDBInstanceCpuArchService {
    @Autowired
    protected MySQLServiceImpl mySQLService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected CommonProviderService commonProviderService;
    @Autowired
    protected PodParameterHelper podParameterHelper;
    @Autowired
    protected PodTemplateHelper podTemplateHelper;
    @Autowired
    protected MysqlParamSupport paramSupport;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    protected WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    protected MysqlParamSupport mysqlParamSupport;
    @Resource
    protected KmsService kmsService;
    @Resource
    protected PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private BaseModifyDBInstanceService baseModifyDBInstanceService;
    @Resource
    private PodAvzSupport podAvzSupport;
    @Autowired
    CustinsParamService custinsParamService;
    @Resource
    MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    protected DBaasMetaService metaService;
    @Autowired
    protected InstanceIDao instanceIDao;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    MigrateDBInstanceAvzService migrateDBInstanceAvzService;

    protected static final LogAgent logger = LogFactory.getLogAgent(MigrateDBInstanceImpl.class);


    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = paramSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        logger.info("Request {} Action {}, params {}", requestId, "ModifyDBInstanceCpuArchService", JSON.toJSONString(params));

        AllocateTmpResourceResult allocateResult = new AllocateTmpResourceResult();

        boolean isSuccess = false;
        try {
            PodModifyInsParam modifyInsParam = baseModifyDBInstanceService.initPodModifyInsParam(params);

            //basic check
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);

            // 查询目标规格的serviceSpec，可能是x86/ARM
            // 校验变配小版本>=实例当前小版本 aone 47055364
            String sourceMinorVersion = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(), "minor_version");
            String targetServiceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                    sourceMinorVersion,
                    modifyInsParam.getReplicaSetMeta().getBizType(),
                    modifyInsParam.getDbType(),
                    modifyInsParam.getDbVersion(),
                    "MySQL",
                    KIND_CODE_NEW_ARCH,
                    modifyInsParam.getTargetInstanceLevel(),
                    modifyInsParam.getTargetDiskType(),
                    modifyInsParam.isDHG(),
                    true);
            modifyInsParam.setTargetComposeTag(targetServiceSpecTag);

            // 可用区迁移走现有流程
            boolean isMigratingAVZ = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false"));
            if(isMigratingAVZ){
                logger.info("Request {} migrate to another avz when process arch change", requestId);
                return migrateDBInstanceAvzService.doActionRequest(custins, params);
            }

            if (replicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
                logger.error("db instance is not active.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            }

            boolean isCloudBox = podAvzSupport.isCloudBoxAz(modifyInsParam.getReplicaSetMeta().getResourceGroupName());
            if (isCloudBox) {
                logger.error("cloudbox ins un-support arch change");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
            }

            // TODO: 集群版暂不支持可用区迁移操作，等支持后再放开
            if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "cluster is not supported yet!");
            }

            // 只读检查主实例状态
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), false);
                if (primaryReplicaSet.getStatus() != ReplicaSet.StatusEnum.ACTIVATION || primaryReplicaSet.getLockMode() != ReplicaSet.LockModeEnum.NOLOCK) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_MASTER_DBINSTANCE_STATUS);
                }
            }

            if (!PodParameterHelper.isAliYun(replicaSet.getBizType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "biz type " + replicaSet.getBizType() + " not supported yet!");
            }

            // TODO：第一阶段单变双不支持， 后端任务流已经适配
            if (InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSet.getCategory())
                    && InstanceLevel.CategoryEnum.STANDARD.toString().equals(modifyInsParam.getTargetInstanceLevel().getCategory())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            }

            if (!ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType()) && !ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "db type " + replicaSet.getInsType() + " not supported yet!");
            }

            if (!kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            // 校验变配参数是否合法
            if (modifyInsParam.isDiskTypeChange()) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, "disk type change is not supported");
            }

            // 校验参数模板 ParameterGroupId，xengine不支持架构变更
            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
            if (labels.containsKey(CUSTINS_PARAM_NAME_PARAM_GROUP_ID)) {
                String paramGroupId = labels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
                String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, replicaSet.getId().intValue());
                podCommonSupport.checkARMSupportXEngineMinorVersion(serviceSpecTag, paramGroupId);
            }


            // 申请资源
            allocateResource(requestId, replicaSet, modifyInsParam, allocateResult);
            ReplicaSet tmpReplicaSet = allocateResult.getReplicaSet();

            // 下发迁移任务
            Integer taskIdInt = addTransferTask(requestId, replicaSet, tmpReplicaSet, modifyInsParam, params);

            // 更新实例状态
            dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.CLASS_CHANGING.toString());
            if (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSet.getInsType())) {
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getPrimaryInsName(), ReplicaSet.StatusEnum.INS_MAINTAINING.toString());
            }
            isSuccess = true;

            // build response
            Map<String, Object> data = new HashMap<>();
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", replicaSet.getName());
            data.put("TaskId", taskIdInt);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            logger.error(requestId + " CommonProvider error: " + e.getMessage(), e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 处理失败时释放资源
            if (allocateResult.isAllocated() && !isSuccess) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(
                            requestId, allocateResult.getResourceRequest().getReplicaSetName());
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("resource resource for transfer failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    private void allocateResource(String requestId, ReplicaSet replicaSet, PodModifyInsParam modifyInsParam, AllocateTmpResourceResult result) throws Exception {
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();

        // 申请资源
        CustInstanceDO custins = modifyInsParam.getCustins();

        String tmpReplicaSetName = String.format("tmp-%s-%s", modifyInsParam.getDbInstanceName(), System.currentTimeMillis() / 1000L);
        String connectionString = CheckUtils.checkValidForConnAddrCust(tmpReplicaSetName);
        AVZInfo targetAvzInfo = modifyInsParam.getAvzInfo();

        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            replicaSetResourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }

        replicaSetResourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .insType(ReplicaSet.InsTypeEnum.MAIN.toString())
                .replicaSetName(tmpReplicaSetName)
                .domainPrefix(connectionString)

                // 版本规格
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .composeTag(modifyInsParam.getTargetComposeTag())
                .bizType(custins.getBizType())
                .catagory(modifyInsParam.getTargetInstanceLevel().getCategory().getValue())
                .classCode(modifyInsParam.getTargetClassCode())

                // 磁盘资源
                .storageType(modifyInsParam.getTargetDiskType())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                .allocateDisk(false)
                .burstingEnabled(modifyInsParam.isBurstingEnabled())
                .provisionedIops(modifyInsParam.getProvisionedIops())

                // 网络资源
                //只有跨可用区才申请vpc
                .connType(CONN_TYPE_PHYSICAL)
                .vswitchID(mysqlParamSupport.getParameterValue(modifyInsParam.getParams(), ParamConstants.VSWITCH_ID, null))
                .cloudInstanceIp(null)
                .vpcInstanceId(null)
                //反向VPC的资源申请下沉到任务流
                .ignoreCreateVpcMapping(true)
                .eniDirectLink(false)

                // 地域
                .subDomain(targetAvzInfo.getRegion())
                .regionId(targetAvzInfo.getRegionId());


        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (isTargetSingleTenant) {
            // 单租户场景
            replicaSetResourceRequest.singleTenant(true);
        }

        // 资源模板相关
        replicaSetResourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());
        podTemplateHelper.setSpecSchedulerConfigSpread(replicaSetResourceRequest, modifyInsParam.getDbInstanceName());

        val diskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        val extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        false, // 云上无影响
                        diskSizeGB
                );

        // 需要迁移的角色
        List<Replica.RoleEnum> roles = new ArrayList<>();
        roles.add(Replica.RoleEnum.MASTER);
        if (CATEGORY_STANDARD.equalsIgnoreCase(modifyInsParam.getTargetInstanceLevel().getCategory().getValue())) {
            roles.add(Replica.RoleEnum.SLAVE);
        }

        val replicas = new ArrayList<ReplicaResourceRequest>();
        for (val rr : roles) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            replicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaResourceRequest.setRole(rr.getValue());

            // 设置可用区
            String zoneId = "";
            switch (rr) {
                case MASTER:
                    zoneId = getMasterZoneId(modifyInsParam);
                    break;
                case SLAVE:
                    zoneId = getSlaveZoneId(modifyInsParam);
                    break;
                default:
                    logger.error("unsupported replica role {}", rr.getValue());
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            replicaResourceRequest.setZoneId(zoneId);

            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        result.setResourceRequest(replicaSetResourceRequest);

        boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
        if(isArm){
            //如果是arm架构需要指定arch
            replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.ARM);
            replicaSetResourceRequest.setUseAsiCluster(true);
        }

        // 申请资源
        logger.info("allocate resoure for {}, request body: {}", tmpReplicaSetName, JSON.toJSONString(replicaSetResourceRequest));
        boolean isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, tmpReplicaSetName, replicaSetResourceRequest);
        if(isAllocate){
            Map<String, String> labels = new HashMap<>();
            //open double write as x86 not support close double write, used for com.aliyun.app.activityprovider.base.param.param.BuildParamParameter.ArchChangedMatcher
            labels.put(PodDefaultConstants.PARAM_ARCH_CHANGED, "1");
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), labels);
        }
        result.setAllocated(isAllocate);

        // 更新临时实例参数
        ReplicaSet tmpReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, tmpReplicaSetName, null);
        tmpReplicaSet.setPrimaryInsName(custins.getInsName());
        tmpReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, tmpReplicaSetName, tmpReplicaSet);

        // 只读实例配置白名单同步label
        podParameterHelper.setReadInsSgLabel(requestId, replicaSet, tmpReplicaSetName);
        Map<String, String> tmpInsLabels = new HashMap<>();
        if(isArm){
            //arch arm, 设置这个label的目的是因为刷白不支持源实例和目标实例是不同的cpu架构，会导致arm实例上刷白镜像拉取成x86的
            tmpInsLabels.put(PodDefaultConstants.PARAM_ARCH_LABEL, ReplicaSetResourceRequest.ArchEnum.ARM.getValue());
        }
        dBaasMetaService.getDefaultClient().updateReplicaSetLabels(modifyInsParam.getRequestId(), tmpReplicaSetName, tmpInsLabels);

        //单变双新增dispense_mode, master_location, slave_location, multi_avz_ex_param
        if (InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSet.getCategory()) &&
                InstanceLevel.CategoryEnum.STANDARD.toString().equals(modifyInsParam.getTargetInstanceLevel().getCategory().getValue())) {
            List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, tmpReplicaSetName,null, 6, null, null).getItems();
            Replica masterReplica = replicaList.stream().filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER)).findFirst().get();
            Replica slaveReplica = replicaList.stream().filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE)).findFirst().get();
            final int tmpCustinsId = tmpReplicaSet.getId().intValue();
            custinsParamService.setDispenseMode(tmpCustinsId, ParamConstants.DispenseMode.MultiAVZDispenseMode);
            custinsParamService.setMasterLocation(tmpCustinsId, masterReplica.getSubDomain());
            String[] slaveLocationArr = new String[1];
            slaveLocationArr[0] = slaveReplica.getSubDomain();
            custinsParamService.setSlaveLocations(tmpCustinsId, slaveLocationArr);
            custinsParamService.setMultiAVZExParam(tmpCustinsId, JSON.toJSONString(targetAvzInfo.getMultiAVZExParamDO()));
        }
        result.setReplicaSet(tmpReplicaSet);
    }



    private Integer addTransferTask(String requestId, ReplicaSet srcReplicaSet, ReplicaSet destReplicaSet,
                                    PodModifyInsParam modifyInsParam, Map<String, String> params) throws Exception {
        // get replica info
        ReplicaListResult srcReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, srcReplicaSet.getName(), null, 6, null, null);
        ReplicaListResult destReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, destReplicaSet.getName(), null, 6, null, null);
        Long srcMasterReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long destMasterReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst().get().getId();
        Long srcSlaveReplicaId = 0L;
        Long destSlaveReplicaId = 0L;
        if (InstanceLevel.CategoryEnum.STANDARD.toString().equals(srcReplicaSet.getCategory())) {
            srcSlaveReplicaId = srcReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
            destSlaveReplicaId = destReplicas.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst().get().getId();
        }

        // step 1: 写迁移记录
        CustInstanceDO srcCustins = custinsService.getCustInstanceByCustinsId(srcReplicaSet.getId().intValue());
        TransListDO transList = new TransListDO(srcCustins, InstanceSupport.TRANS_STATUS_REMOVE_NB, InstanceSupport.TRANS_TYPE_REMOVE);
        transList.setsHinsid1(srcMasterReplicaId.intValue());
        transList.setsHinsid2(srcSlaveReplicaId.intValue());

        transList.setdCinsid(destReplicaSet.getId().intValue());
        InstanceLevelDO targetLevel = instanceService.getInstanceLevelByClassCode(destReplicaSet.getClassCode(), srcCustins.getDbType(), srcCustins.getDbVersion(), null, null);
        transList.setdLevelid(targetLevel.getId());
        transList.setdDisksize((long) (modifyInsParam.getTargetDiskSizeGB() * 1024));
        transList.setdHinsid1(destMasterReplicaId.intValue());
        transList.setdHinsid2(destSlaveReplicaId.intValue());

        transList.setSwitchTime(modifyInsParam.getSwitchTime());

        String comment = "Arch change";
        boolean isModifySpec = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, "migrateAzModifySpec", "false"));
        if (isModifySpec) {
            comment = "MigrateInsAvzAndModifySpec";
        }
        transList.setComment(comment);

        this.instanceIDao.createTransList(transList);

        // step 2: 设置任务参数
        JSONObject taskParam = new JSONObject();
        taskParam.put("requestId", requestId);
        taskParam.put(CustinsSupport.TRANS_ID, transList.getId());
        taskParam.put(CustinsSupport.SWITCH_KEY, modifyInsParam.getSwitchInfo());
        taskParam.put("replicaSetName", srcReplicaSet.getName());
        taskParam.put("transTaskId", transList.getId());
        taskParam.put("switchInfo", modifyInsParam.getSwitchInfo());
        taskParam.put("srcReplicaSetName", srcReplicaSet.getName());
        taskParam.put("destReplicaSetName", destReplicaSet.getName());
        taskParam.put("srcReplicaId", srcMasterReplicaId);
        taskParam.put("destReplicaId", destMasterReplicaId);
        taskParam.put("srcReplicaSetResourceGroupName", srcReplicaSet.getResourceGroupName());
        taskParam.put("destReplicaSetResourceGroupName", destReplicaSet.getResourceGroupName());
        taskParam.put("srcDiskType", modifyInsParam.getSrcDiskType());
        taskParam.put("targetDiskType", modifyInsParam.getTargetDiskType());
        taskParam.put("srcPerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
        taskParam.put("targetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
        // 变配相关参数下发
        taskParam.put("destClassCode", modifyInsParam.getTargetClassCode());
        if (modifyInsParam.isDiskSizeChange()) {
            taskParam.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
        }

        // step3: 下发任务
        String taskKey = null;
        if (InstanceLevel.CategoryEnum.BASIC.toString().equals(srcReplicaSet.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.toString().equals(destReplicaSet.getCategory())) {
            if (ReplicaSet.InsTypeEnum.READONLY.equals(srcReplicaSet.getInsType())) {
                taskKey = PodDefaultConstants.TASK_MODIFY_READ_INS_BASIC_TO_HA_ARCH_CHANGE;
            }else {
                taskKey = PodDefaultConstants.TASK_MODIFY_INS_BASIC_TO_HA_ARCH_CHANGE;
            }
        }else {
            if (ReplicaSet.InsTypeEnum.READONLY.equals(srcReplicaSet.getInsType())) {
                taskKey = PodDefaultConstants.TASK_MODIFY_READ_INS_ARCH_CHANGE;
            }else {
                taskKey = PodDefaultConstants.TASK_MODIFY_INS_ARCH_CHANGE;
            }
        }

        String domain = PodDefaultConstants.DOMAIN_MYSQL;
        Object taskId = workFlowService.dispatchTask("custins", srcReplicaSet.getName(), domain, taskKey, taskParam.toString(), 0);

        Integer taskIdInt = Double.valueOf(taskId.toString()).intValue();
        this.instanceIDao.updateTransTaskIdById(transList.getId(), taskIdInt);

        return taskIdInt;
    }

    private String getMasterZoneId(PodModifyInsParam modifyInsParam) throws ApiException, RdsException {
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                        null, 6, null, null);
        var masterReplicaOptional = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.MASTER)).findFirst();
        if (masterReplicaOptional.isPresent()) {
            logger.info("Action {}, zoneId {}", "ModifyDBInstanceCpuArchService::getMasterZoneId", masterReplicaOptional.get().getZoneId());
            return masterReplicaOptional.get().getZoneId();
        } else {
            logger.error("failed to get master zoneId from replica. instanceName= {}",modifyInsParam.getDbInstanceName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
    }
    private String getSlaveZoneId(PodModifyInsParam modifyInsParam) throws ApiException, RdsException {
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(),
                        null, 6, null, null);
        var slaveReplicaOptional = listReplicasInReplicaSet.getItems().stream().filter(x -> x.getRole().equals(Replica.RoleEnum.SLAVE)).findFirst();
        if (slaveReplicaOptional.isPresent()) {
            logger.info("Action {}, zoneId {}", "ModifyDBInstanceCpuArchService::getSlaveZoneId", slaveReplicaOptional.get().getZoneId());
            return slaveReplicaOptional.get().getZoneId();
        } else {
            logger.error("failed to get slave zoneId from replica. instanceName= {}",modifyInsParam.getDbInstanceName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
        }
    }
}
