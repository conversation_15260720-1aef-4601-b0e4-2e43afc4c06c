package com.aliyun.dba.poddefault.action.service.createReadOnly;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.gdnmetaapi.model.InstanceMember;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.CreateReadDBInstanceImpl;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.DuckDBService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.ACCESS_ID_RESOURCE;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.READ_ONLY_STATUS;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED;
import static com.aliyun.dba.support.property.ParamConstants.IO_ACCELERATION_ENABLED_ON;

@Service
public abstract class BaseCreateReadOnlyInsService {
    private static final LogAgent logger = LogFactory.getLogAgent(CreateReadDBInstanceImpl.class);

    @Resource
    public ReplicaSetService replicaSetService;
    @Resource
    public MysqlParamSupport paramSupport;
    @Resource
    public DBaasMetaService metaService;
    @Resource
    public PodAvzSupport avzSupport;
    @Resource
    public CommonProviderService commonProviderService;
    @Resource
    public WorkFlowService workFlowService;
    @Resource
    public GdnInstanceService gdnInstanceService;
    @Resource
    public AligroupService aligroupService;
    @Resource
    public MysqlParameterHelper mysqlParameterHelper;
    @Resource
    public PodTemplateHelper podTemplateHelper;
    @Resource
    public MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    public PodParameterHelper podParameterHelper;
    @Resource
    public IpWhiteListService ipWhiteListService;
    @Resource
    public PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    protected PodAvzSupport podAvzSupport;
    @Resource
    public PodCommonSupport podCommonSupport;
    @Resource
    public DuckDBService duckDBService;

    public Object doCreateReadOnly(CreateReadOnlyInsRequest request, Map<String, String> params) throws RdsException {
        try {
            //step 1: build special request
            request = buildSpecialRequest(request, params);

            //step 2: do basic check
            Map<String, Object> checkResult = doBasicCheck(request);
            if (MapUtils.isNotEmpty(checkResult)) {
                return checkResult;
            }

            //step 3: do special check
            doSpecialCheck(request);

            //step 4: build allocate request
            ReplicaSetResourceRequest replicaSetResourceRequest = buildAllocateRequest(request);

            //step 5: build special allocate request
            replicaSetResourceRequest = buildSpecialAllocateRequest(replicaSetResourceRequest, request);
            //step 6: allocate resource
            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            try {// 申请实例资源
                logger.info("allocateReplicaSetResourceV1:{}", JSONObject.toJSONString(replicaSetResourceRequest));
                isAllocate = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(request.getRequestId(), request.getReadInsName(), replicaSetResourceRequest);
                ReplicaSet readReplicaSet = metaService.getDefaultClient().getReplicaSet(request.getRequestId(), request.getReadInsName(), false);

                // step 7: create gdn instance member
                if (request.getIsCreatingGdnInstance()) {
                    createGdnInstanceMember(request);
                }

                // step 8: sync account meta
                syncDbAccountMeta(request);

                // step 9: sync instance lables
                addInstanceLables(request, readReplicaSet, replicaSetResourceRequest.getComposeTag());

                // step 10: Dispatch task
                Object taskId = dispatchTask(request);

                return taskId;
            } catch (Exception e) {
                logger.error(request.getRequestId() + " Exception: ", e);
                isAllocate = false;
                if (e instanceof com.aliyun.apsaradb.activityprovider.ApiException) {
                    return CommonProviderExceptionUtils.resourceWrapper(request.getRequestId(), (com.aliyun.apsaradb.activityprovider.ApiException)e);
                }
                logger.error(request.getRequestId() + " Exception: {}", e.getMessage());
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败或者其它异常的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(request.getRequestId(), request.getReadInsName());
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        //ignore
                        logger.error(request.getRequestId() + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }

        } catch (RdsException re) {
            logger.error(request.getRequestId() + " RdsException: ", re);
            throw new RdsException(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(request.getRequestId() + " Dbaas MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed: " + e.getMessage()});
        } catch (com.aliyun.apsaradb.gdnmetaapi.ApiException e) {
            logger.error(request.getRequestId() + " Gdn MetaApi error: " + e.getResponseBody());
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Request gdn meta failed: " + e.getMessage()});
        } catch (Exception ex) {
            logger.error(request.getRequestId() + " Exception: ", ex);
            throw new RdsException(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * build response
     * @param request
     * @param taskId
     * @return
     */
    private Object buildResponse(CreateReadOnlyInsRequest request, Object taskId){
        Map<String, Object> data = new HashMap<>();
        data.put("TaskId", taskId);
        data.put("DBInstanceName", request.getDbInstanceName());
        data.put("ReadDBInstanceName", request.getReadInsName());
        return data;
    }

    /**
     * submit workflow
     * @param request
     * @return
     */
    private Object dispatchTask(CreateReadOnlyInsRequest request) throws Exception {
        String domain = "mysql";
        String taskKey = getTaskKey(request);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", request.getRequestId());
        jsonObject.put("replicaSetName", request.getReadInsName());
        jsonObject.put("primaryReplicaSetName", request.getDbInstanceName());
        jsonObject.put("destDiskSizeMB", request.getDiskSize() * 1024);
        jsonObject.put("primaryDiskSizeMB", request.getPrimaryReplicaSet().getDiskSizeMB()  );
        if (request.getIsArmIns()) {
            jsonObject.put("instructionSetArch", "arm");
        }
        jsonObject.put("isAnalyticReadOnlyIns", request.isAnalyticReadOnlyIns());
        jsonObject.put("performanceLevel", request.getPerformanceLevel());
        String parameter = jsonObject.toJSONString();
        return workFlowService.dispatchTask("custins", request.getReadInsName(), domain, taskKey, parameter, 0);
    }

    /**
     * put lables tool
     *
     * @param labels
     * @param key
     * @param value
     * @param skipWithEmpty
     */
    public void putLabels(Map<String, String> labels, String key, String value, boolean skipWithEmpty) {
        if (StringUtils.isEmpty(value) && skipWithEmpty) {
            return;
        }
        labels.put(key, value);
    }

    /**
     * add instance lables
     */
    private void addInstanceLables(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet, String composeTag) throws ApiException, RdsException {
        Map<String, String> primaryInsLabels = metaService.getRegionClient(request.getCenterRegionId()).listReplicaSetLabels(request.getRequestId(), request.getDbInstanceName());
        Assert.notEmpty(primaryInsLabels, "primaryInsLabels not null");

        // 同步主实例标签
        Map<String, String> labels = new HashMap<>(primaryInsLabels);

        //指定实例资源调度模板的写入实例和模版名关联
        putLabels(labels, CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, request.getRsTemplateName(), true);

        boolean isArmForPrimaryIns = PodCommonSupport.isArm(request.getPrimaryInsInstanceLevel());
        if (request.getIsArmIns() ^ isArmForPrimaryIns) {
            putLabels(labels, PodDefaultConstants.PARAM_ARCH_CHANGED, "1", false);
        }

        // 写优化参数传递
        if (StringUtils.isNoneBlank(request.getOptimizedWritesInfo())) {
            labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, request.getOptimizedWritesInfo());
        }

        if (request.isAnalyticReadOnlyIns()) {
            labels.put("isAnalyticReadOnlyIns", String.valueOf(true));
            labels.put(CUSTINS_PARAM_NAME_PARAM_GROUP_ID, duckDBService.getAnalyticReadOnlyInsParamGroupId(request, readReplicaSet));
        }else {
            labels.remove("isAnalyticReadOnlyIns");
        }

        if (CloudDiskCompressionHelper.isCompressionModeOn(request.getCompressionMode())) {
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE, request.getCompressionMode());
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_RATIO, String.valueOf(request.getCompressionRatio()));
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION, String.valueOf(CloudDiskCompressionHelper.convertDiskSizeGBToMB(request.getDiskSizeGBBeforeCompression())));
        }


        // 小版本信息
        putLabels(labels, "minor_version", minorVersionServiceHelper.resetReplicaSetMinorVersion(request.getRequestId(), request.getReadInsName()), true);
        //composeTag不能继承主实例，防止被使用
        putLabels(labels, "composeTag", composeTag, true);
        //IO加速不能继承主实例
        if (podCommonSupport.isIoAccelerationEnabled(request.getGeneralCloudDisk())) {
            putLabels(labels, IO_ACCELERATION_ENABLED, IO_ACCELERATION_ENABLED_ON, true);
        } else {
            labels.remove(IO_ACCELERATION_ENABLED);
        }


        if (request.getIsCreatingGdnInstance()) {
            putLabels(labels, "CenterReplicaSetName", request.getDbInstanceName(), false);
            putLabels(labels, "CenterRegionId", request.getCenterRegionId(), false);
            putLabels(labels, "clusterName", request.getGdnInstanceName(), false);
        }

        //设置是否单租户标
        labels.put(PodDefaultConstants.REPLICA_SET_SINGLE_TENANT, String.valueOf(request.getIsSingleTenant()));

        putLabels(labels, "master_location", request.getAvzInfo().getMultiAVZExParamDO().getMasterLocation(), false);
        putLabels(labels, "multi_avz_ex_param", JSON.toJSONString(request.getAvzInfo().getMultiAVZExParamDO()), false);

        //高可用maz更新lave标签，基础版移除
        if (InstanceLevel.CategoryEnum.STANDARD.toString().equalsIgnoreCase(readReplicaSet.getCategory())){
            String slaveLocation = String.join(",",request.getAvzInfo().getMultiAVZExParamDO().getSlaveLocations());
            putLabels(labels, "slave_location", slaveLocation, false);
        } else {
            labels.remove("slave_location");
        }


        labels.remove("migrateStage");

        // 移除开关类标签，功能类不继承
        labels.remove("ssl_enabled");
        labels.remove("enable_sql_log");

        // 移除主实例锁信息
        labels.remove("WF_LOCK");

        // 移除主实例 SQL 洞察相关参数
        labels.remove("sql_retention");
        labels.remove("sql_visible_time");
        labels.remove("enable_das_pro");

        // 资源类标签不继承
        labels.remove(PodDefaultConstants.PARAM_ARCH_LABEL);
        labels.remove(PodDefaultConstants.COMMON_K8S_CLUSTER_LABEL);


        // 移除主实例autopl配置参数，已在common组件中同步更新，不需要覆盖
        labels.remove(CustinsParamSupport.CUSTINS_PARAM_NAME_AUTOPL_PROVISIONED_IOPS);
        labels.remove(CustinsParamSupport.CUSTINS_PARAM_NAME_AUTOPL_BURSTING_ENABLED);

        // 移除实例只读态参数
        labels.remove(READ_ONLY_STATUS);

        addSpecialInstanceLables(request, readReplicaSet, composeTag, labels);

        // 同步实例标签
        metaService.getDefaultClient().updateReplicaSetLabels(request.getRequestId(), request.getReadInsName(), labels);
    }


    /**
     * 同步原实例的账户信息, 主要是系统账户
     *
     * @param request
     * @throws ApiException
     */
    protected void syncDbAccountMeta(CreateReadOnlyInsRequest request) throws ApiException {
        Account queryExample = new Account();
        //只同步active状态的账号
        queryExample.setStatus(Account.StatusEnum.ACTIVE);
        AccountListResult accountListResult = metaService.getRegionClient(request.getCenterRegionId()).listReplicaSetAccountsByExample(request.getRequestId(), request.getDbInstanceName(), null, null, queryExample);
        List<Account> accounts = CollectionUtils.isNotEmpty(accountListResult.getItems()) ? accountListResult.getItems() : new ArrayList<>();
        for (Account account : accounts) {
            metaService.getDefaultClient().createAccountForReplicaSet(request.getRequestId(), request.getReadInsName(), account);
        }
    }

    /**
     * create gdn instance member
     *
     * @param request
     * @throws RdsException
     * @throws com.aliyun.apsaradb.gdnmetaapi.ApiException
     */
    protected void createGdnInstanceMember(CreateReadOnlyInsRequest request) throws RdsException, com.aliyun.apsaradb.gdnmetaapi.ApiException {
        InstanceMember instanceMember = new InstanceMember();
        instanceMember.setInsName(request.getGdnInstanceName());
        instanceMember.setService(request.getPrimaryReplicaSet().getService());
        instanceMember.setServiceVersion(request.getDbVersion());
        instanceMember.setMemberName(request.getReadInsName());
        instanceMember.setMemberGroupId(request.getDbInstanceName());  // 主实例 ID 作为 groupID
        instanceMember.setMemberKind(InstanceMember.MemberKindEnum.REPLICASET);
        instanceMember.setMemberRegion(request.getAvzInfo().getRegionId());
        instanceMember.setRole(ReplicaSet.InsTypeEnum.READONLY.toString());
        instanceMember.setStatus(ReplicaSet.StatusEnum.CREATING.toString());
        gdnInstanceService.createGdnInstanceMember(request.getRequestId(), request.getGdnInstanceName(), instanceMember);
    }

    /**
     * allocate resource request
     *
     * @param request
     * @return
     * @throws Exception
     */
    private ReplicaSetResourceRequest buildAllocateRequest(CreateReadOnlyInsRequest request) throws Exception {
        InstanceLevel instanceLevel = metaService.getDefaultClient().getInstanceLevel(request.getRequestId(), request.getDbType(), request.getDbVersion(), request.getClassCode(), null);
        Replica.RoleEnum[] roleEnumList = PodCommonSupport.getRoles(request.getDbEngine(), instanceLevel, true, null);
        // 根据主实例，获取category
        ReplicaSetResource primaryReplicaSetResource = metaService.getRegionClient(request.getCenterRegionId())
                .getReplicaSetBundleResource(request.getRequestId(), request.getDbInstanceName());
        Map<String, String> primaryInsLabels = primaryReplicaSetResource.getReplicaSet().getLabels();
        String category = primaryReplicaSetResource.getReplicaSet().getCategory();
        InstanceLevel primaryInstanceLevel = metaService.getRegionClient(request.getCenterRegionId()).getInstanceLevel(
                request.getRequestId(),
                request.getPrimaryReplicaSet().getService(),
                request.getPrimaryReplicaSet().getServiceVersion(),
                request.getPrimaryReplicaSet().getClassCode(),
                null);

        // Buid replicaSet resource
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        replicaSetResourceRequest.setUserId(request.getBid());
        replicaSetResourceRequest.setUid(request.getUid());
        replicaSetResourceRequest.setPort(request.getConnStrPortStr());
        replicaSetResourceRequest.setReplicaSetName(request.getReadInsName());
        replicaSetResourceRequest.setInsType(request.getInsTypeDesc());
        replicaSetResourceRequest.setCatagory(category);
        replicaSetResourceRequest.setEniDirectLink(getIsENI(request));
        replicaSetResourceRequest.setComment(request.getComment());

        // build labels
        replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, request.getOrderId());
        replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, request.getAccessId());

        ScheduleTemplate replicaSetTemplate = getReplicaSetTemplate(request, instanceLevel);
        replicaSetResourceRequest.setScheduleTemplate(replicaSetTemplate);
        // 如果是创建跨地域的只读, 不要设置主实例 ID, 不在一个单元, 通过实例参数和 GDN 管控来维护
        if (!request.getIsCreatingGdnInstance()) {
            replicaSetResourceRequest.setPrimaryInsName(request.getDbInstanceName());
        }

        if (request.getIsArmIns()) {
            replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.valueOf(request.getInstructionSetArch()));
        }


        replicaSetResourceRequest.setDbType(request.getPrimaryReplicaSet().getService());
        replicaSetResourceRequest.setDbVersion(request.getDbVersion());
        replicaSetResourceRequest.setConnType(request.getConnType());
        replicaSetResourceRequest.setDedicatedHostGroupId(request.getClusterName());
        replicaSetResourceRequest.setClassCode(request.getClassCode());
        replicaSetResourceRequest.setStorageType(request.getDiskType());
        replicaSetResourceRequest.setDiskSize(request.getDiskSize());
        replicaSetResourceRequest.setBurstingEnabled(request.isBurstingEnabled());
        replicaSetResourceRequest.setProvisionedIops(request.getProvisionedIops());
        replicaSetResourceRequest.setGeneralCloudDisk(request.getGeneralCloudDisk());
        // 写优化参数完全继承主实例的参数 buildCommonRequest中进行的初始化
        replicaSetResourceRequest.setInitOptimizedWrites(podCommonSupport.isInitOptimizedWrites(request.getOptimizedWritesInfo()));
        replicaSetResourceRequest.setBizType(request.getBizType());
        replicaSetResourceRequest.setSubDomain(request.getAvzInfo().getRegion());
        replicaSetResourceRequest.setVpcId(request.getVpcId());
        replicaSetResourceRequest.setVswitchID(request.getVSwitchId());
        replicaSetResourceRequest.setCloudInstanceIp(request.getIPAddress());
        replicaSetResourceRequest.setVpcInstanceId(request.getVpcInstanceId());

        replicaSetResourceRequest.setIgnoreCreateVpcMapping(true);
        if (request.getIsXdbEngine()) {
            // 指定endpoint绑定的角色是learner
            replicaSetResourceRequest.setEndpointReplicaRole(Replica.RoleEnum.LEARNER.toString());
        }

        // 云盘加密
        if (primaryInsLabels.containsKey(PodDefaultConstants.ENCRYPTION_KEY_LABEL)) {
            replicaSetResourceRequest.setEncryptionKey(primaryInsLabels.get(PodDefaultConstants.ENCRYPTION_KEY_LABEL));
            replicaSetResourceRequest.setEncryptionType(primaryInsLabels.get(PodDefaultConstants.ENCRYPTION_TYPE_LABEL));
        }

        // 为只读实例绑定composeTag
        // 只读实例的预期的composeTag与主实例相同，但由于规格都是高可用规格，所以需要用主实例的规格去获取tag
        String primaryMinorVersion = getTargetMinorVersionForReadIns(request, primaryInsLabels);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(primaryMinorVersion,
                ReplicaSet.BizTypeEnum.fromValue(request.getBizType()),
                request.getDbType(),
                request.getDbVersion(),
                request.getDbEngine(),
                KIND_CODE_NEW_ARCH,
                primaryInstanceLevel,
                request.getDiskType(),
                request.getIsDhg(),
                request.getIsArmIns(),
                true,
                request.isAnalyticReadOnlyIns()); //需要查询到下线版本
        replicaSetResourceRequest.setComposeTag(serviceSpecTag);

        // todo：后续需要更新
        // IO加速的内核版本必须大于20221231
        if (podCommonSupport.isIoAccelerationEnabled(request.getGeneralCloudDisk())) {
            podCommonSupport.checkIoAccelerationSupportedMinorVersion(replicaSetResourceRequest.getComposeTag());
        }

        if (request.getIsArmIns()) {
            //检查ARM只读实例是否支持xengine
            String primaryParamGroupId = primaryInsLabels.get(CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
            if (StringUtils.isNotBlank(primaryParamGroupId)) {
                podCommonSupport.checkARMSupportXEngineMinorVersion(replicaSetResourceRequest.getComposeTag(), primaryParamGroupId);
            }
        }

        // 只读实例容灾策略：单机房容灾
        List<ReplicaResourceRequest> replicas = new ArrayList<>();

        logger.info("readInsReplicaCount is {}", request.getReadInsReplicaCount());


        if (!getAllocateDiskflag(request)) {
            replicaSetResourceRequest.setAllocateDisk(false);
        }

        PodScheduleTemplate podScheduleTemplate = getPodScheduleTemplate(request);

        // 基础版及高可用版，count=1，2
        // 高可用只读， slave尝试从maz字段里获取slave的可用区， 若没有则默认使用master的
        for (int i = 0; i < request.getReadInsReplicaCount(); i++) {
            String zoneId = request.getAvzInfo().getMasterZoneId();
            try{
                if (roleEnumList[i] == Replica.RoleEnum.SLAVE){
                    String slaveZoneId = request.getAvzInfo().getMultiAVZExParamDO().getSlaveAvailableZoneInfo().get(0).getZoneID();
                    if (!StringUtils.isBlank(slaveZoneId)){
                        zoneId = slaveZoneId;
                    }
                }
            } catch (Exception e){
                logger.warn("maz get slave info failed, use default master zoneId");
            }
            ReplicaResourceRequest replicaResourceRequest = buildReplicaResourceRequestUseZoneId(
                    request.getReadInsName(),
                    request.getDiskSize(),
                    request.getDiskType(),
                    request.getPerformanceLevel(),
                    request.getClassCode(),
                    zoneId,
                    roleEnumList[i],
                    podScheduleTemplate,
                    request.getIsSingleTenant(),
                    ReplicaSet.BizTypeEnum.fromValue(request.getBizType()));
            replicas.add(replicaResourceRequest);
        }
        replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
        replicaSetResourceRequest.setSingleTenant(request.getIsSingleTenant());

        podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);
        return replicaSetResourceRequest;
    }

    /**
     * get task key
     * @param request
     * @return
     */
    public abstract String getTaskKey(CreateReadOnlyInsRequest request);

    /**
     * add special instance labels
     *
     * @param request
     * @param readReplicaSet
     * @param labels
     */
    public abstract void addSpecialInstanceLables(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet, String composeTag, Map<String, String> labels) throws ApiException;


    /**
     * biztype为aliyun的云盘版本的实例 只申请计算资源 不申请实例
     *
     * @param request
     * @return
     */
    public abstract Boolean getAllocateDiskflag(CreateReadOnlyInsRequest request);

    /**
     * special allocate resource request param
     *
     * @param replicaSetResourceRequest
     * @param request
     * @return
     * @throws ApiException
     */
    public abstract ReplicaSetResourceRequest buildSpecialAllocateRequest(ReplicaSetResourceRequest replicaSetResourceRequest, CreateReadOnlyInsRequest request) throws ApiException;

    /**
     * be used to get replica level schedule template
     *
     * @param request
     * @return
     * @throws Exception
     */
    public abstract PodScheduleTemplate getPodScheduleTemplate(CreateReadOnlyInsRequest request) throws Exception;

    /**
     * replica set level schedule template
     *
     * @param request
     * @param instanceLevel
     * @return
     * @throws Exception
     */
    public abstract ScheduleTemplate getReplicaSetTemplate(CreateReadOnlyInsRequest request, InstanceLevel instanceLevel) throws Exception;

    /**
     * is eni or not
     *
     * @param request
     * @return
     */
    public abstract Boolean getIsENI(CreateReadOnlyInsRequest request);

    /**
     * specical sub check
     *
     * @param request
     * @throws RdsException
     */
    public abstract void doSpecialCheck(CreateReadOnlyInsRequest request) throws RdsException;

    /**
     * special sub request param
     *
     * @param request
     * @param params
     * @return
     * @throws ApiException
     * @throws RdsException
     */
    public abstract CreateReadOnlyInsRequest buildSpecialRequest(CreateReadOnlyInsRequest request, Map<String, String> params) throws ApiException, RdsException, com.aliyun.apsaradb.gdnmetaapi.ApiException;

    /**
     * replica resource request
     *
     * @param readInsName
     * @param diskSize
     * @param diskType
     * @param performanceLevel
     * @param classCode
     * @param avzInfo
     * @param role
     * @param podScheduleTemplate
     * @param isSingleTenant
     * @param bizType
     * @return
     * @throws RdsException
     */
    private ReplicaResourceRequest buildReplicaResourceRequest(String readInsName,
                                                               Integer diskSize, String diskType, String performanceLevel,
                                                               String classCode, AVZInfo avzInfo,
                                                               Replica.RoleEnum role,
                                                               PodScheduleTemplate podScheduleTemplate,
                                                               boolean isSingleTenant,
                                                               ReplicaSet.BizTypeEnum bizType) throws RdsException {
        return buildReplicaResourceRequestUseZoneId(readInsName, diskSize, diskType, performanceLevel, classCode,
                avzInfo.getMasterZoneId(), role, podScheduleTemplate, isSingleTenant, bizType);
    }

    public ReplicaResourceRequest buildReplicaResourceRequestUseZoneId(String readInsName,
                                                               Integer diskSize, String diskType, String performanceLevel,
                                                               String classCode, String zoneId,
                                                               Replica.RoleEnum role,
                                                               PodScheduleTemplate podScheduleTemplate,
                                                               boolean isSingleTenant,
                                                               ReplicaSet.BizTypeEnum bizType) throws RdsException {
        ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
        replicaResourceRequest.setRole(role.getValue());
        replicaResourceRequest.setClassCode(classCode);
        if (ReplicaSetService.isStorageTypeCloudDisk(diskType)) {
            List<VolumeSpec> volumeSpecList = replicaSetService.getCloudDiskReplicaVolumeSpecList(diskSize, readInsName);
            if (StringUtils.isNotBlank(performanceLevel)) {
                volumeSpecList.stream().filter(v -> StringUtils.equals(v.getCategory(), "data")).forEach(v -> v.setPerformanceLevel(performanceLevel));
            }
            replicaResourceRequest.setVolumeSpecs(volumeSpecList);
        }

        if (podScheduleTemplate != null) {
            replicaResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaScheduleTemplateByRole(podScheduleTemplate, replicaResourceRequest.getRole()));
        }
        diskSize = podParameterHelper.getExtendDiskSizeGBForPod(bizType, false, diskSize);
        replicaResourceRequest.setDiskSize(diskSize);
        replicaResourceRequest.setStorageType(diskType);
        replicaResourceRequest.setSingleTenant(isSingleTenant);
        replicaResourceRequest.setZoneId(zoneId);
        return replicaResourceRequest;
    }

    /**
     * 只读实例如果不指定版本，则使用主实例版本
     * 不传入，就是用主实例版本
     *
     * @param request
     * @param primaryInsLabels
     * @return
     */
    protected String getTargetMinorVersionForReadIns(CreateReadOnlyInsRequest request,
                                                   Map<String, String> primaryInsLabels) {
        String targetMinorVersion = request.getTargetMinorVersion();
        if (StringUtils.isNotEmpty(targetMinorVersion)) {
            return targetMinorVersion;
        }

        String minorVersion = primaryInsLabels.get("minor_version");
        logger.info("primary ins version: {}", minorVersion);
        return minorVersion;

    }


    /**
     * basic check
     *
     * @param request
     * @return
     * @throws RdsException
     * @throws ApiException
     */
    protected Map<String, Object> doBasicCheck(CreateReadOnlyInsRequest request) throws RdsException, ApiException {
        //check user cluster
        mysqlParameterHelper.checkUserOperatorCluster(request.getUserId());

        if (StringUtils.isNotEmpty(request.getInstructionSetArch())
                &&!ReplicaSetResourceRequest.ArchEnum.ARM.toString().equalsIgnoreCase(request.getInstructionSetArch())
                && !ReplicaSetResourceRequest.ArchEnum.X86.toString().equalsIgnoreCase(request.getInstructionSetArch())) {
            throw new RdsException(MysqlErrorCode.INVALID_INSTRUCTIONSET_ARCH.toArray());
        }

        if (request.getPrimaryReplicaSet().getStatus() != ReplicaSet.StatusEnum.ACTIVATION) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        } else if (request.getPrimaryReplicaSet().getInsType() != ReplicaSet.InsTypeEnum.MAIN) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        //check read ins name exist or not
        ReplicaSet readReplicaSet = metaService.getDefaultClient().getReplicaSet(request.getRequestId(), request.getReadInsName(), true);
        if (readReplicaSet != null) {
            throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
        }

        if (MysqlParamSupport.isSingleNode(request.getPrimaryInsInstanceLevel())) {
            logger.error(String.format("%s is single node.", request.getPrimaryInsInstanceLevel()));
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 只读实例的磁盘大小需要大于等于原主实例的磁盘
        if (request.getPrimaryReplicaSet().getDiskSizeMB() > request.getDiskSize() * 1024) {
            return ResponseSupport.createErrorResponse(ErrorCode.UNSUPPORTED_READ_DBINSTANCE_DISKSIZE);
        }

        if ("lvs".equalsIgnoreCase(request.getConnType())) {
            CheckUtils.checkValidForVPCId(request.getVpcId());
            CheckUtils.checkValidForVswitchId(request.getVSwitchId());
        }

        PodParameterHelper.checkCloudEssdStorageValidByDiskTypeAndPLEVEL(request.getDiskType(), request.getPerformanceLevel(), request.getDiskSize());
        return null;
    }

    /**
     * 只读实例节点个数
     *
     * @param request
     * @return
     * @throws RdsException
     */
    public abstract Integer getNodeCount(CreateReadOnlyInsRequest request) throws RdsException, com.aliyun.apsaradb.gdnmetaapi.ApiException, ApiException;
}
