package com.aliyun.dba.poddefault.action.service.modify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.InitOptimizedWritesStringEnum;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.activityprovider.model.ShrinkReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.ExtendedLogPlanDO;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustInstanceQuery;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsSupport.CLUSTER_LEVEL;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_STATUS_TRANS;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.RdsConstants.CUSTINS_INSTYPE_TMP;

@Service
public class ModifyDBInstanceDiskShrinkServiceV2 extends BaseModifyDBInstanceService {

    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private ServerlessResourceService serverlessResourceService;
    @Resource
    protected RundPodSupport rundPodSupport;

    /**
     *  云盘缩容服务，仅支持同系列规格变更和云盘容量减少和云盘性能等级变更
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        boolean isSuccess = false;
        Map<String, String> allocateReplicaSets = new HashMap<>();
        List<Integer> transList = new ArrayList<>();
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID, "");
        PodModifyInsParam modifyInsParam = null;
        try {
            // 灰度开关
            if(!podParameterHelper.isSupportShrink()){
                throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            // 初始化变配参数
            modifyInsParam = initPodModifyInsParam(params);
            ReplicaSetResource replicaSetResource = dBaasMetaService.getDefaultClient().getReplicaSetBundleResource(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName());

            if (!modifyInsParam.isAliyun() || !modifyInsParam.isShrinkIns()) {
                logger.error("not support shrink, isAliyun:{}, isShrinkIns:{}", modifyInsParam.isAliyun(), modifyInsParam.isShrinkIns());
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 缩容次数校验,可按照uid加白
            podParameterHelper.checkShrinkLimit(modifyInsParam.getRequestId(), modifyInsParam.getDbInstanceName(), modifyInsParam.getUid());

            // 检查可缩容云盘类型
            if(!SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getSrcDiskType()) || !SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(modifyInsParam.getTargetDiskType())){
                throw new RdsException(INVALID_STORAGE);
            }

            // 检查磁盘类型变更
            if (modifyInsParam.isDiskTypeChange()) {
                throw new RdsException(UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }

            // 不允许没达标的集团TDDL实例缩容
            boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, modifyInsParam.getReplicaSetMeta());
            if (modifyInsParam.isTDDL() && !isTddlTaskMigrate) {
                logger.error("not support TDDL shrink when tddl task migrate label is false");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 不允许架构迁移
            boolean isArchChange =  PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel());
            if (isArchChange) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许同时category改变，暂不支持serverless
            if (modifyInsParam.getSrcInstanceLevel().getCategory() != modifyInsParam.getTargetInstanceLevel().getCategory() || !SHRINK_SUPPORT_CATEGORY_LIST_V2.contains(modifyInsParam.getSrcInstanceLevel().getCategory())) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许同时迁移
            String migratingAvz = mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false");
            if (Boolean.parseBoolean(migratingAvz)) {
                logger.error("not support shrink and migrate at the same time");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            String replicaSetName = modifyInsParam.getReplicaSetMeta().getName();
            ReplicaSet replicaSet = modifyInsParam.getReplicaSetMeta();
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null);

            // 集群版mgr条件校验
            if (replicaSetService.isMgr(requestId, modifyInsParam.getDbInstanceName())) {
                replicaSetService.isSupportMgr(listReplicasInReplicaSet.getItems().size(), modifyInsParam.getTargetInstanceLevel(), null);
            }

            // 校验当前实例已使用空间是否满足缩容条件
            // check logPlan and return extendedLogPlan with local retain
            ExtendedLogPlanDO extendedLogPlanDO = mysqlParamSupport.checkAndGetCustinsBinlogForShrink(custins);
            podParameterHelper.checkShrinkCloudESSDValid(modifyInsParam.getRequestId(), modifyInsParam.getReplicaSetMeta().getId().intValue(), modifyInsParam.getUid(), modifyInsParam.getDiskSizeGB(), modifyInsParam.getTargetDiskSizeGB(), extendedLogPlanDO);

            // 云盘赠送
            int extendedDestDiskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(modifyInsParam.getReplicaSetMeta().getBizType(), modifyInsParam.isSingleNode(), modifyInsParam.getTargetDiskSizeGB());

            // 适配集群版下同role可有多个replica
            Map<Replica.RoleEnum, List<Replica>> replicas = new HashMap<>();
            List<Replica> slaveReplicas = listReplicasInReplicaSet.getItems().stream().filter(r-> Replica.RoleEnum.SLAVE == r.getRole()).collect(Collectors.toList());
            replicas.put(Replica.RoleEnum.SLAVE, slaveReplicas);
            Replica masterReplica = listReplicasInReplicaSet.getItems().stream().filter(r-> Replica.RoleEnum.MASTER == r.getRole()).findFirst().get();
            replicas.put(Replica.RoleEnum.MASTER, Arrays.asList(masterReplica));

            // 适配集群版,指定HA时切换时的源replica
            Replica targetReplica = CollectionUtils.isEmpty(slaveReplicas) ? masterReplica : slaveReplicas.get(0);

            // 集群版可以指定优先替换某个slave
            String targetReplicaId = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_ID, "");
            if (CollectionUtils.isNotEmpty(slaveReplicas) && StringUtils.isNotEmpty(targetReplicaId)) {
                targetReplica = slaveReplicas.stream().filter(r -> StringUtils.equals(Objects.requireNonNull(r.getId()).toString(), targetReplicaId)).findFirst().orElse(null);
                if (null == targetReplica) {
                    throw new RdsException(INVALID_INSTANCE_ID);
                }
            }

            // 资源申请
            Integer transTaskId = allocateForShrink(requestId, transList, allocateReplicaSets, modifyInsParam, targetReplica, replicas, params, extendedDestDiskSizeGB);
            Replica destReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("master"), null, null, null, null).getItems().get(0);
            Long destReplicaId = destReplica.getId();
            Long destSlaveReplicaId = null;
            if (null != allocateReplicaSets.get("slave")){
                Replica destSlaveReplica = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, allocateReplicaSets.get("slave"), null, null, null, null).getItems().get(0);
                destSlaveReplicaId = destSlaveReplica.getId();
            }

            // 只读实例的临时实例需要配置白名单同步label
            allocateReplicaSets.forEach((key, value) -> {
                try {
                    podParameterHelper.setReadInsSgLabel(requestId, replicaSetResource.getReplicaSet(), value);
                } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
                    logger.error("set read ins flush white list label fail.", requestId, e.getResponseBody());
                }
            });

            // 构建任务参数
            JSONObject jsonObject = new JSONObject();
            String domain = PodCommonSupport.getTaskDomain(replicaSet.getBizType());
            String taskKey = TASK_SHRINK_INS;

            // 获取transList并更新参数
            TransferTask transTask = dBaasMetaService.getDefaultClient().getTransferTask(requestId, replicaSetName, transTaskId);

            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", replicaSetName);
            jsonObject.put("destReplicaSetName", allocateReplicaSets.get("master"));
            jsonObject.put("destReplicaId", destReplicaId);
            jsonObject.put("destSlaveReplicaSetName", allocateReplicaSets.get("slave"));
            jsonObject.put("destSlaveReplicaId", destSlaveReplicaId);
            jsonObject.put("destDiskSizeMB", modifyInsParam.getTargetDiskSizeGB() * 1024);
            jsonObject.put("extendedDestDiskSizeGB", extendedDestDiskSizeGB);
            jsonObject.put("destClassCode", modifyInsParam.getTargetClassCode());
            jsonObject.put("performanceLevel", modifyInsParam.getTargetPerformanceLevel());
            jsonObject.put("srcParentReplicaSetName", modifyInsParam.getReplicaSetMeta().getPrimaryInsName());
            jsonObject.put("switchInfo", modifyInsParam.getSwitchInfo());
            jsonObject.put("targetReplicaName", StringUtils.equals(replicaSet.getCategory(), CLUSTER_LEVEL) ? targetReplica.getName() : null);

            Map<String, Object> transParams = StringUtils.isEmpty(transTask.getParameter()) ? new HashMap<>() : JSON.parseObject(transTask.getParameter(), Map.class);
            transParams.put("workflowParams", jsonObject.toJSONString());
            transTask.setParameter(JSONObject.toJSONString(transParams));
            // common新增任务时dest disk size应用的扩展值，这里还原成接口原始请求值
            transTask.setDestDiskSizeMB(modifyInsParam.getTargetDiskSizeGB() * 1024);
            dBaasMetaService.getDefaultClient().updateTransferTask(requestId, replicaSetName, transTaskId, transTask);

            // 下发任务
            jsonObject.put(CustinsSupport.TRANS_ID, transTask.getId());
            String parameter = jsonObject.toJSONString();
            Object taskId = workFlowService.dispatchTask("custins", replicaSetName, domain, taskKey, parameter, 0);

            custinsService.updateCustInstanceStatusByCustinsId(replicaSet.getId().intValue(), CUSTINS_STATUS_TRANS, CustinsState.STATE_CLASS_CHANGING.getComment());
            isSuccess = true;

            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("Region", modifyInsParam.getRegionId());
            data.put("DBInstanceID", modifyInsParam.getReplicaSetMeta().getId());
            data.put("DBInstanceName", modifyInsParam.getDbInstanceName());
            data.put("SourceDBInstanceClass", modifyInsParam.getClassCode());
            data.put("TargetDBInstanceClass", modifyInsParam.getTargetClassCode());
            data.put("SourceDBInstanceStorage", modifyInsParam.getDiskSizeGB());
            data.put("TargetDBInstanceStorage", modifyInsParam.getTargetDiskSizeGB());
            data.put("SourcePerformanceLevel", modifyInsParam.getSrcPerformanceLevel());
            data.put("TargetPerformanceLevel", modifyInsParam.getTargetPerformanceLevel());
            return data;
        } catch (RdsException e) {
            logger.warn(requestId + " RdsException: ", e);
            return createErrorResponse(e.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception e){
            logger.error(requestId + " Exception: ", e);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            //释放申请的资源
            if (!isSuccess && modifyInsParam != null) {
                allocateReplicaSets.forEach((key, value) -> {
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, value);
                    } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                        logger.error("{} rollback resource failed: {}", requestId, e.getResponseBody());
                    }
                });
                // 将common新增的transList记录删除
                if (CollectionUtils.isNotEmpty(transList) && null != transList.get(0)) {
                    try {
                        dBaasMetaService.getDefaultClient().deleteTransferTask(requestId, modifyInsParam.getReplicaSetMeta().getName(), transList.get(0));
                    } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
                        logger.error("{} delete trans list {} record failed: {}", requestId, transList.get(0), e.getResponseBody());
                    }
                }
            }
        }
    }

    public Integer allocateForShrink(String requestId, List<Integer> transList, Map<String, String> allocateReplicaSets, PodModifyInsParam modifyInsParam, Replica targetReplica, Map<Replica.RoleEnum, List<Replica>> replicas, Map<String, String> params, int extendedDestDiskSizeGB) throws Exception{
        // 获取源实例信息
        ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, modifyInsParam.getReplicaSetMeta().getName(), null);
        // 获取serverless实例信息
        ServerlessSpec serverlessSpec = null;
        if(replicaSetService.isServerless(replicaSet)){
            // Get serverless spec info
            try {
                serverlessSpec = serverlessResourceService.getServerlessSpec(requestId, replicaSet.getName());
            } catch (ApiException e) {
                logger.error(e.getMessage(), e);
                throw new RdsException(ErrorCode.INTERNAL_FAILURE);
            }
        }
        // 检查是否存在重搭临时实例
        CustInstanceQuery custInstanceQuery = new CustInstanceQuery();
        custInstanceQuery.setCustInsType(CustInsType.CUST_INS_TYPE_MIRROR);
        custInstanceQuery.setIsTmp(CUSTINS_INSTYPE_TMP);
        custInstanceQuery.setPrimaryCustinsId(modifyInsParam.getReplicaSetMeta().getId().intValue());
        Long mirrorCount = custinsService.countCustIns(custInstanceQuery);
        if (mirrorCount > 0) {
            throw new RdsException(ErrorCode.MIRROR_INS_EXIST);
        }
        String category = replicaSet.getCategory();
        if (modifyInsParam.isReadIns() && Objects.nonNull(replicaSet.getPrimaryInsName())) {
            ReplicaSet primaryReplicaSet = dependency.getDBaasMetaService().getDefaultClient().getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
            category = primaryReplicaSet.getCategory();
        }
        // 构建资源申请,缩容允许规格变化,且都走跨机重搭的方式
        ShrinkReplicaResourceRequest shrinkReplicaResourceRequest = new ShrinkReplicaResourceRequest();
        shrinkReplicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
        shrinkReplicaResourceRequest.setDiskSize(extendedDestDiskSizeGB);
        shrinkReplicaResourceRequest.setRebuildMode(ShrinkReplicaResourceRequest.RebuildModeEnum.MIGRATE);
        shrinkReplicaResourceRequest.setSingleTenant(modifyInsParam.isTargetSingleTenant());
        shrinkReplicaResourceRequest.setCategory(category);
        shrinkReplicaResourceRequest.setIgnoreCreateVpcMapping(true);
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(modifyInsParam.getPodType().getRuntimeType())) {
            rundPodSupport.completeReplicaSetNetworkConfig(shrinkReplicaResourceRequest);
        }

        shrinkReplicaResourceRequest.setInitOptimizedWritesString(InitOptimizedWritesStringEnum.fromValue(modifyInsParam.getTargetInitOptimizedWritesString()));

        // 指定资源调度模板
        shrinkReplicaResourceRequest.setScheduleTemplate(
                podTemplateHelper.getReplicaSetScheduleTemp(replicaSet, modifyInsParam.getTargetInstanceLevel(), modifyInsParam.isTargetSingleTenant(), null)
        );

        // 指定composeTag
        String composeTag = minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(requestId, replicaSet.getName());
        shrinkReplicaResourceRequest.setComposeTag(composeTag);

        // 指定重搭主机
        String hostName = podParameterHelper.getHostNameFromParamsByDedicatedHostName(ActionParamsProvider.ACTION_PARAMS_MAP.get(), "DedicatedHostNames", replicaSet.getResourceGroupName());
        shrinkReplicaResourceRequest.setHostName(hostName);

        // 如果接口指定了调度策略，则透传下去
        if (StringUtils.isNotEmpty(mysqlParamSupport.getResourceStrategy(params))) {
            if (shrinkReplicaResourceRequest.getScheduleTemplate() == null) {
                shrinkReplicaResourceRequest.setScheduleTemplate(new ScheduleTemplate());
            }
            shrinkReplicaResourceRequest.getScheduleTemplate().setResourceStrategy(mysqlParamSupport.getResourceStrategy(params));
        }

        // 云盘在任务流中申请资源
        shrinkReplicaResourceRequest.setVolumeSpecs(null);
        shrinkReplicaResourceRequest.setAllocateDisk(false);
        // serverless实例需要传入最大RCU
        if(replicaSetService.isServerless(replicaSet)){
            shrinkReplicaResourceRequest.setServerlessRcu(serverlessSpec.getScaleMax());
        }
        //===================================重搭master实例资源申请=======================================
        shrinkReplicaResourceRequest.setReplicaIds(Arrays.asList(targetReplica.getId()));
        shrinkReplicaResourceRequest.setTmpReplicaSetName(modifyInsParam.getTmpReplicaSetName());
        // 只读不需要拷贝数据，因此不需要两块盘
        shrinkReplicaResourceRequest.isReserveDisk(ReplicaSet.InsTypeEnum.READONLY != replicaSet.getInsType());
        Integer transTaskId = commonProviderService.getDefaultApi().allocateReplicaSetResourceForShrinkV2(requestId, replicaSet.getName(),  shrinkReplicaResourceRequest);
        allocateReplicaSets.put("master", shrinkReplicaResourceRequest.getTmpReplicaSetName());
        transList.add(transTaskId);
        //===================================重搭slave实例资源申请=======================================
        if (CollectionUtils.isEmpty(replicas.get(Replica.RoleEnum.SLAVE))) {
            return transTaskId;
        }
        List<Long> replicaIds = new ArrayList<>();
        // 申请重搭主节点资源
        replicaIds.add(replicas.get(Replica.RoleEnum.MASTER).get(0).getId());
        // 申请除第一个重搭节点外的所有备节点资源
        replicas.get(Replica.RoleEnum.SLAVE).stream().filter(r -> r.getId() != targetReplica.getId()).forEach(r -> replicaIds.add(r.getId()));
        shrinkReplicaResourceRequest.setReplicaIds(replicaIds);
        shrinkReplicaResourceRequest.setTmpReplicaSetName("slave" + modifyInsParam.getTmpReplicaSetName());
        shrinkReplicaResourceRequest.setTransTaskId(transTaskId);
        // 后续重搭用备份恢复，不需要两块盘拷数据
        shrinkReplicaResourceRequest.isReserveDisk(false);
        commonProviderService.getDefaultApi().allocateReplicaSetResourceForShrinkV2(requestId, replicaSet.getName(), shrinkReplicaResourceRequest);
        allocateReplicaSets.put("slave", shrinkReplicaResourceRequest.getTmpReplicaSetName());
        return transTaskId;
    }
}
