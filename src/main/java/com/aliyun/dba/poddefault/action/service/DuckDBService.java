package com.aliyun.dba.poddefault.action.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

/**
 * @author: 光齐
 * @create-date: 2025/6/9 16:00
 */
@Service
public class DuckDBService {

    /**
     * 判断是否为分析型只读实例
     *
     * @param params 请求参数
     * @return true 如果是分析型只读实例，false 否则
     */
    public boolean isAnalyticReadOnlyIns(Map<String, String> params) {
        String isAnalyticReadOnlyIns = getParameterValue(params, "isAnalyticReadOnlyIns");
        return StringUtils.isNotBlank(isAnalyticReadOnlyIns) && Boolean.parseBoolean(isAnalyticReadOnlyIns);
    }
}
