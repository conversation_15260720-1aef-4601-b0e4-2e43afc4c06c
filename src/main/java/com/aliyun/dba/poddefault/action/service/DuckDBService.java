package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;

/**
 * @author: 光齐
 * @create-date: 2025/6/9 16:00
 */
@Service
public class DuckDBService {
    private static final LogAgent logger = LogFactory.getLogAgent(DuckDBService.class);

    @Autowired
    private CustinsParamService custinsParamService;

    @Resource
    private DBaasMetaService dBaasMetaService;

    /**
     * 判断是否为分析型只读实例
     *
     * @param params 请求参数
     * @return true 如果是分析型只读实例，false 否则
     */
    public boolean isAnalyticReadOnlyIns(Map<String, String> params) {
        String isAnalyticReadOnlyIns = getParameterValue(params, "isAnalyticReadOnlyIns");
        return StringUtils.isNotBlank(isAnalyticReadOnlyIns) && Boolean.parseBoolean(isAnalyticReadOnlyIns);
    }

    public String getAnalyticReadOnlyInsParamGroupId(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet) throws ApiException, RdsException {
        ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(request.getRequestId(), request.getDbInstanceName(),false);
        CustinsParamDO primaryParamGroupIdDO = custinsParamService.getCustinsParam(primaryReplicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        String paramGroupId;
        Map<String, String> map;
        if (primaryParamGroupIdDO == null || !primaryParamGroupIdDO.getValue().startsWith("rpg-sys-")) {
            // 如果是用户参数模板 使用官方 duckDB 模板
            map = new HashMap<>();
            map.put("db_type", request.getDbType());
            map.put("db_version", request.getDbVersion());
            map.put("character_type", "normal");
            map.put("category", Objects.requireNonNull(readReplicaSet.getCategory()).toLowerCase());
            map.put("db_storage_engine", "duckdb");
            map.put("performance_mode", "safe");
            map.put("kind_code", "any");
        } else {
            //todo:先只支持safe模版
            Map<String, Object> paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(primaryParamGroupIdDO.getValue());
            paramGroupInfo.put("db_storage_engine", "duckdb");
            paramGroupInfo.put("performance_mode", "safe");
            map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
        }
        paramGroupId = SysParamGroupHelper.getSysParamGroupId(map);
        logger.info("paramGroupId is: {}, request map is {}", paramGroupId, map.toString());
        return paramGroupId;
    }
}
