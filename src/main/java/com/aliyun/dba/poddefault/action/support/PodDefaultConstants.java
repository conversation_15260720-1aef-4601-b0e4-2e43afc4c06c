package com.aliyun.dba.poddefault.action.support;

import com.aliyun.apsaradb.activityprovider.model.ModifyReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.NetTypeEnum;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.google.common.collect.Sets;

import java.util.*;

import static com.aliyun.dba.custins.support.CustinsParamSupport.*;

public class PodDefaultConstants {
    public static final String APP_NAME = "RDSAPI_EXT_MYSQL";

    public static Map<String, String> ALIGROU_CORE_BIZ_CLASS_CODE_MAP = new HashMap<String, String>() {{
        // 48C 360G
        put("BUYERS_APP", "mysql.x7.6xlarge.25.aligroup");
        put("ALI_INV_XCLUSTER_APP", "mysql.x7.6xlarge.25.aligroup");
        put("SBTEST1INS_APP", "mysql.x7.12xlarge.25.aligroup");
        put("SBTEST2INS_APP", "mysql.x7.6xlarge.25.aligroup");
        put("SBTEST4INS_APP", "mysql.x5.4xlarge.25.aligroup");
        put("SBTEST8INS_APP", "mysql.x5.2xlarge.25.aligroup");
        put("CARTS_APP", "mysql.x5.4xlarge.25.aligroup");
    }};


    public static Map<Integer, NetTypeEnum> NET_TYPE_ENUM_MAP = new HashMap<Integer, NetTypeEnum>() {{
        this.put(0, NetTypeEnum.PUBLIC);
        this.put(1, NetTypeEnum.CLASSIC);
        this.put(2, NetTypeEnum.VPC);
    }};

    /**
     * 任务域相关
     */
    public final static String DOMAIN_MYSQL = "mysql";
    public final static String DOMAIN_MARIADB = "mariadb";
    public final static String DOMAIN_XDB = "xdb";  //目前就上云走该domain


    /**
     * 资源模板
     */
    public final static String RS_TEMPLATE_USER_PREFIX = "TEMPLATE_";
    public final static String RS_TEMPLATE_SYS_PREFIX = "SYS_TEMPLATE_";
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_HA = RS_TEMPLATE_SYS_PREFIX + "MYSQL_HIGH_PERF_POOL"; // 云上高可用云盘使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS = RS_TEMPLATE_SYS_PREFIX + "MYSQL_SERVERLESS_POOL"; // 云上SERVERLESS使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_ECONOMIC = RS_TEMPLATE_SYS_PREFIX + "MYSQL_ECONOMIC"; // 云上SERVERLESS使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS_V2 = RS_TEMPLATE_SYS_PREFIX + "MYSQL_SERVERLESS_V2_POOL"; // 云上SERVERLESS使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_VBM = RS_TEMPLATE_SYS_PREFIX + "MYSQL_VBM_POOL";
    public final static String RS_TEMPLATE_NAME_SYS_DBS = RS_TEMPLATE_SYS_PREFIX + "DBS_POOL"; // DBS的资源池
    public final static String RS_TEMPLATE_ADMIN_USER = "admin";
    public final static String TEMPLATE_ALIGROUP_128_VOLUME = RS_TEMPLATE_USER_PREFIX +"ALIGROUP_128_VOLUME";
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_HA_FOR_IO_ACCELERATION = RS_TEMPLATE_SYS_PREFIX + "MYSQL_HIGH_PERF_POOL_FOR_BPE"; // 云上高可用云盘开启本地盘介质IO加速使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_BASIC_FOR_IO_ACCELERATION = RS_TEMPLATE_SYS_PREFIX + "MYSQL_BASIC_PERF_POOL_FOR_BPE"; // 云上基础版云盘开启本地盘介质IO加速使用的默认资源模板


    //容器网络lb挂载端口
//    public static final String ACCESS_PORT_NAME = "access_port";
//    public static final String PORT_POLICY_CONTAINER = "Container";

    /**
     * VBM灰度控制相关
     * */
    public static final String VBM_GRAY_UID_LEVEL_CONFIG = "VBM_GRAY_UID_LEVEL_CONFIG";

    public static final String VBM_CUSTINS_LABEL_KEY = "custins_resource_type";
    public static final String VBM_CUSTINS_LABEL_VALUE = "vbm";


    /**
     * 可用区自动选择
     */
    public static String ZONE_AUTOMATIC = "automatic";

    /**
     * 任务目标类型
     */
    public final static String TARGET_TYPE_CUSTINS = "custins";

    /**
     * 任务优先级相关
     */
    public final static int TASK_PRIORITY_COMMON = 0; //普通任务
    public final static int TASK_PRIORITY_VIP = 1;    //VIP任务


    /**
     * 任务TaskKey相关
     */
    public final static String TASK_CREATE_INS = "create_ins";
    public final static String TASK_CREATE_CLUSTER_INS = "create_cluster_ins";
    public final static String TASK_CHECK_USER_BAKFILE = "check_user_bakfile";
    public final static String TASK_CREATE_XDB_INS = "create_xdb_ins";
    public final static String TASK_CREATE_XDB_TDDL_INS = "create_xdb_tddl_ins";


    public final static String TASK_CLONE_INS = "clone_ins";
    public final static String TASK_CLONE_CLUSTER_INS = "clone_cluster_ins";
    public final static String TASK_CLONE_XDB_INS = "clone_xdb_ins";
    public final static String TASK_RESTORE_DBS_INS = "restore_dbs_ins";

    public final static String TASK_DISASTER_RESTORE_INS = "disaster_restore_ins";
    public final static String TASK_DISASTER_RESTORE_CLUSTER_INS = "disaster_restore_cluster_ins";

    public final static String TASK_RESTART_INS = "restart_ins";
    public final static String TASK_RESTART_INS_NODE = "restart_ins_node";
    public final static String TASK_RESTART_INS_WITH_FAILOVER = "restart_ins_with_failover";
    public final static String TASK_RESTART_MYSQL_NODE = "restart_mysql_node";
    public final static String TASK_RESTART_XDB_INS_WITH_FAILOVER = "restart_xdb_ins_with_failover";
    public final static String TASK_RESTART_XDB_INS = "restart_xdb_ins";
    public final static String TASK_RESTART_XDB_READ_INS = "restart_xdb_read_ins";
    public final static String TASK_RESTART_RESOURCE_NODE = "restart_resource_node";
    public final static String TASK_RESTART_MYSQLD = "restart_mysqld";
    public final static String TASK_RESTART_MGR_INS = "restart_mgr_ins";
    public final static String TASK_MODIFY_INS = "modify_ins";
    public final static String TASK_MODIFY_INS_FOR_RUND = "modify_ins_for_rund";
    public final static String TASK_MODIFY_INS_BASIC_TO_HA_OR_CLUSTER = "modify_ins_basic_to_ha_or_cluster";
    public final static String TASK_MODIFY_INS_HA_TO_CLUSTER = "modify_ins_ha_to_cluster";
    public final static String TASK_MODIFY_INS_FOR_IO_ACCELERATION = "modify_ins_for_io_acceleration";
    public final static String TASK_MODIFY_OPTIMIZED_WRITES = "modify_optimized_writes";
    public final static String TASK_MODIFY_OPTIMIZED_WRITES_INFO = "modify_optimized_writes_info";
    public final static String TASK_MODIFY_INS_READ_ONLY_STATUS = "modify_ins_read_only_status";

    public final static String TASK_MODIFY_INS_HA_TO_CLUSTER_DIRECTLY = "modify_ins_ha_to_cluster_directly";
    public final static String TASK_CREATE_TMP_INS = "create_tmp_ins";
    public static final String TASK_SWITCH_TO_TMP_INS = "switch_to_tmp_ins";
    public static final String CANCEL_TMP_INS = "cancel_tmp_ins";
    public final static String TASK_BASIC_MODIFY_INS_DISK_TYPE = "modify_basic_ins_disk_type";
    public final static String TASK_STANDARD_MODIFY_INS_DISK_TYPE = "modify_ha_ins_disk_type";
    public final static String TASK_MIGRATE_FAIL_OVER = "migrate_fail_over";
    public final static String TASK_MIGRATE_FAIL_OVER_FOR_BASIC_READ = "migrate_basic_read_ins";
    public final static String TASK_MODIFY_INS_HA = "modify_ins_for_ha";
    public final static String TASK_MODIFY_XDB_INS = "modify_xdb_ins";
    public final static String TASK_ONLINE_RESIZE_INS = "online_resize_ins";
    public final static String TASK_ONLINE_SHRINK_INS = "online_shrink_ins";
    public final static String TASK_SHRINK_INS = "shrink_ins";
    public final static String TASK_SHRINK_SERVERLESS_INS = "shrink_serverless_ins";
    public final static String TASK_ONLINE_RESIZE_INS_LOG = "online_resize_ins_log_volume";
    public final static String TASK_MODIFY_INS_CLUSTER = "modify_ins_for_cluster";
    public final static String TASK_MODIFY_INS_NODE = "modify_ins_node";
    public final static String TASK_LOCAL_MODIFY_INS_NODE = "local_modify_ins_node";
    public final static String TASK_ONLINE_RESIZE_INS_FOR_CLUSTER = "online_resize_ins_for_cluster";

    public final static String TASK_MODIFY_TDDL_READ_INS = "modify_tddl_readins";
    public final static String TASK_MODIFY_TDDL_DOUBLE_READ_INS = "modify_tddl_double_read_ins";
    public final static String TASK_MODIFY_READ_INS = "modify_readins";
    public final static String READ_INS_SET_SQL_DELAY = "read_ins_set_sql_delay";

    public final static String TASK_MIGRATE_BASIC_INS_AVZ = "migrate_basic_ins_avz";
    public final static String TASK_MIGRATE_HA_INS_AVZ = "migrate_ha_ins_avz";
    public final static String TASK_MIGRATE_READ_INS_AVZ = "migrate_read_ins_avz";
    public final static String TASK_MIGRATE_CLUSTER_INS_AVZ = "migrate_cluster_ins_avz";
    public final static String TASK_MIGRATE_CLUSTER_NODES = "migrate_cluster_nodes";

    public final static String TASK_STOP_INS_STAGE_1 = "stop_ins_stage_1";
    public final static String TASK_STOP_INS_STAGE_2 = "stop_ins_stage_2";
    public final static String TASK_START_INS_FROM_STOP_STAGE_1 = "start_ins_from_stop_stage_1";
    public final static String TASK_START_INS_FROM_STOP_STAGE_2 = "start_ins_from_stop_stage_2";

    public final static String TASK_MODIFY_INS_ARCH_CHANGE = "modify_ins_arch_change";
    public final static String TASK_MODIFY_READ_INS_BASIC_TO_HA_ARCH_CHANGE = "modify_read_ins_basic_to_ha_arch_change";
    public final static String TASK_MODIFY_READ_INS_ARCH_CHANGE = "modify_read_ins_arch_change";
    public final static String TASK_MODIFY_INS_BASIC_TO_HA_ARCH_CHANGE = "modify_ins_basic_to_ha_arch_change";

    public final static String TASK_MODIFY_COLD_OSS = "modify_cold_oss";

    public final static String TASK_MODIFY_COMPRESSION_MODE = "modify_compression_mode";

    public final static String TASK_REFRESH_OSS_STS_TOKEN = "refresh_oss_sts_token";

    public final static String TASK_REFRESH_INS_STAT = "update_ins_stat";

    public final static String TASK_REFRESH_INS_CGROUP = "refresh_ins_cgroup";

    /**
     * The time when resize for mysql basic is being developed,
     * resize for xdb(HA) is already implemented as 'online_resize_ins'.
     */
    public final static String TASK_ONLINE_RESIZE_INS_FOR_BASIC = "online_resize_ins_for_basic";

    public final static String TASK_HA_SWITCH_INS = "ha_switch";
    public final static String TASK_TDDL_HA_SWITCH_INS = "tddl_ha_switch";

    public final static String TASK_READ_HA_SWITCH_INS = "read_ha_switch";

    public final static String TASK_DELETE_INS = "delete_ins";
    public final static String TASK_DELETE_TDDL_INS = "delete_tddl_ins";
    public final static String TASK_DELETE_STOPPED_INS = "delete_stopped_ins";
    public final static String TASK_START_STOPPED_INS_FOR_MAINTAIN = "start_stopped_ins_for_maintain";
    public final static String TASK_PURGE_LOGS = "purge_logs";
    public final static String TASK_LOCK_UNLOCK_INS = "lock_unlock_ins";
    public final static String TASK_LOCK_INS = "lock_ins";
    public final static String TASK_LOCK_INS_WITHOUT_KILL_PROCESS = "lock_ins_without_kill_process";
    public final static String TASK_UNLOCK_INS = "unlock_ins";

    // ibd check 相关
    public final static String CHECK_REPLICA_IBD = "check_replica_ibd";
    public final static String CHECK_IBD_INSTANCE_LEVEL_KEY = "CHECK_IBD_INSTANCE_LEVEL";


    public final static String TASK_TDDL_READINS_UPGRADE_MINOR_VERSION = "tddl_readins_upgrade_minor_version";
    public final static String TASK_TDDL_DOUBLE_READINS_UPGRADE_MINOR_VERSION = "tddl_double_read_upgrade_minor_version";
    public final static String TASK_UPGRADE_MINOR_VERSION_FOR_READ = "upgrade_minor_version_for_read";
    public final static String TASK_UPGRADE_MINOR_VERSION = "upgrade_minor_version";
    public final static String TASK_TDDL_XDB_UPGRADE_MINOR_VERSION = "tddl_xdb_upgrade_minor_version";

    public final static String TASK_UPGRADE_MAJOR_VERSION_FOR_STANDARD = "upgrade_major_version_for_standard";

    public final static String TASK_UPGRADE_MAJOR_VERSION_FOR_BASIC = "upgrade_major_version_for_basic";
    public final static String TASK_UPGRADE_MAJOR_VERSION_FOR_CLUSTER = "upgrade_major_version_for_cluster";

    public final static String TASK_UPGRADE_MAJOR_VERSION_FOR_BASIC_READ = "upgrade_major_version_for_basic_read";
    public final static String TASK_UPGRADE_MAJOR_VERSION_FOR_STANDARD_READ = "upgrade_major_version_for_standard_read";

    public final static String TASK_FLUSH_PARAM = "flush_params";
    public final static String TASK_FLUSH_PARAM_MYCNF = "flush_params_mycnf";
    public final static String TASK_FLUSH_PARAM_WITH_RESTART = "flush_params_with_restart";
    public final static String TASK_FLUSH_HA_MODEL = "flush_ha_model";
    public final static String TASK_TDDL_FLUSH_PARAM_WITH_RESTART = "tddl_flush_params_with_restart";
    public final static String TASK_REBUILD_SLAVE = "rebuild_slave";
    public final static String TASK_REBUILD_SLAVE_FOR_ESCAPE = "rebuild_slave_for_escape";
    public final static String TASK_REBUILD_CLUSTER_SLAVE = "rebuild_cluster_slave";

    public final static String TASK_REBUILD_MGR_SLAVE = "rebuild_mgr_slave";
    public final static String TASK_REBUILD_MGR_SLAVE_LOCAL = "rebuild_mgr_slave_local";
    public final static String TASK_REBUILD_CLUSTER_SLAVE_LOCAL = "rebuild_cluster_slave_local";
    public final static String TASK_REBUILD_SLAVE_LOCAL = "rebuild_slave_local";
    public final static String TASK_REBUILD_XDB_SLAVE = "rebuild_xdb_slave";
    public final static String TASK_REBUILD_TDDL_XDB_SLAVE = "rebuild_tddl_xdb_slave";
    public final static String TASK_REBUILD_XDB_SLAVE_BY_NC = "rebuild_xdb_slave_by_nc";
    public final static String TASK_REBUILD_XDB_LOGGER = "rebuild_xdb_logger";
    public final static String TASK_XDB_ONLINE_RESIZE_PFS_INS = "online_resize_pfs_ins";
    public final static String TASK_XDB_MIGRATE_INS_BY_NC = "modify_xdb_ins_by_nc";
    public final static String TASK_XDB_MIGRATE_READ_INS_BY_NC = "modify_xdb_read_ins";
    public final static String TASK_XDB_MIGRATE_PFS_INS = "modify_xdb_pfs_ins";
    public final static String TASK_XDB_MODIFY_TDDL_INS = "modify_tddl_xdb_ins";
    public final static String TASK_XDB_MODIFY_TDDL_PFS_INS = "modify_tddl_xdb_pfs_ins";
    public final static String TASK_REBUILD_TDDL_XDB_LEARNER = "rebuild_tddl_xdb_learner";

    public final static String TASK_REBUILD_XDB_LEARNER_BACK = "rebuild_xdb_learner_back";

    public final static String TASK_LOCAL_MODIFY_INS = "local_modify_ins";
    public final static String TASK_LOCAL_MODIFY_INS_HA = "local_modify_ins_for_ha";
    public final static String TASK_ELASTIC_MODIFY_INS = "elastic_modify_ins";
    public final static String TASK_ELASTIC_MODIFY_SERVERLESS_BASIC = "elastic_modify_serverless_basic";
    public final static String TASK_ELASTIC_MODIFY_INS_LOCAL = "elastic_modify_ins_local";
    public final static String TASK_SERVERLESS_CREATE_NET_TYPE = "serverless_create_net_type";
    public final static String TASK_SERVERLESS_DELETE_NET_TYPE = "serverless_delete_net_type";
    public final static String TASK_SERVERLESS_MODIFY_NET_TYPE = "serverless_modify_net_type";
    public final static String TASK_LOCAL_MODIFY_INS_CLUSTER = "local_modify_ins_for_cluster";

    public final static String TASK_MODIFY_VIP = "modify_vip";
    public final static String TASK_MODIFY_VIP_FOR_RUND = "modify_vip_for_rund";
    public final static String TASK_MODIFY_ENDPOINT = "modify_endpoint";

    /**
     * endpoint
     */
    public final static String TASK_CREATE_INS_ENDPOINT = "create_ins_endpoint";
    public final static String TASK_CREATE_ENDPOINT_PUBLIC_ADDRESS = "create_endpoint_public_address";
    public final static String TASK_DELETE_INS_ENDPOINT = "delete_ins_endpoint";
    public final static String TASK_DELETE_INS_ENDPOINT_ADDRESS = "delete_ins_endpoint_address";
    public final static String TASK_MODIFY_INS_ENDPOINT = "modify_ins_endpoint";
    public final static String TASK_MODIFY_ENDPOINT_ADDRESS = "modify_endpoint_address";
    public final static String TASK_MODIFY_ENDPOINT_VIP = "modify_endpoint_vip";

    /**
     *mysql mgr
     */
    public final static String TASK_CLUSTER_REMOVE_NODE = "cluster_remove_node";
    public final static String TASK_CLUSTER_ADD_NODE = "cluster_join_node";

    public final static String TASK_CLUSTER_MGR_REMOVE_NODE = "cluster_mgr_remove_node";
    public final static String TASK_CLUSTER_MGR_ADD_NODE = "cluster_mgr_join_node";

    /**
     * 添加ClickHouse节点
     */
    public final static String TASK_CLUSTER_JOIN_CLICKHOUSE_INSTANCE = "cluster_join_clickhouse_instance";

    public final static String TASK_CLUSTER_REMOVE_CLICKHOUSE_INSTANCE = "cluster_remove_clickhouse_instance";

    /**
     * 小版本升级
     */
    public final static String TASK_UPGRADE_MINOR_VERSION_HA = "upgrade_minor_version_for_ha";

    /**
     * 集群版实例小版本升级
     */
    public final static String TASK_UPGRADE_MINOR_VERSION_CLUSTER = "upgrade_minor_version_for_cluster";

    /**
     * 基础版数据紧急恢复任务
     */
    public final static String TASK_RECOVER_BASIC_INS = "recover_basic_ins";

    /**
     * 基础版离线迁移任务
     */
    public final static String TASK_MIGRATE_BASIC_INS = "migrate_basic_ins";

    /**
     * 基础版PL1迁移到PL0
     */
    public static final String TASK_MODIFY_BASIC_INS_FOR_PL1_TO_PL0 = "modify_basic_ins_from_pl1_to_pl0";

    /**
     * SSL相关任务
     * */
    public final static String MODIFY_READ_XDB_SSL_CONFIG = "modify_read_xdb_ssl_config";
    public final static String MODIFY_XDB_SSL_CONFIG = "modify_xdb_ssl_config";
    public final static String MODIFY_SSL = "modify_ssl";
    public final static String DYNAMIC_MODIFY_SSL = "modify_ssl_dynamic";
    public final static String MODIFY_SSL_FOR_CLUSTER = "modify_ssl_for_cluster";
    public final static String TASK_SERVERLESS_MODIFY_SSL = "serverless_modify_ssl";
    public static final String MYSQL_SSL_FORCE_ENCRYPTION_SWITCH = "MYSQL_SSL_FORCE_ENCRYPTION_SWITCH";

    /**
     * MGR
     * */
    public final static String SWITCH_MGR_TO_SEMI_SYNC = "switch_mgr_to_semi_sync";
    public final static String BUILD_GROUP_REPLICATION = "build_group_replication";

    public final static String TASK_MODIFY_INS_AFTER_CREATE = "modify_ins_after_create";

    /**
     * 链路相关
     */
    public final static String CONNECTION_DRAIN_MODE = "CONNECTION_DRAIN_MODE";

    // 安全容器相关
    public final static String RUND_GRAY_POLICY_MYSQL_BY_CATEGORY = "RUND_GRAY_POLICY_MYSQL_BY_CATEGORY";

    public final static String RUND_SUPPORT_MYSQL_MINI_VERSION = "RUND_SUPPORT_MYSQL_MINI_VERSION";
    public static final String PORT_POLICY_CONTAINER = "Container";
    public static final String ACCESS_PORT_NAME = "access_port";
    public static final Integer POD_DEFAULT_PORT = 3306;
    public static final String RUND_NODE_KEY = "alibabacloud.com/runtime-rund";
    public static final String Target_Runtime_Type = "target_runtime_type";
    /**
     * 实例Label相关
     */
    public final static String REPLICA_SET_LABEL_CLOUD_PFS = "cloud_pfs";
    public final static String REPLICA_SET_LABEL_CONTAINER_VERSION = "containers_version";

    public final static String TEMPLATE_ISOLATION_LABEL_KEY = "rm.alibaba-inc.com/dedicatedResPoolId";

    public final static String TEMPLATE_AFFINITY_LABEL_KEY = "appName";

    public final static String TEMPLATE_NODE_APP_CAPACITY_KEY = "app";

    public final static String TEMPLATE_ROLE_LOGGER_CAPACITY_KEY = "logger";

    public final static String REPLICA_SET_SINGLE_TENANT = "single_tenant";

    // 参考文档: https://yuque.antfin-inc.com/docs/share/033a6e3d-df1d-4d9c-b793-4ab5fc086fb1?#bdSsM
    public final static String SG_CLIENT_OF_CUSTINS = "sg_client_of_custins";

    public final static String SG_USE_OTHER_CUSTINS = "sg_use_other_custins";


    public static final String TFC_ACCESS = "TFC";


    // 集团跳过备份的label
    public final static String SKIP_BACKUP_LABLE = "skip_backup";

    /**
     * 数据库版本相关
     */
    public final static String MYSQL_VERSION_80 = "8.0";
    public final static String MYSQL_VERSION_57 = "5.7";

    /**
     * pengine迁移新架构
     */
    public final static String TASK_MIGREATE_PENGINE_TO_K8S_FOR_MYSQL_BASIC_ON_ECS = "migrate_pengine_to_k8s_for_mysql_basic_on_ecs";

    /**
     * 集团静默
     */
    public final static String SLIENT_HOURS = "slientHours";
    public final static String SKIP_BACKUP = "skipBackup";

    /**
     * XDB 默认权重设置
     */
    public static final Integer DEFAULT_XDB_DATA_NODE_WEIGHT = 9;
    public static final Integer DEFAULT_XDB_DEFAULT_WEIGHT = 5;
    public static final Integer DEFAULT_XDB_NONE_WEIGHT = 0;
    public static final Integer DEFAULT_XDB_MASTER_WEIGHT = DEFAULT_XDB_DATA_NODE_WEIGHT;
    public static final Integer DEFAULT_XDB_FOLLOWER_WEIGHT = DEFAULT_XDB_DATA_NODE_WEIGHT;
    public static final Integer DEFAULT_XDB_LEANER_WEIGHT = DEFAULT_XDB_DEFAULT_WEIGHT;
    public static final Integer DEFAULT_XDB_LOGGER_WEIGHT = 1;

    /*
     * 云盘加密相关
     * */
    public final static String ENCRYPTION_CLOUD_DISK_TYPE = "CloudDisk";
    public static String ENCRYPTION_TYPE_LABEL = "encryption_type";
    public static String ENCRYPTION_KEY_LABEL = "encryption_key";
    public static final String ENCRYPTION_KEY_CONFIG_NAME = "encryptionKey";
    public static final String DEFAULT_ENCRYPTION_KEY = "serviceKey";
    public static final String TDE_KEY_USAGE = "ENCRYPT/DECRYPT";
    public static final String SERVICE_KEY_FILTERS = "[{\"Key\":\"CreatorType\", \"Values\":[\"Service\"]}]";
    public static final String TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_STANDARD = "modify_disk_encryption_key_for_standard";
    public static final String TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_CLUSTER = "modify_disk_encryption_key_for_cluster";
    public static final String TASK_MODIFY_DISK_ENCRYPTION_KEY_FOR_BASIC = "modify_disk_encryption_key_for_basic";

    /*
     * 配置变更相关
     * */
    public final static String CONFIG_NAME = "ConfigName";
    public final static String CONFIG_VALUE = "ConfigValue";

    /**
     * TDE加密相关
     */
    public static final String TASK_MODIFY_TDE = "modify_tde";
    public static final String TASK_REFRESH_KMS_SERVICE_CONFIG = "refresh_kms_service_config";
    public static final String TDE_ENABLED = "tde_enabled";
    public static final String TDE_MODE = "tde_mode";
    public static final String TDE_MODE_BYOK = "byok_key";
    public static final String TDE_MODE_SERVICE = "service_key";
    public static final String TDE_ENCRYPTION_KEY_ID = "tde_encryption_key_id";
    public static final String PARAM_ROLE_ARN = "RoleARN";
    public static final String PARAM_ENCRYPTION_KEY = "EncryptionKey";
    public static final String PARAM_TDE_STATUS = "TDEStatus";
    public static final String RES_KEY_TDE_SUPPORT_SWITCH = "TDE_SUPPORT_SWITCH";
    public static final String TDE_SUPPORT_SWITCH_OFF = "off";
    public static final String TDE_ROLEARN_TYPE = "kms";

    /*
     * 列加密（CLS）相关
     */
    public static final String TASK_MODIFY_CLS = "modify_cls";
    public static final String TASK_DESCRIBE_CLS = "describe_cls";
    public static final String CLS_ENABLED = "cls_enabled";
    public static final String CLS_KEY_MODE = "cls_key_mode";
    public static final String CLS_MODE_CLIENT_KEY = "client_key";
    public static final String CLS_MODE_KMS_KEY = "kms_key";
    public static final String CLS_MODE_NONE = "none";
    public static final String CLS_ENCRYPTION_KEY_ID = "cls_encryption_key_id";
    public static final String PARAM_CLS_STATUS = "EncryptionStatus";
    public static final String LOOSE_PARAM_CLS_STATUS = "loose_encdb";
    public static final String PARAM_CLS_KEY_MODE = "EncryptionKeyMode";
    public static final String PARAM_CLS_ENCRYPTION_KEY = PARAM_ENCRYPTION_KEY;
    public static final String PARAM_CLS_ROLE_ARN = PARAM_ROLE_ARN;
    public static final String PARAM_CLS_KEY_IS_ROTATE = "IsRotate";
    public static final String PARAM_CLS_ENCRYPTION_ALGO = "EncryptionAlgorithm";
    public static final String PARAM_CLS_WHITELIST_MODE = "WhiteListMode";



    /*
     *   主实例修改参数之后，需要同步修改只读实例的参数列表
     *  MYSQL_CUSTINSPARAM_SYNC_READINS_SET
     */
    /**
     * 参数相关
     */
    public static final String LOWER_CASE_TABLE_NAMES = "lower_case_table_names";
    public static final String LOOSE_VALIDATE_PASSWORD_LENGTH = "loose_validate_password_length";
    public static final String DEFAULT_TIME_ZONE = "default_time_zone";
    public static final String INNODB_LARGE_PREFIX = "innodb_large_prefix";
    public static final String LOOSE_OPT_RDS_AUDIT_LOG_ENABLED = "loose_opt_rds_audit_log_enabled";
    public static final String OPT_RDS_AUDIT_LOG_ENABLED = "opt_rds_audit_log_enabled";
    public static final String RDS_AUDIT_LOG_ENABLED = "rds_audit_log_enabled";

    public static final String MAX_BINLOG_CACHE_SIZE = "max_binlog_cache_size";
    public static final String LOOSE_RDS_USER_WITH_KILL_OPTION = "loose_rds_user_with_kill_option";
    public static final String RDS_USER_WITH_KILL_OPTION = "rds_user_with_kill_option";
    public static final String LOOSE_RDS_KILL_USER_LIST = "loose_rds_kill_user_list";
    public static final String RDS_KILL_USER_LIST = "rds_kill_user_list";
    public static final String INNODB_BUFFER_POOL_SIZE = "innodb_buffer_pool_size";
    public static final String INNODB_BUFFER_POOL_INSTANCES = "innodb_buffer_pool_instances";
    public static final String BP_INSTANCES_XENGINE_DEFAULT_VALUE = "1"; // bp_instances在x-engine引擎中默认为1
    public static final String INNODB_UNDO_TABLESPACES = "innodb_undo_tablespaces";

    // cold data parameters
    public static final String MYSQL_OSS_ENABLED = "loose_oss_enabled";
    public static final String MYSQL_OSS_ENDPOINT = "loose_oss_endpoint";
    public static final String MYSQL_OSS_BUCKET_NAME = "loose_oss_bucket_name";
    public static final String MYSQL_OSS_DIRECTORY = "loose_oss_directory";
    public static final String MYSQL_OSS_ACCESS_KEY_ID = "loose_oss_access_key_id";
    public static final String MYSQL_OSS_ACCESS_KEY_SECRET = "loose_oss_access_key_secret";
    public static final String MYSQL_OSS_TOKEN = "loose_oss_token";

    // io acceleration(bpe) parameters
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION = "loose_innodb_buffer_pool_extension";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_DIR = "loose_innodb_buffer_pool_extension_dir";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_SIZE = "loose_innodb_buffer_pool_extension_size";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_FLUSH_DIRTY = "loose_innodb_buffer_pool_extension_flush_dirty";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_READ_CAPACITY = "loose_innodb_buffer_pool_extension_io_read_capacity";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_WRITE_CAPACITY = "loose_innodb_buffer_pool_extension_io_write_capacity";

    public static final String COLLATION_SERVER = "collation_server";

    public static final String LOOSE_COLLATION_SERVER = "loose_collation_server";

    public static final String CHARACTER_SET_SERVER = "character_set_server";

    public static final Set<String> MYSQL_CUSTINSPARAM_SYNC_READINS_SET = Sets.newHashSet(LOWER_CASE_TABLE_NAMES,
            LOOSE_VALIDATE_PASSWORD_LENGTH,  DEFAULT_TIME_ZONE, INNODB_LARGE_PREFIX, COLLATION_SERVER, LOOSE_COLLATION_SERVER, CHARACTER_SET_SERVER, MAX_BINLOG_CACHE_SIZE);

    /**
     * 关键参数，在刷参数模板的时候需要保持不变
     * */
    public static final Set<String> MYSQL_RESERVED_PARAMS = Sets.newHashSet(
            LOWER_CASE_TABLE_NAMES, DEFAULT_TIME_ZONE, INNODB_LARGE_PREFIX,
            OPT_RDS_AUDIT_LOG_ENABLED,
            LOOSE_OPT_RDS_AUDIT_LOG_ENABLED,
            RDS_AUDIT_LOG_ENABLED,
            LOOSE_RDS_USER_WITH_KILL_OPTION, RDS_USER_WITH_KILL_OPTION, LOOSE_RDS_KILL_USER_LIST, RDS_KILL_USER_LIST, INNODB_UNDO_TABLESPACES,
            MYSQL_OSS_ENABLED, MYSQL_OSS_ENDPOINT, MYSQL_OSS_BUCKET_NAME, MYSQL_OSS_DIRECTORY, MYSQL_OSS_ACCESS_KEY_ID, MYSQL_OSS_ACCESS_KEY_SECRET,
            MYSQL_OSS_TOKEN, MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION, MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_DIR, MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_SIZE,
            MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_FLUSH_DIRTY, MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_READ_CAPACITY, MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_WRITE_CAPACITY);
    /**
     * 可用区迁移相关参数
     */
    public static final String MIGRATING_AVZ = "migratingAvz";
    public static final String MIGRATE_IN_SAME_AVZ = "migrateInSameAvz";
    public static final String MIGRATE_SLAVE_IN_SAME_AVZ = "migrateSlaveInSameAvz";
    public static final String ACTIVE_REPLICA_SET_STATUS = "activeReplicaSetStatus";

    public static final String LABEL_PARAM_SSL_ENABLE = "ssl_enabled";

    /**
     * 实例不可以用逃逸
     */
    public static final String UNAVAILABLE_INSTANCE_ESCAPE = "unavailableInstanceEscape";

    /**
     * 备库重搭相关
     */
    public static final String SLAVE_REBUILD_TYPE_FORCE_REMOTE = "2";

    /**
     * serverless接口参数
     * */
    public static final String RCU = "Rcu";
    public static final Double SERVERlESS_RCU_DEFAULT_VALUE = 1.0;

    /**
     * 本地变配的调度模式
     * */
    public static final Set<ModifyReplicaSetResourceRequest.ModifyModeEnum> LOCAL_MODIFY_POD_MODES = new HashSet<ModifyReplicaSetResourceRequest.ModifyModeEnum>() {
        {
            add(ModifyReplicaSetResourceRequest.ModifyModeEnum.TRYINPLACEUPDATEREPLICA);
            add(ModifyReplicaSetResourceRequest.ModifyModeEnum.FORCEINPLACEUPDATEREPLICA);
            add(ModifyReplicaSetResourceRequest.ModifyModeEnum.TRYINPLACEUPDATERESOURCESET);
        }
    };


    public final static Set<String> DATA_RESTORE_USAGE = new HashSet<String>() {{
        this.add(MySQLParamConstants.DOWNLOAD_BAKSET);  //备份集下载
    }};

    /**
     * 跨地域恢复相关
     */
    public static final String BACK_SCENE_TYPE_REPLICATION = "LEVEL_2_REPLICATION";
    public static final String PARAM_SNAPSHOT_ID = "SnapshotId";
    public static final String PARAM_CONSISTENT_TIME = "ConsistentTime";
    public static final String PARAM_RESTORE_TIME_POINT = "RestoreTimePoint";
    public static final String PARAM_LOCK_TYPE_LOCK = "Lock";
    public static final String PARAM_TARGET_MINOR_VERSION = "TargetMinorVersion";
    public static final String PARAM_SOURCE_REGION = "SourceRegion";
    public static final String PARAM_BACKUP_SET_REGION = "BackupSetRegion";
    public static final String SRC_INS_LEVEL_EXTRA_INFO = "SrcInsLevelExtraInfo";

    public static final String RES_KEY_STOP_DB_INSTANCE_SWITCH = "STOP_DB_INSTANCE_SWITCH";

    public static final String LABEL_ORDER_ID = "orderId";

    /**
     * 资源策略相关
     * <a href="https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXIE4XpZNxJzN67Mw4?utm_scene=team_space">算力保障策略，变配时ECS机型代系不降低</a>
     */
    public static final String LABEL_ALIGNMENT_STRATEGY = "rm.alibaba-inc.com/ecs-alignment-strategy";
    public static final String SINGLE_TENANT_RESOURCE_STRATEGY_COMPUTING = "computingPowerEnsure";
    public static final Integer RESOURCE_NOT_EXIST = 404;


    public static final String CLUSTER_MGR = "mgr";
    public static final String STORAGE_ENGINE_XENGINE = "xengine";
    public static final String STORAGE_ENGINE_INNODB = "innodb";
    /**
     * x86 转换到 arm需要配置asi参数， 这样资源调度才可以调度到arm机器
     */
    public static String ASI_CLUSTER_VALUE = "asi";
    public static String ack_CLUSTER_VALUE = "ack";
    public static String COMMON_K8S_CLUSTER_LABEL = "common.k8sCluster";
    public static String PARAM_ARCH_CHANGED = "arch_changed";//0, 1
    public static String PARAM_ARCH_LABEL = "arch";//arm, x86

    public static final String LABEL_SYNC_MODE = "sync_mode";

    public static final String SYNC_MODE_MGR = "4";

    public static final String REBUILD_REPLICA = "rebuild_replica";

    /**
     * 调度key相关
     */
    public static final String SCHEDULER_CONFIG_ARCH_KEY = "rm.alibaba-inc.com/arch";

    public static final String SCHEDULE_LABEL_SERVERLESS = "rm.alibaba-inc.com/serverless";

    /**
     * 系列相关
     */
    public static final String CATEGORY_BASIC = "basic";
    public static final String CATEGORY_STANDARD = "standard";
    public static final String CATEGORY_CLUSTER = "cluster";

    /**
     * 缩容相关
     */
    public static final Integer SHRINK_LIMIT_COUNT = 2;
    public static final Integer SHRINK_LIMIT_PERIOD_OF_DAYS = 1;
    public static final Double SHRINK_LIMIT_RATIO = 1.30;
    public static final Integer SHRINK_LIMIT_STORAGE_USED_OFFSET_GB = 500;
    public static final Integer SHRINK_LIMIT_STORAGE_USED_MIN_OFFSET_GB = 0;

    public static final Long SHRINK_COPY_SPEED_MB_PER_MINUTE = 150 * 60L;

    public static final Double OSS_APPLY_BINLOG_CNT_PER_MINUTE = 2.0D;
    public static List<String> SHRINK_SUPPORT_CATEGORY_LIST = Arrays.asList(CATEGORY_BASIC, CATEGORY_STANDARD);
    public static List<InstanceLevel.CategoryEnum> SHRINK_SUPPORT_CATEGORY_LIST_V2 = Arrays.asList(InstanceLevel.CategoryEnum.BASIC, InstanceLevel.CategoryEnum.STANDARD, InstanceLevel.CategoryEnum.CLUSTER, InstanceLevel.CategoryEnum.SERVERLESS_BASIC, InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);
    public static List<String> SHRINK_SUPPORT_STORAGE_TYPE_LIST = Arrays.asList(ECS_ClOUD_ESSD,ECS_ClOUD_AUTO, ClOUDDISK_CLOUD_SSD);

    /**
     * RDS + CK 一站式相关
     */

    // RDS大账号
    public static final String RDS_ACCOUNT = "<EMAIL>";
    public static final String DB_TYPE_CK_KEEPER = "clickhouse_keeper";
    public static final String DB_TYPE_CK_SERVER = "clickhouse_server";

    public static final String DB_TYPE_CK = "clickhouse";

    public static final String DTS_INFO = "dts_info";
    public static final String DTS_INSTANCE_ID = "dtsInstanceId";
    public static final String DTS_JOB_ID = "dtsJobId";
    public static final String DTS_ADMIN = "dts_admin";

    public static final String ANALYTIC_SYNC_MODE = "analytic_sync_mode";
    public static final String ANALYTIC_SYNC_MODE_PART = "part";
    public static final String ANALYTIC_SYNC_MODE_FULL = "full";
    public static Set<String> ANALYTIC_SYNC_MODE_SET = Sets.newHashSet(ANALYTIC_SYNC_MODE_PART, ANALYTIC_SYNC_MODE_FULL);
    //大版本升级任务流前置校验
    public final static String TASK_UPGRADE_MAJOR_VERSION_PRECHECK = "upgrade_major_version_precheck";
    //新架构云盘服务账号添加数据权限标识
    public static final String K8S_DATA_PERMISSION_IDENTIFIER = "k8s_data_permission_identifier";
    public static final String K8S_DATA_PERMISSION_IDENTIFIER_VALUE = "1";

    public final static String PRIMARY_REPLICASET_NAME = "primaryReplicaSetName";
    public final static String BACKUPSET_ID ="backupSetId";

    /**
     * 创建蓝绿部署
     */
    public final static String TASK_CREATE_BLUE_GREEN_DEPLOYMENT = "create_blue_green_deployment";
    /**
     * 删除蓝绿部署
     */
    public final static String TASK_DELETE_BLUE_GREEN_DEPLOYMENT = "delete_blue_green_deployment";
    /**
     * 切换蓝绿部署
     */
    public final static String TASK_SWITCH_BLUE_GREEN_DEPLOYMENT = "switch_blue_green_deployment";


    /**
     * 原生复制
     */
    public final static String TASK_ACTIVATE_EXTERNAL_REPLICATION = "activate_external_replication";
    public final static String TASK_DEACTIVATE_EXTERNAL_REPLICATION = "deactivate_external_replication";
    public final static String EXTERNAL_REPLICATION = "external_replication";
    public final static String OPTION_ACTIVATE = "activate";
    public final static String TASK_REBUILD_EXTERNAL_REPLICATION_CUSTINS = "rebuild_external_replication_custins";
    // 原生复制场景枚举
    public enum ExternalReplicationScenario {
        activate,       // 开启原生复制
        deactivate,     // 关闭原生复制
        rebuild,        // 重建原生复制实例
        describe        // 查询原生复制
    }


    public enum optimizedWritesTypeEnum {
        // 开启写优化
        optimized,
        // 关闭写优化
        none
    }
    public final static String DOCKERONECS_CLONE_TO_K8S_SWITCH = "DOCKERONECS_CLONE_TO_K8S_SWITCH";

    public enum readOnlyStatus {
        // 实例只读态打开
        ON,
        // 实例只读态关闭 实例正常读写
        OFF
    }

    public static final String READ_ONLY_STATUS = "read_only_status";

    public static final String GLOBAL_CLUSTER_SITENAME_TO_SUBDOMAIN_MAP_KEY = "GLOBAL_CLUSTER_SITENAME_TO_SUBDOMAIN_MAP";
    // Mysql vip可用性修改
    public final static String TASK_MYSQL_MODIFY_VIP_AVAILABLE = "mysql_modify_vip_available";

    public static final String TASK_SWITCHOVER_HOST_INSTANCES = "switchover_host_instances";
}
