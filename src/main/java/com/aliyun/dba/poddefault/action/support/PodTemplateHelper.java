package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSONObject;
import com.alicloud.apsaradb.k8s.resmanager.Constants;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.activityprovider.model.Label;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.opensearch.util.JsonUtil;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME;

@Component
public class PodTemplateHelper {

    private static final LogAgent logger = LogFactory.getLogAgent(PodTemplateHelper.class);

    @Resource
    protected DBaasMetaService dBaasMetaService;

    @Resource
    private CustinsParamService custinsParamService;

    @Resource
    private PodParameterHelper podParameterHelper;

    @Resource
    private InsArchHelper insArchHelper;

    @Resource
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    @Autowired
    private RundPodSupport rundPodSupport;
    @Autowired
    private PodCommonSupport podCommonSupport;


    /**
     * replica级别
     * 调度模板转换成资源管理器需要的模板对象
     *
     * @param podScheduleTemplate
     * @return
     */
    public ScheduleTemplate getReplicaScheduleTemplateByRole(PodScheduleTemplate podScheduleTemplate, String role) {

        if (podScheduleTemplate == null || Strings.isEmpty(role)) {
            return null;
        }
        role = role.toLowerCase();
        //之前未考虑learner角色，做个特殊处理，使之当作数据节点
        if ("learner".equals(role)) {
            role = "master";
        }
        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();

        ScheduleTemplateNodePolicy nodePolicy = new ScheduleTemplateNodePolicy();
        if (MapUtils.isNotEmpty(podScheduleTemplate.getRoleCapacity()) &&
                MapUtils.isNotEmpty(podScheduleTemplate.getRoleCapacity().get(role))) {
            List<NodeCapacity> nodeCapcities = Lists.newArrayList();
            podScheduleTemplate.getRoleCapacity().get(role).forEach((key, value) -> {
                NodeCapacity nodeCapacity = new NodeCapacity();
                nodeCapacity.setLabel(key);
                nodeCapacity.setMaxReplica(value);
                nodeCapcities.add(nodeCapacity);
            });
            nodePolicy.setNodeCapcity(nodeCapcities);
        }

        if (MapUtils.isNotEmpty(podScheduleTemplate.getRoleLabels()) &&
                MapUtils.isNotEmpty(podScheduleTemplate.getRoleLabels().get(role))) {
            NodeSelector nodeSelector = new NodeSelector();
            podScheduleTemplate.getRoleLabels().get(role).forEach((key, value) -> {
                NodeLabel nodeLabel = new NodeLabel();
                nodeLabel.setKey(key);
                nodeLabel.setValue(value);
                nodeSelector.addLabelsItem(nodeLabel);
            });
            nodePolicy.nodeSelector(nodeSelector);
        }

        scheduleTemplate.setNodePolicy(nodePolicy);

        logger.info("====>getReplicaScheduleTemplateByRole;role=" + role + "; scheduleTemplate=" + JsonUtil.toJson(scheduleTemplate));

        return scheduleTemplate;
    }


    /**
     * replicaSet级别
     * 调度模板转换成资源管理器需要的模板对象
     *
     * @param podScheduleTemplate
     * @return
     */
    public ScheduleTemplate getReplicaSetScheduleTemplate(PodScheduleTemplate podScheduleTemplate) {
        if (podScheduleTemplate == null) {
            return null;
        }
        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();

        ScheduleTemplateMetadata metadata = new ScheduleTemplateMetadata();

        if (Strings.isNotEmpty(podScheduleTemplate.getAppName())) {
            Label label = new Label();
            label.setKey(PodDefaultConstants.TEMPLATE_AFFINITY_LABEL_KEY);
            label.setValue(podScheduleTemplate.getAppName());
            metadata.addLabelsItem(label);
            scheduleTemplate.setMetadata(metadata);
        }

        if (CollectionUtils.isNotEmpty(podScheduleTemplate.getAffinitys())) {
            List<Label> affinitys = Lists.newArrayList();
            podScheduleTemplate.getAffinitys().forEach(affinity -> {
                Label affinityLabel = new Label();
                String[] tempSortString = {podScheduleTemplate.getAppName(), affinity};
                Arrays.sort(tempSortString);
                affinityLabel.setKey(tempSortString[0]);
                affinityLabel.setValue(tempSortString[1]);
                affinitys.add(affinityLabel);
            });
            scheduleTemplate.setAffinity(affinitys);
        }

        if (CollectionUtils.isNotEmpty(podScheduleTemplate.getAntiAffinitys())) {
            List<Label> antiAffinitys = Lists.newArrayList();
            podScheduleTemplate.getAntiAffinitys().forEach(antiAffinity -> {
                String[] tempSortString = {podScheduleTemplate.getAppName(), antiAffinity};
                Arrays.sort(tempSortString);
                Label antiAffinityLabel = new Label();
                antiAffinityLabel.setKey(tempSortString[0]);
                antiAffinityLabel.setValue(tempSortString[1]);
                antiAffinitys.add(antiAffinityLabel);
            });
            scheduleTemplate.setAntiAffinity(antiAffinitys);
        }

        if (MapUtils.isNotEmpty(podScheduleTemplate.getNodeLabels())) {
            List<NodeLabel> nodeLabels = podScheduleTemplate.getNodeLabels()
                    .entrySet().stream().map(entry -> {
                NodeLabel nodeLabel = new NodeLabel();
                nodeLabel.setKey(entry.getKey());
                nodeLabel.setValue(entry.getValue());
                return nodeLabel;
            }).collect(Collectors.toList());
            ScheduleTemplateNodePolicy nodePolicy = new ScheduleTemplateNodePolicy();
            NodeSelector nodeSelector = new NodeSelector();
            nodeSelector.setLabels(nodeLabels);
            nodePolicy.setNodeSelector(nodeSelector);
            scheduleTemplate.setNodePolicy(nodePolicy);
        }
        logger.info("====>getReplicaSetScheduleTemplate scheduleTemplate=" + JsonUtil.toJson(scheduleTemplate));
        return scheduleTemplate;
    }

    /**
     * Construct blank node policy
     */
    public static ScheduleTemplateNodePolicy constructBlankNodePolicy() {
        NodeSelector nodeSelector = new NodeSelector();
        ScheduleTemplateNodePolicy nodePolicy = new ScheduleTemplateNodePolicy();
        nodePolicy.setNodeSelector(nodeSelector);
        return nodePolicy;
    }

    /**
     * 创建实例时指定模板名，根据模板名+实例id在模板表里查询模板
     * 变配/重搭 需要根据实例id查询实例资源调度模板
     *
     * @param requestId
     * @param rsTemplateName
     * @param loginId
     * @return
     * @throws com.aliyun.apsaradb.dbaasmetaapi.ApiException
     */
    public PodScheduleTemplate getPodTemplateByRsTemplateNameAndLoginId(String requestId, String rsTemplateName, String loginId) throws Exception {

        if (Strings.isBlank(rsTemplateName)) {
            return null;
        }

        RsScheduleTemplate rsScheduleTemplate = dBaasMetaService.getDefaultClient().getRsScheduleTemplate(requestId, loginId, rsTemplateName, true);// waiting metadb api @qiaoyun

        if (rsScheduleTemplate == null) {
            //不存在的模板，报错
            return null;

        } else if (Strings.isBlank(rsScheduleTemplate.getTemplate())) {
            //空模板
            return null;
        }

        PodScheduleTemplate podScheduleTemplate = JsonUtil.fromJson(rsScheduleTemplate.getTemplate(), PodScheduleTemplate.class);

        logger.info("====>podScheduleTemplate=" + JsonUtil.toJson(podScheduleTemplate));
        return podScheduleTemplate;
    }


    public RsScheduleTemplate deletePodTemplateByRsTemplateNameAndLoginId(String requestId, String rsTemplateName, String loginId) throws Exception {
        if (StringUtils.isEmpty(rsTemplateName)) {
            throw new Exception("rsTemplateName is null.");
        }
        RsScheduleTemplate rsScheduleTemplate = dBaasMetaService.getDefaultClient().deleteRsScheduleTemplate(requestId, loginId, rsTemplateName);
        if (rsScheduleTemplate == null) {
            throw new Exception("rsTemplateName is not exist.");
        }
        return rsScheduleTemplate;
    }


    /**
     * 获取ReplicaSet绑定的资源模板
     *
     * @param replicaSet
     * @return
     * @throws Exception
     */
    public PodScheduleTemplate getReplicaSetPodScheduleTemplate(ReplicaSet replicaSet) throws Exception {
        String requestId = RequestSession.getRequestId();
        String rsTemplateName = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSet.getName(), CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME);
        PodScheduleTemplate podScheduleTemplate = null;
        if (StringUtils.isNotBlank(rsTemplateName)) {
            if (StringUtils.isBlank(rsTemplateName)) {
                return null;
            }
            if (StringUtils.startsWith(rsTemplateName, PodDefaultConstants.RS_TEMPLATE_SYS_PREFIX)) {
                // 资源调度系统模板
                podScheduleTemplate = getSystemPodTemplateFromMeta(requestId, rsTemplateName);
            } else {
                // 资源调度用户模板
                User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), null);
                podScheduleTemplate = getPodTemplateByRsTemplateNameAndLoginId(
                        requestId,
                        rsTemplateName,
                        user.getBid() + "_" + user.getAliUid());
            }
        }
        return podScheduleTemplate;
    }

    /**
     * 判断是否用大规格资源池：高可用 & 基础版 cpu >= 8核时，用mysqlx资源池
     * */
    public boolean isMySqlXResource(InstanceLevel instanceLevel, String insTypeStr) {
        return instanceLevel.getCategory() == InstanceLevel.CategoryEnum.STANDARD ||
                (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC && instanceLevel.getCpuCores() >= 8) ||
        //高可用只读或主节点，以及基础版只读节点
                (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.BASIC && ReplicaSet.InsTypeEnum.READONLY.getValue().equals(insTypeStr)) ||
                instanceLevel.getCategory() == InstanceLevel.CategoryEnum.CLUSTER;
    }


    /**
     * 根据模板名获取系统资源模板
     *
     * @param requestId
     * @param rsTemplateName
     * @return
     * @throws Exception
     */
    public Pair<String, ScheduleTemplate> getBizSysScheduleTemplateByName(String requestId,String rsTemplateName) throws Exception {
        PodScheduleTemplate podScheduleTemplate = getSystemPodTemplateFromMeta(requestId, rsTemplateName);
        ScheduleTemplate scheduleTemplate = getReplicaSetScheduleTemplate(podScheduleTemplate);
        return new ImmutablePair<>(rsTemplateName, scheduleTemplate);
    }


    /**
     * 根据业务形态获取在多租户形态下对应的系统调度模板(for IO 加速)
     *
     * @param podType               容器运行时
     * @param bizType               业务类型
     * @param dbEngine              引擎
     * @param instanceLevel         规格
     * @param isSingleTenant        是否单租户
     * @param insTypeStr            实例类型（只读， 主节点）
     * @param replicaSetName        实例名（用于配置spread key，只读实例配置主机打散策略需要）
     * @param primaryReplicaSetName 主实例名（用于配置spread key，只读实例配置主机打散策略需要）
     * @param uid
     * @return 系统调度模板名称，系统调度模板
     * @throws Exception
     */


    public Pair<String, ScheduleTemplate> getBizSysScheduleTemplate(PodType podType,
                                                                    ReplicaSet.BizTypeEnum bizType,
                                                                    String dbEngine,
                                                                    InstanceLevel instanceLevel,
                                                                    boolean isSingleTenant,
                                                                    String insTypeStr,
                                                                    String replicaSetName,
                                                                    String primaryReplicaSetName, String uid) throws Exception {
        String requestId = RequestSession.getRequestId();
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
            // RunD使用自己的通用调度策略
            return new ImmutablePair<>(RundPodSupport.RES_TEMPLATE_NAME, rundPodSupport.generateBaseScheduleTemplate(replicaSetName, uid, podType));
        }
        String rsTemplateName = getBizSysScheduleTemplateName(requestId, bizType, dbEngine, instanceLevel, isSingleTenant, insTypeStr,
                uid);
        logger.info("rsTemplateName:{} requestId:{}", rsTemplateName, requestId);

        ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
        if (StringUtils.isNotBlank(rsTemplateName)) {
            PodScheduleTemplate podScheduleTemplate = getSystemPodTemplateFromMeta(requestId, rsTemplateName);
            scheduleTemplate = getReplicaSetScheduleTemplate(podScheduleTemplate);
        }

        if (!isSingleTenant && PodParameterHelper.isAliYun(bizType)) {
            if (scheduleTemplate.getSpread() == null) {
                scheduleTemplate.setSpread(new ArrayList<>());
            }
            scheduleTemplate.getSpread().addAll(getCommonSpreads(replicaSetName, uid));
            if (ReplicaSet.InsTypeEnum.READONLY.getValue().equals(insTypeStr) && StringUtils.isNotEmpty(primaryReplicaSetName)) {
                logger.info("add primary ins spread key {}", primaryReplicaSetName);
                scheduleTemplate.getSpread().addAll(getReadInsSpread(replicaSetName, primaryReplicaSetName));
            }
        }

        // for singleTenant, lafite will determine if give free core
        if (isSingleTenant && PodParameterHelper.isAliYun(bizType)) {
            scheduleTemplate.setComputerResourceNormalize(true);
        }

        // 先判断是否是serverless服务,以及是否是新创建的实例
        if(ServerlessConstant.SERVERLESS_CATEGORY_TYPE.contains(instanceLevel.getCategory().toString().toLowerCase())){
            // 先以primaryReplicaSetName来进行host的检查，如果没有的话，再用自己的
            String targetReplicaSetName = StringUtils.isBlank(primaryReplicaSetName)? replicaSetName : primaryReplicaSetName;
            String sourceHostName = insArchHelper.getHostNameByReplicaSetName(requestId, targetReplicaSetName);
            if(StringUtils.isNotBlank(sourceHostName)){
                String originArch = insArchHelper.getOriginArch(requestId, sourceHostName);
                if(!insArchHelper.supportArchMix(uid, targetReplicaSetName, requestId)){
                    // 用targetReplicaSetName来进行架构的判断
                    logger.info("not support ArchMix for primaryReplicaSetName={}, replicaSetName={} set originArch={}, targetReplicaSetName:{}.",
                                    primaryReplicaSetName, replicaSetName, originArch, targetReplicaSetName);
                    scheduleTemplate = insArchHelper.updateArchLabelForScheduleTemplate(scheduleTemplate, originArch);
                }
            }
        }

        if(PodCommonSupport.isArm(instanceLevel)){
            ScheduleTemplateNodePolicy nodePolicy = scheduleTemplate.getNodePolicy();
            if(Objects.isNull(nodePolicy)){
                nodePolicy = new ScheduleTemplateNodePolicy();
                scheduleTemplate.setNodePolicy(nodePolicy);
            }
            NodeSelector nodeSelector = nodePolicy.getNodeSelector();
            if(Objects.isNull(nodeSelector)){
                nodeSelector = new NodeSelector();
                nodePolicy.setNodeSelector(nodeSelector);
            }
            NodeLabel armLabel = new NodeLabel();
            armLabel.setKey(Constants.ArchLabel);
            armLabel.setValue(CreateReplicaSetDto.ArchEnum.ARM.toString());
            //all of aliyun arm instances use asi
            NodeLabel asiLabel = new NodeLabel();
            asiLabel.setKey("rm.alibaba-inc.com/isAsi");
            asiLabel.setValue("true");
            nodeSelector.getLabels().add(armLabel);
            nodeSelector.getLabels().add(asiLabel);
        }
        // 加入混开策略
        scheduleTemplate.setResourceGuaranteePolicies(poddefaultResourceGuaranteeModelService.getAllResourceGuaranteeLevelMap(uid, instanceLevel));

        logger.info("====>getBizSysScheduleTemplate scheduleTemplate=" + JsonUtil.toJson(scheduleTemplate));
        return new ImmutablePair<>(rsTemplateName, scheduleTemplate);
    }

    /**
     * 只读实例打散策略：和主实例进行主机级别打散
     * */
    private List<Spread> getReadInsSpread(String replicaSetName, String primaryReplicaSetName) {
        List<Spread> spreads = new ArrayList<>();

        // 只读实例和主实例 尝试打散
        Spread primaryInsSpread = new Spread();
        primaryInsSpread.spreadKey(primaryReplicaSetName);
        primaryInsSpread.setSpreadPolicy(Spread.SpreadPolicyEnum.TRY_SCATTER);
        primaryInsSpread.setSpreadLevel(Constants.SpreadHost);
        spreads.add(primaryInsSpread);
        return spreads;
    }

    protected List<Spread> getCommonSpreads(String replicaSetName, String uid) {
        List<Spread> spreads = new ArrayList<>();
        // 同一个实例内的节点强制打散
        Spread insSpread = new Spread()
                .spreadKey(replicaSetName)
                .spreadPolicy(Spread.SpreadPolicyEnum.FORCE_SCATTER)
                .spreadLevel(Constants.SpreadHost);
        spreads.add(insSpread);

        // 同一个用户的实例尝试打散
        Spread uidSpread = new Spread()
                .spreadKey(uid)
                .spreadPolicy(Spread.SpreadPolicyEnum.TRY_SCATTER)
                .spreadLevel(Constants.SpreadHost);
        spreads.add(uidSpread);


        return spreads;
    }

    /**
     * 根据业务形态获取在多租户形态下对应的系统调度模板名称
     */
    public String getBizSysScheduleTemplateName(String requestId,
                                                ReplicaSet.BizTypeEnum bizType,
                                                String dbEngine,
                                                InstanceLevel instanceLevel,
                                                boolean isSingleTenant, String insTypeStr, String uid) {
        logger.info("requestId:{} biztype:{} dbEngine:{} instanceLevel:{} isSingelTenant:{} insTypeStr:{}, uid:{} ",
                requestId, bizType, dbEngine, JSONObject.toJSONString(instanceLevel), isSingleTenant, insTypeStr, uid);

        if (PodParameterHelper.isAliGroup(bizType) || isSingleTenant) {
            // 集团使用的是用户资源模板
            // 单租户不需要使用资源模板
            return "";
        }
        if (StringUtils.equalsIgnoreCase(dbEngine, "XDB")) {
            //xdb暂时没有系统资源模板
            return "";
        }

        if (isMySqlXResource(instanceLevel, insTypeStr)) {
            //云上高可用的系统调度模板
            return PodDefaultConstants.RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_HA;
        } else if (instanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_BASIC || instanceLevel.getCategory() == InstanceLevel.CategoryEnum.SERVERLESS_STANDARD) {
            return PodDefaultConstants.RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS_V2;
        }
        return "";
    }

    /**
     * 从MetaDB中获取系统调度模板
     *
     * @param requestId
     * @param rsTemplateName
     * @return
     * @throws Exception
     */
    private PodScheduleTemplate getSystemPodTemplateFromMeta(String requestId, String rsTemplateName) throws Exception {
        RsScheduleTemplate rsScheduleTemplate = null;
        try {
            rsScheduleTemplate = dBaasMetaService.getDefaultClient().getRsScheduleTemplate(requestId, PodDefaultConstants.RS_TEMPLATE_ADMIN_USER, rsTemplateName, true);
        } catch (ApiException e) {
            if (e.getCode() != 404) {
                throw e;
            }
            logger.warn("cannot find sys template {}", rsTemplateName);
        }
        if (rsScheduleTemplate == null || Strings.isBlank(rsScheduleTemplate.getTemplate())) {
            return null;
        }
        return JsonUtil.fromJson(rsScheduleTemplate.getTemplate(), PodScheduleTemplate.class);
    }


    /**
     * 更新资源调度模板
     *
     * @param requestId
     * @param userId
     * @param bizType
     * @param template
     * @return
     * @throws Exception
     */
    public String updatePodScheduleTemplate(String requestId, String userId, String bizType, RsScheduleTemplate template) throws Exception {
        RsScheduleTemplate rsScheduleTemplate = dBaasMetaService.getDefaultClient().updateRsScheduleTemplate(requestId, userId, bizType, template);
        return rsScheduleTemplate.getTemplate();
    }

    /**
     * 创建资源调度模板
     *
     * @param requestId
     * @param userId
     * @param template
     * @return
     * @throws Exception
     */
    public String createPodScheduleTemplate(String requestId, String userId, RsScheduleTemplate template) throws Exception {
        RsScheduleTemplate rsScheduleTemplate = dBaasMetaService.getDefaultClient().createRsScheduleTemplate(requestId, userId, template);
        return rsScheduleTemplate.getTemplate();
    }

    /**
     * insert custins and template relation to custins_param table
     *
     * @param custInsId
     * @param rsTemplateName
     */
    public void createCustInsAndTemplcateRelation(int custInsId, String rsTemplateName) {

        if (Strings.isNotEmpty(rsTemplateName)) {
            custinsParamService.createCustinsParam(new CustinsParamDO(custInsId, CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, rsTemplateName));
        }

    }

    /**
     * @param key
     * @param value
     * @return
     */
    public List<NodeLabel> getNodeLabelFromParams(String key, String value) {
        Validate.notEmpty(key, "null key");
        Validate.notEmpty(value, "null value");
        List<NodeLabel> nodeLabels = Lists.newArrayList();

        NodeLabel nodeLabel = new NodeLabel();
        nodeLabel.setKey(key);
        nodeLabel.setValue(value);
        nodeLabels.add(nodeLabel);

        return nodeLabels;

    }

    /**
     * check template
     *
     * @param templateString
     * @throws RdsException
     */
    public void checkTemplateString(String templateString) throws RdsException {
        PodScheduleTemplate podScheduleTemplate = JsonUtil.fromJson(templateString, PodScheduleTemplate.class);
        if (Strings.isEmpty(podScheduleTemplate.getAppName())) {
            throw new RdsException(MysqlErrorCode.INVALID_RSESOURCE_SCHEDULE_TEMPLATE.toArray());
        }
    }

    /**
     * 实例进行变配和重搭时，取对应的资源调度模板
     * */
    public ScheduleTemplate getReplicaSetScheduleTemp(ReplicaSet replicaSet, InstanceLevel instanceLevel, boolean isSingleTenant, PodType podType) throws Exception {
        if(podType == null) {
            podType = podCommonSupport.getReplicaSetRuntimeType(replicaSet.getName(), instanceLevel);
        }
        // 云上统一取系统模板
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
            return getBizSysScheduleTemplate(
                    podType,
                    replicaSet.getBizType(),
                    replicaSet.getService(),
                    instanceLevel,
                    isSingleTenant,
                    replicaSet.getInsType().getValue(),
                    replicaSet.getName(),
                    replicaSet.getPrimaryInsName(), podParameterHelper.getUidByLoginId(replicaSet.getUserId())
            ).getValue();
        } else {
            return getReplicaSetScheduleTemplate(getReplicaSetPodScheduleTemplate(replicaSet));
        }
    }

    /**
     * 实例进行变配和重搭时，取对应的资源调度模板(for IO 加速)
     * */
    //todo：需要修改
    public ScheduleTemplate getReplicaSetScheduleTemp(ReplicaSet replicaSet, InstanceLevel instanceLevel, boolean isSingleTenant, boolean ioAccelerationEnabled) throws Exception {
        // 云上统一取系统模板
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
            return getBizSysScheduleTemplate(
                    PodType.POD_RUNC,   //IO加速只能在RunC中
                    replicaSet.getBizType(),
                    replicaSet.getService(),
                    instanceLevel,
                    isSingleTenant,
                    replicaSet.getInsType().getValue(),
                    replicaSet.getName(),
                    replicaSet.getPrimaryInsName(),
                    podParameterHelper.getUidByLoginId(replicaSet.getUserId())
            ).getValue();
        } else {
            return getReplicaSetScheduleTemplate(getReplicaSetPodScheduleTemplate(replicaSet));
        }
    }


    /**
     * 设置主机维度的打散策略
     *
     * @param resourceRequest
     * @param replicaSetName
     */
    public void setSpecSchedulerConfigSpread(ReplicaSetResourceRequest resourceRequest, String replicaSetName) {
        if (StringUtils.isEmpty(resourceRequest.getUid())) {
            logger.error("resourceRequest uid is null.");
            throw new RuntimeException("resourceRequest uid is null.");
        }
        List<Spread> spreads = getCommonSpreads(replicaSetName, resourceRequest.getUid());

        if (resourceRequest.getScheduleTemplate() == null) {
            ScheduleTemplate scheduleTemplate = new ScheduleTemplate();
            resourceRequest.setScheduleTemplate(scheduleTemplate);
        }
        if (resourceRequest.getScheduleTemplate().getSpread() == null) {
            resourceRequest.getScheduleTemplate().setSpread(new ArrayList<>());
        }

        // 已经配置打散Key的情况下，避免重复配置
        List<String> spreadKeys = resourceRequest.getScheduleTemplate().getSpread().stream().map(Spread::getSpreadKey).collect(Collectors.toList());
        for (Spread s : spreads) {
            if (spreadKeys.contains(s.getSpreadKey())) {
                logger.info("{} spread key {} already existed.", resourceRequest.getReplicaSetName(), s.getSpreadKey());
                continue;
            }
            resourceRequest.getScheduleTemplate().getSpread().add(s);
        }

    }

    /**
     * 获取实例对应的系统调度模板名称
     */
    public String getRsTemplateNameForReplicaSet(String requestId, ReplicaSet replicaSet) throws ApiException {
        boolean isSingleTenant = podParameterHelper.isSingleTenant(replicaSet);
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId,
                replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);

        return getBizSysScheduleTemplateName(requestId,
                replicaSet.getBizType(), replicaSet.getService(), instanceLevel, isSingleTenant, replicaSet.getInsType().toString(),
                podParameterHelper.getUidByLoginId(replicaSet.getUserId())
        );
    }
}
