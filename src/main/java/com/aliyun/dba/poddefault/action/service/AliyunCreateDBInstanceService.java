package com.aliyun.dba.poddefault.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.base.support.HashUtils;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dbs.support.DbsSupport;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.poddefault.action.support.urd.URDWalker;
import com.aliyun.dba.poddefault.action.support.urd.URDZoneDescriptor;
import com.aliyun.dba.rdscustom.action.support.ECSActionConstant;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.service.SlrCheckService.SERVICE_LINKED_ROLE_MYSQL;
import static com.aliyun.dba.base.support.MySQLParamConstants.*;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;
import static com.aliyun.dba.support.property.ParamConstants.*;

/**
 * <AUTHOR> on 2020/6/12.
 */
@Service
public class AliyunCreateDBInstanceService {

    private static final LogAgent logger = LogFactory.getLogAgent(AliyunCreateDBInstanceService.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private PodAvzSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private ClusterBackUpService clusterBackUpService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private AligroupService aligroupService;
    @Resource
    private LocalCacheService cacheService;
    @Resource
    private AliyunInstanceDependency dependency;
    @Resource
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private ColdDataService coldDataService;
    @Autowired
    private MysqlParameterHelper mysqlParaHelper;
    @Resource
    private MySQLServiceImpl mySQLservice;
    @Autowired
    private WhitelistTemplateService whitelistTemplateService;
    @Resource
    protected RundPodSupport rundPodSupport;
    @Resource
    protected CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Resource
    private SlrCheckService slrCheckService;


    Cache<String,String> zoneMapping = CacheBuilder.newBuilder().expireAfterWrite(Duration.ofMinutes(10)).build();

    /**
     * 创建实例
     *
     * @category CreateReadDBInstance
     */
    public Map<String, Object> createDBInstance(CustInstanceDO custins, Map<String, String> params) {
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            PodCreateInsParam metaParam = new PodCreateInsParam(dependency, params);
            // 初始化实例基础信息
            metaParam
                    .setUserId()                        // 初始化用户新消息
                    .setClusterName()                   // 初始化cluster信息，集团实例的cluster信息在判断TDDL后补录
                    .setInstanceName()                  // 初始化实例名
                    .setDBType()                        // 初始化数据库类型
                    .setDBVersion()                     // 初始化数据库版本
                    .setTargetMinorVersion()            // 初始化内核小版本
                    .setAutoUpgradeMinorVersion()       // 初始化版本升级策略
                    .setPortStr()                       // 初始化端口
                    .setDBName()                        // 设置数据库
                    .setAccount()                       // 设置初始化账号
                    .setDBEngine()                      // XDB | MySQL
                    .setConnType()                      // 连接类型
                    .setTimezone()                      // 初始化时区
                    .setTddlClusterName()               // 初始化集团TDDL配置
                    .setTddlRegionConfig()              // 初始化集团TDDL配置
                    .setClassCode()                     // 规格
                    .setInstanceLevel()                 // class code
                    .setInstructionSetArch()            // 初始化实例架构类型（x86 or arm or x86hg）, 依赖InstanceLevel 初始化
                    .setDispenseModeForce()             // 强制设置为主可用区模式
                    .setAvzInfo()                       // 初始化AvzInfo
                    .setRegionId()                      // 获取region
                    .setSnapshotId()                    // 设置备份集信息（如果有）
                    .setDisasterRestore()               // 设置跨地域恢复 需要在setSnapshotId之后设置
                    .setBizType()                       // aligroup | aliyun
                    .setIsForMigrate()                  // 设置migrate标（如果有）
                    .setIsTDDL()                        // 设置TDDL标
                    .setIsPhysical()                    // 设置Physical标
                    .setIsDHG()                         // 设置主机组信息
                    .setIsSingleNode()                  // 设置单节点标
                    .setDescription()                   // 设置实例的描述
                    .setOrderId()                       // 设置OrderID
                    .setAccessId()                      // 设置访问ID
                    .setExternalReplication()           // 设置原生复制模式
                    .setPodType();                      // 设置容器运行时


            // 初始化安全相关参数
            metaParam
                    .setIPWhiteList()
                    .setWhitelistTemplateList()
                    .setTemplateIdList()
                    .setRoleArn();

            // 初始化链路相关参数
            metaParam
                    .setVpcId()
                    .setVswitchId()
                    .setConnectionString()
                    .setVpcInstanceId()
                    .setIpAddress();
            // 初始化参数相关参数
            metaParam
                    .setCharSet()
                    .setParamGroupId()
                    .setCustomMysqlParams();
            // 初始化资源相关参数
            metaParam
                    .setResourceGroupId()
                    .setCompressionMode()
                    .setDiskSize()
                    .setDiskType()
                    .setURD() // 设置实例节点的role
                    .resetBizTypeAndDiskTypeForAliGroup()
                    .setInsTypeDesc(ReplicaSet.InsTypeEnum.MAIN)
                    // 设置网络协议栈
                    .setNetProtocol();
            // 备份相关
            metaParam
                    .setBakRetention()
                    .setPreferredBackupTime()
                    .setPreferredBackupPeriod();

            // 从CDM恢复
            metaParam
                    .setRecoverFromCDM();

            //DAS相关
            metaParam.setStorageAutoScale();

            //AutoPL配置相关
            metaParam.setAutoPLConfig();
            //coldData config
            metaParam.setColdDataConfig();

            // 初始化通用盘
            metaParam.setGeneralCloudDiskConfig();

            // 初始化已删除实例备份集恢复
            metaParam.setRebuildDeleteInsConfig();

            // 初始化是否自动创建代理参数
            metaParam.setAutoCreateProxy();

            // optimized writes
            metaParam.setInitOptimizedWrites();

            //vbm形态相关
            metaParam.initVbmConfig();

            URDZoneDescriptor masterZone = metaParam.getUrd().getMasterZoneDesc();
            metaParam.setEncryptionKey();
            metaParam.checkTdeAndClsEncryptionKey();

            boolean isSingleTenant = metaParam.isSingleTenant();

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(metaParam.getBid());
            replicaSetResourceRequest.setUid(metaParam.getUid());
            replicaSetResourceRequest.setPort(metaParam.getPortStr());
            replicaSetResourceRequest.setInsType(metaParam.getInsType().toString());
            replicaSetResourceRequest.setReplicaSetName(metaParam.getDbInstanceName());
            replicaSetResourceRequest.setDomainPrefix(metaParam.getConnectionString());
            replicaSetResourceRequest.setDbType(metaParam.getDbType());
            replicaSetResourceRequest.setDbVersion(metaParam.getDbVersion());
            replicaSetResourceRequest.setConnType(metaParam.getConnType());
            replicaSetResourceRequest.setBizType(metaParam.getBizType().toString());
            replicaSetResourceRequest.setClassCode(metaParam.getClassCode());
            replicaSetResourceRequest.setStorageType(metaParam.getDiskType());
            replicaSetResourceRequest.setDiskSize(metaParam.getDiskSize());
            replicaSetResourceRequest.setVpcId(metaParam.getVpcId());
            replicaSetResourceRequest.setVswitchID(metaParam.getVswitchId());
            replicaSetResourceRequest.setCloudInstanceIp(metaParam.getIpAddress());
            replicaSetResourceRequest.setVpcInstanceId(metaParam.getVpcInstanceId());
            replicaSetResourceRequest.setSubDomain(masterZone.getRegion());
            replicaSetResourceRequest.setRegionId(masterZone.getRegionId());
            replicaSetResourceRequest.setSingleTenant(isSingleTenant);
            replicaSetResourceRequest.ignoreCreateVpcMapping(true);                    //反向VPC的资源申请下沉到任务流
            replicaSetResourceRequest.setComment(metaParam.getDescription());         //实例描述

            replicaSetResourceRequest.setTrafficPolicy(metaParam.getTrafficPolicy());  // DBStack专用，公共云不感知

            replicaSetResourceRequest.setInternetProtocol(metaParam.getNetProtocol()); // IPV6双栈协议支持


            // 云盘加密相关
            replicaSetResourceRequest.setEncryptionKey(metaParam.getEncryptionKey());
            replicaSetResourceRequest.setEncryptionType(metaParam.getEncryptionType());

            //增加了autopl配置参数
            replicaSetResourceRequest.setProvisionedIops(metaParam.getProvisionedIops());
            replicaSetResourceRequest.setBurstingEnabled(metaParam.isBurstingEnabled());

            // set labels
            replicaSetResourceRequest.putLabelsItem(PodDefaultConstants.LABEL_ORDER_ID, metaParam.getOrderId());
            replicaSetResourceRequest.putLabelsItem(ACCESS_ID_RESOURCE, metaParam.getAccessId());

            // 通用云盘相关
            replicaSetResourceRequest.setGeneralCloudDisk(metaParam.getGeneralCloudDisk());

            // OptimizedWrites
            replicaSetResourceRequest.setInitOptimizedWrites(metaParam.isInitOptimizedWrites());
            boolean isRebuildFromPengine = false;
            // 选择对应版本镜像
            String serviceSpecTag = null;
            CustInstanceDO srcCustins = null;
            if (StringUtils.isNumeric(metaParam.getSourceDBInstanceId())) {
                srcCustins = mysqlParaHelper.getAndCheckCustInstanceById("sourcedbinstanceid");
                if(!Objects.equals(srcCustins.getKindCode(), KIND_CODE_NEW_ARCH)){
                    //从老架构重建来的，取最新minor version
                    serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                            null,
                            metaParam.getBizType(),
                            metaParam.getDbType(),
                            metaParam.getDbVersion(),
                            metaParam.getDbEngine(),
                            KIND_CODE_NEW_ARCH,
                            metaParam.getInstanceLevel(),
                            metaParam.getDiskType(),
                            metaParam.isDhg(),
                            false,
                            null);
                    isRebuildFromPengine = true;
                }else {
                    //从回收站恢复的，直接取原实例的specTag, 如果原实例的Tag找不到，则尝试用最新的，防止实例恢复报错
                    serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId,
                            Integer.valueOf(metaParam.getSourceDBInstanceId()));

                    InstanceLevel sourceInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevelById(requestId,
                            Long.valueOf(metaParam.getSourceCustInstanceDO().getLevelId()), null);
                    if (PodCommonSupport.isArchChange(sourceInstanceLevel, metaParam.getInstanceLevel())) {
                        // 如果CPU架构发生了变化，将spec设置为空，使用规格绑定的spec去找最新的去恢复
                        logger.info("source dbInstance and target dbInstance arch change, reset origin service spec tag to null");
                        serviceSpecTag = null;
                    }
                }
            }
            if (mySQLservice.isRebuildBackupSet(getParameterValue(params, ParamConstants.BACKUP_SET_ID))) {
                String bakServiceSpecTag = params.get("bakServiceSpecTag");
                if (StringUtils.isNotBlank(bakServiceSpecTag)) {
                    serviceSpecTag = bakServiceSpecTag;
                }
            }
            if (StringUtils.isEmpty(serviceSpecTag)) {
                boolean isContainsOfflineMinorVersion = metaParam.isRestoreFromBackupSet(); //备份集恢复的默认可以取到offline版本
                serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                        metaParam.getTargetMinorVersion(),
                        metaParam.getBizType(),
                        metaParam.getDbType(),
                        metaParam.getDbVersion(),
                        metaParam.getDbEngine(),
                        KindCodeParser.KIND_CODE_NEW_ARCH,
                        metaParam.getInstanceLevel(),
                        metaParam.getDiskType(),
                        metaParam.isDhg(),
                        isContainsOfflineMinorVersion,
                        null);
            }
            if (StringUtils.isEmpty(serviceSpecTag)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }

            //todo:内核小版本需要使用最新的
            // IO加速的内核版本必须大于20221231
            if (metaParam.isIoAccelerationEnabled()) {
                podCommonSupport.checkIoAccelerationSupportedMinorVersion(serviceSpecTag);
            }
            // rund 有最低内核版本要求
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(metaParam.getPodType().getRuntimeType()) && !rundPodSupport.routeToRund(serviceSpecTag, metaParam.isInitOptimizedWrites())) {
                //reset podType to RunC
                metaParam.setPodType(PodType.POD_RUNC);
            }
            // 恢复实例场景，to runc
            if (StringUtils.isNotBlank(metaParam.getSourceDBInstanceId())) {
                //reset podType to RunC
                metaParam.setPodType(PodType.POD_RUNC);
            }
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(metaParam.getPodType().getRuntimeType())) {
                Boolean slrAccess = slrCheckService.checkServiceLinkedRole(metaParam.getUid(), requestId, SERVICE_LINKED_ROLE_MYSQL, false);
                if(!slrAccess){
                    //reset podType to RunC
                    metaParam.setPodType(PodType.POD_RUNC);
                }
            }

            // 设置资源模板相关
            metaParam.setRsTemplate();
            replicaSetResourceRequest.setScheduleTemplate(metaParam.getScheduleTemplate());
            replicaSetResourceRequest.setDedicatedHostGroupId(metaParam.getDHGHostGroupId());
            replicaSetResourceRequest.setDedicatedBizGroup(metaParam.getDedicatedBizGroup());

            //cold Data limit minor version
            if (metaParam.isColdDataEnabled())
            {
                podCommonSupport.checkColdDataSupportLimit(requestId, metaParam.getInstanceLevel(), metaParam.getDbEngine(),  metaParam.getDbVersion(), metaParam.getDiskType(), serviceSpecTag, null, metaParam.getUid(), replicaSetResourceRequest.getRegionId());
            }

            // check compressionMode support
            if (CloudDiskCompressionHelper.isCompressionModeOn(metaParam.getCompressionMode())) {
                cloudDiskCompressionHelper.checkCompressionSupportLimit(requestId, metaParam.getInstanceLevel(), metaParam.getDbEngine(), metaParam.getUid(), replicaSetResourceRequest.getRegionId(), Optional.ofNullable(metaParam.getDiskSizeGBBeforeCompression()).map(Integer::longValue).orElse(null), metaParam.getDiskType(), true);
            }

            // 校验ARM支持xengine需要在8.0的内核版本
            if (StringUtils.equalsIgnoreCase(CreateReplicaSetDto.ArchEnum.ARM.name(), metaParam.getInstructionSetArch())) {
                podCommonSupport.checkARMSupportXEngineMinorVersion(serviceSpecTag, metaParam.getParamGroupId());
            }
            // 校验X86海光仅支持20241231及以后版本
            if (StringUtils.equalsIgnoreCase(X86HG_ARCH, metaParam.getInstructionSetArch())) {
                podCommonSupport.checkX86HGSupportMinorVersion(serviceSpecTag);
            }

            replicaSetResourceRequest.setComposeTag(serviceSpecTag);

            // 专属集群和集团使用eni
            boolean isENI = metaParam.isDhg() || PodParameterHelper.isAliGroup(metaParam.getBizType());
            replicaSetResourceRequest.setEniDirectLink(isENI);
            replicaSetResourceRequest.setDiskSize(metaParam.getDiskSize());  //用户可见的磁盘空间
            Map<Replica.RoleEnum, String> roleHostNameMapping = podParameterHelper.getRoleHostNameMapping();

            // Build replicaResource
            // 遍历节点申请资源
            List<ReplicaResourceRequest> replicaResources = new ArrayList<>();
            metaParam.getUrd().walkNode(new URDWalker() {
                @Override
                public void walkNode(URDZoneDescriptor zoneDescriptor, Replica.RoleEnum role) throws Exception {
                    ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                    replicaResourceRequest.setStorageType(metaParam.getDiskType());
                    // 设置资源模板
                    replicaResourceRequest.setScheduleTemplate(metaParam.getScheduleTemplate());
                    replicaResourceRequest.setRole(role.toString());
                    // 大客户主机组指定机器
                    replicaResourceRequest.setHostName(roleHostNameMapping.get(role));
                    // 设置是否是单租户
                    replicaResourceRequest.setSingleTenant(isSingleTenant);
                    replicaResourceRequest.setZoneId(zoneDescriptor.getZoneId());

                    if (metaParam.getPodScheduleTemplate() != null) {
                        replicaResourceRequest.setScheduleTemplate(
                                dependency.getPodTemplateHelper().getReplicaScheduleTemplateByRole(metaParam.getPodScheduleTemplate(), role.toString()));
                    }
//                    replicaResourceRequest.setWeight(zoneDescriptor.getZoneWeight());
                    // 根据节点角色设置参数
                    if (metaParam.getDbEngine().equalsIgnoreCase("XDB") && role.equals(Replica.RoleEnum.LOGGER)) {
                        setXDBReplicaResourceForLogger(requestId, metaParam, replicaResourceRequest);
                    } else {
                        setReplicaResourceForDataNode(metaParam, replicaResourceRequest);
                    }
                    // 设置rund 网络相关配置
                    if (PodType.POD_ECS_RUND.getRuntimeType().equals(metaParam.getPodType().getRuntimeType())) {
                        rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, metaParam.getAvzInfo());
                    }
                    replicaResources.add(replicaResourceRequest);
                }
            });
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(metaParam.getPodType().getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }

            if (StringUtils.isNotEmpty(metaParam.getInstructionSetArch())) {
                if (!CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())
                        && !CreateReplicaSetDto.ArchEnum.X86.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())
                        && !X86HG_ARCH.equalsIgnoreCase(metaParam.getInstructionSetArch())) {
                    return ResponseSupport.createErrorResponse(MysqlErrorCode.INVALID_INSTRUCTIONSET_ARCH.toArray());
                }
                if (CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())) {
                    replicaSetResourceRequest.setArch(ReplicaSetResourceRequest.ArchEnum.valueOf(metaParam.getInstructionSetArch()));
                }
            }
            replicaSetResourceRequest.setReplicaResourceRequestList(replicaResources);

            podReplicaSetResourceHelper.mockReplicaSetResource(replicaSetResourceRequest);

            if (StringUtils.isNotEmpty(metaParam.getPerformanceCode())
                    && "mgr".equalsIgnoreCase(metaParam.getPerformanceCode())) {
                replicaSetService.isSupportMgr(replicaResources.size(), metaParam.getInstanceLevel(), serviceSpecTag);
            }

            //cold data enabled and type, support recyle bin
            String sourceBakId = metaParam.getColdSourceBakId();
            if(StringUtils.isNotBlank(sourceBakId)){
                ColdDataDisk coldDataDisk = coldDataService.getColdDataDiskFromSnapshotOrBackupSetId(requestId,
                        metaParam.isColdDataEnabled(),
                        replicaSetResourceRequest.getRegionId(),
                        true,
                        Objects.nonNull(metaParam.getSourceCustInstanceDO())?metaParam.getSourceCustInstanceDO().getInsName():metaParam.getColdDataSourceInstanceName(),
                        sourceBakId,
                        metaParam.getBid(),
                        metaParam.getUid(), true,
                        metaParam.getColdDataSnapshotId(), metaParam.getColdDataJFSId());
                if(Objects.isNull(replicaSetResourceRequest.getGeneralCloudDisk())){
                    replicaSetResourceRequest.setGeneralCloudDisk(new GeneralCloudDisk());
                }
                replicaSetResourceRequest.getGeneralCloudDisk().setColdDataDisk(coldDataDisk);
            }

            // 申请资源并写入核心元数据
            boolean isAllocate = false;
            String dbInstanceName = metaParam.getDbInstanceName();
            IPWhiteList ipWhiteList = metaParam.getIpWhiteList();
            try {
                commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, dbInstanceName, replicaSetResourceRequest);

                ReplicaSet replicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, null);
                String connectionString = null;
                if (!metaParam.isIgnoreVPC()) {
                    try {
                        EndpointListResult endpointListResult = dBaasMetaService.getDefaultClient().listReplicaSetEndpoints(requestId, replicaSet.getName(), null, 1, 10, null);
                        if (endpointListResult != null && CollectionUtils.isNotEmpty(endpointListResult.getItems())) {
                            connectionString = endpointListResult.getItems().get(0).getAddress();
                        }
                    } catch (Exception e) {
                        //ignore
                        logger.warn("Get replicaset's endpoint failed, {}", e.getMessage());
                    }
                }

                // 集群版实例资源配置
                if (MysqlParamSupport.isCluster(replicaSet.getCategory())) {
                    podReplicaSetResourceHelper.configResource4Cluster(requestId, replicaSet);
                }

                //写入白名单数据
                dBaasMetaService.getDefaultClient().createReplicaSetWhiteIps(requestId, dbInstanceName, ipWhiteList);
                //写入白名单模版数据
                whitelistTemplateService.createWhitelistTemplateRecord(requestId,metaParam.getTemplateList(),metaParam.getTemplateIdList(),metaParam.getUserId(),dbInstanceName);
                // 更新主可用区参数
                custinsParamService.updateAVZInfo(replicaSet.getId().intValue(), metaParam.getAvzInfo());

                // 实例参数入库
                Map<String, String> labels = initReplicaSetLabels(requestId, metaParam, replicaSet);
                dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, dbInstanceName, labels);

                // sync_binlog参数持久化到custins_params
                if (MapUtils.isNotEmpty(metaParam.getParameterPersistCustinsParams())) {
                    mysqlParaHelper.checkAddUserSyncBinlogCustinsParam(metaParam.getParameterPersistCustinsParams(), replicaSet.getId().intValue());
                }

                // 高权限账号参数入库
                setSuperAccount(metaParam, requestId);

                // Dispatch task
                String taskKey = getTaskKey(metaParam);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("requestId", requestId);
                jsonObject.put("replicaSetName", dbInstanceName);
                // 备份信息
                // 备份保留时间, 比如 7 天
                jsonObject.put("bakRetention", metaParam.getBakRetention());
                // 每周哪几天备份, 比如 0101010
                jsonObject.put("preferredBackupPeriod", metaParam.getPreferredBackupPeriod());
                // 备份时间, UTC, 比如 06:35Z
                jsonObject.put("preferredBackupTime", metaParam.getPreferredBackupTime());
                jsonObject.put("instructionSetArch", metaParam.getInstructionSetArch());
                jsonObject.put("netProtocol", metaParam.getNetProtocol().getValue());
                // 标记是否从老架构备份集恢复
                if (StringUtils.isNotEmpty(metaParam.getSnapshotId())) {
                    jsonObject.put("isPengineBackupSet", metaParam.isPEngineBackupSet());
                }
                if (StringUtils.isNotBlank(metaParam.getStorageAutoScale())) {
                    jsonObject.put("storageAutoScale", metaParam.getStorageAutoScale());
                    jsonObject.put("storageUpperBound", metaParam.getStorageUpperBound());
                    jsonObject.put("storageThreshold", metaParam.getStorageThreshold());
                }

                if (metaParam.isDisasterRestore()) {
                    jsonObject.put("sourceReplicaSetName", metaParam.getSourceDBInstanceName());
                    jsonObject.put("sourceRegionId", metaParam.getSourceRegionId());
                    jsonObject.put("restoreType", metaParam.getRestoreType());
                    jsonObject.put("backupSetId", metaParam.getBackupSetId());
                    jsonObject.put("backupSetRegionId", metaParam.getBackupSetRegionId());
                    jsonObject.put("restoreTime", metaParam.getRestoreTime());
                    jsonObject.put("consistentTime", metaParam.getConsistentTime());
                }
                if (isRebuildFromPengine) {
                    jsonObject.put("isPengineBackupSet", isRebuildFromPengine);
                    jsonObject.put("backupKindCode", srcCustins.getKindCode());
                }
                // 是否开启TDE
                if (metaParam.isTdeEnabled()) {
                    jsonObject.put("tdeEnabled", true);
                    if (!StringUtils.isBlank(metaParam.getTdeEncryptionKeyId())) {
                        jsonObject.put("tdeEncryptionKeyId", metaParam.getTdeEncryptionKeyId());
                    }
                }
                // CLS
                if (StringUtils.equalsIgnoreCase(metaParam.getClsKeyMode(), PodDefaultConstants.CLS_MODE_KMS_KEY)) {
                    jsonObject.put("clsKeyMode", PodDefaultConstants.CLS_MODE_KMS_KEY);
                    if (!StringUtils.isBlank(metaParam.getClsEncryptionKeyId())) {
                        jsonObject.put("clsEncryptionKeyId", metaParam.getClsEncryptionKeyId());
                    }
                }

                String parameter = jsonObject.toJSONString();
                Object taskId = workFlowService.dispatchTask(replicaSet, taskKey, parameter, WorkFlowService.TASK_PRIORITY_COMMON);
                // 下发创建实例的后置任务
                if (metaParam.getAutoCreateProxy()) {
                    workFlowService.dispatchTask("custins", dbInstanceName, PodDefaultConstants.DOMAIN_MYSQL, PodDefaultConstants.TASK_MODIFY_INS_AFTER_CREATE, "", WorkFlowService.TASK_PRIORITY_COMMON);
                } else if (metaParam.isExternalReplication()) {
                    workFlowService.dispatchTask(replicaSet, PodDefaultConstants.TASK_ACTIVATE_EXTERNAL_REPLICATION, "", WorkFlowService.TASK_PRIORITY_COMMON);
                }

                isAllocate = true;
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", taskId);
                data.put(ParamConstants.DB_INSTANCE_NAME, dbInstanceName);
                data.put(ParamConstants.DB_INSTANCE_ID, replicaSet.getId());
                data.put("ConnectionString", connectionString);
                data.put("Port", metaParam.getPortStr());
                data.put("DBInstanceConnType", replicaSet.getConnType());
                return data;
            } catch (Exception e) {
                logger.error(requestId + " Exception:" + e.getMessage(), e);
                isAllocate = false;
                if (e instanceof ApiException) {
                    logger.error("allocateReplicaSetResourceV1 failed requestId={} bizType={} region={} uid={} replicaSetName={}",
                            requestId, replicaSetResourceRequest.getBizType(), replicaSetResourceRequest.getRegionId(),
                            replicaSetResourceRequest.getUid(), replicaSetResourceRequest.getReplicaSetName());
                    return CommonProviderExceptionUtils.resourceWrapper(requestId, (ApiException)e);
                }
                return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
            } finally {
                if (!isAllocate) {
                    //分配失败或者其它异常的情况下，要调用释放资源接口
                    try {
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                    } catch (ApiException e) {
                        //ignore
                        logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                    }
                }
            }
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    private Map<String, String> initReplicaSetLabels(String requestId, PodCreateInsParam metaParam, ReplicaSet replicaSet) throws Exception {

        Map<String, String> labels = new HashMap<>();
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID, metaParam.getParamGroupId());
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_INFO, JSON.toJSONString(SysParamGroupHelper.describeSysParamGroupId(metaParam.getParamGroupId())));
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_MYSQL_CUSTOM_PARAMS, JSON.toJSONString(metaParam.getCustomMysqlParams()));
        labels.put(CustinsParamSupport.CUSTINS_PARAM_DB_INSTANCE_RESOURCE_GROUP_ID, metaParam.getResourceGroupId());
        labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_FREQUENCY, CustinsParamSupport.CUSTINS_PARAM_VALUE_PERF_FREQUENCY_VIP_INS_DEFAULT);

        //标记开启 IO 加速
        if (metaParam.isIoAccelerationEnabled()) {
            labels.put(IO_ACCELERATION_ENABLED, IO_ACCELERATION_ENABLED_ON);
        }

        // mark compression
        if (CloudDiskCompressionHelper.isCompressionModeOn(metaParam.getCompressionMode())) {
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_MODE, metaParam.getCompressionMode());
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_COMPRESSION_RATIO, String.valueOf(metaParam.getCompressionRatio()));
            labels.put(CloudDiskCompressionHelper.CUSTINS_PARAM_DISK_SIZE_BEFORE_COMPRESSION, String.valueOf(CloudDiskCompressionHelper.convertDiskSizeGBToMB(metaParam.getDiskSizeGBBeforeCompression())));
        }

        //标记pfs盘
        if (metaParam.isUsePfs()) {
            labels.put(PodDefaultConstants.REPLICA_SET_LABEL_CLOUD_PFS, "true");
        }

        //todo: 写优化发布先行版 用于对新增arm打标 根据MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT进行控制
        boolean isArm = CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch());
        if (isArm) {
            labels = podCommonSupport.putLabelForOptimizedWrites(requestId, metaParam.getDbType(), labels);
        }

        //  flag to enable optimized write
        Map<String, Boolean> optimizedWritesInfo = new HashMap<>();
        optimizedWritesInfo.put(CustinsParamSupport.INIT_OPTIMIZED_WRITES, metaParam.isInitOptimizedWrites());
        optimizedWritesInfo.put(CustinsParamSupport.OPTIMIZED_WRITES, metaParam.isInitOptimizedWrites());
        labels.put(CustinsParamSupport.OPTIMIZED_WRITES_INFO, JSON.toJSONString(optimizedWritesInfo));

        //指定实例资源调度模板的写入实例和模版名关联
        if (Strings.isNotEmpty(metaParam.getRsTemplateName())) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME, metaParam.getRsTemplateName());
        }

        if ("XDB".equals(metaParam.getDbEngine())) {
            //xdb 一定是强同步
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SYNC_MODE, CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_SYNC);
            labels.put("dbEngine", metaParam.getDbEngine());
        }

//        //增加是否单租户的标
//        labels.put(PodDefaultConstants.REPLICA_SET_SINGLE_TENANT, String.valueOf(metaParam.isSingleTenant()));

        String minorVersion = minorVersionServiceHelper.resetReplicaSetMinorVersion(requestId, replicaSet.getName());
        if (StringUtils.isNotBlank(minorVersion)) {
            labels.put("minor_version", minorVersion);
        }

        if (StringUtils.isNotEmpty(metaParam.getAutoUpgradeMinorVersion())) {
            labels.put(ParamConstants.AUTO_UPGRADE_MINOR_VERSION, metaParam.getAutoUpgradeMinorVersion());
        }

        // 云上业务需要开启审计日志
        if (PodParameterHelper.isAliYun(replicaSet.getBizType())) {
            labels.put(CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        }

        // vbm产品形态，标记custins_resoure_type=vbm
        if(metaParam.isVbm()){
            labels.put("custins_resource_type", "vbm");
        }

        if (CONN_TYPE_TDDL.equals(metaParam.getConnType())) {
            labels.put(TDDL_TASK_MIGRATE, "true");
            if (metaParam.isForMigrate()) {
                labels.put("without_tddl", "true");
            }
            labels.put("TddlBiztype", "non_unit");
            labels.put("TddlRegionConfig", metaParam.getTddlRegionConfig());
            labels.put("appDbList", JSON.toJSONString(metaParam.getDbName().split("\\|")));
            labels.put("bizType", metaParam.getBizType().toString());
            labels.put("charset", metaParam.getCharSet());
            labels.put("clusterName", metaParam.getTddlClusterName());
            labels.put("dbEngine", metaParam.getDbEngine());
            labels.put("dbType", metaParam.getDbType());
            labels.put("dbVersion", metaParam.getDbVersion());
            labels.put("requestId", requestId);
            labels.put("timeZone", metaParam.getTimeZone());
            labels.put("uid", metaParam.getUid());
            labels.put("userId", metaParam.getBid());
            labels.put("instructionSetArch", metaParam.getInstructionSetArch());
            labels.put("netProtocol", metaParam.getNetProtocol().getValue());
        }
        return labels;
    }

    private void setReplicaResourceForDataNode(PodCreateInsParam metaParam, ReplicaResourceRequest replicaResourceRequest) throws Exception {
        replicaResourceRequest.setClassCode(metaParam.getClassCode());
        if (ReplicaSetService.isStorageTypeCloudDisk(metaParam.getDiskType())) {
            //云上业务云盘需要有赠送，集团业务不需要
            int diskSizeGB = podParameterHelper.getExtendDiskSizeGBForPod(metaParam.getBizType(), metaParam.isSingleNode(), metaParam.getDiskSize());

            //cloud_pfs需要有分盘逻辑
            if (metaParam.isUsePfs()) {
                replicaResourceRequest.setVolumeSpecs(replicaSetService.getCloudDiskReplicaVolumeSpecList(diskSizeGB, null));
            }
            replicaResourceRequest.setDiskSize(diskSizeGB);
            if (metaParam.getSnapshotId() != null) {
                // 基于快照来数据恢复
                VolumeSpec volumeSpec = new VolumeSpec();
                volumeSpec.setSnapshotId(metaParam.getSnapshotId());
                volumeSpec.setName("data");
                replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                replicaResourceRequest.setDiskSize(metaParam.getDiskSize()); //通过快照恢复恢复的实例，需要使用在线扩容，所以放在任务流中
            }

            //设置云盘性能等级
            String performanceLevel = metaParam.getPerformanceLevel();
            if (StringUtils.isNotEmpty(performanceLevel)) {
                if (CollectionUtils.isNotEmpty(replicaResourceRequest.getVolumeSpecs())) {
                    replicaResourceRequest.getVolumeSpecs().stream().filter(v -> StringUtils.equalsIgnoreCase(v.getName(), "data")).forEach(v -> v.setPerformanceLevel(performanceLevel));
                } else {
                    VolumeSpec volumeSpec = new VolumeSpec();
                    volumeSpec.setName("data");
                    volumeSpec.setPerformanceLevel(performanceLevel);
                    replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
                }
            }
        } else {
            replicaResourceRequest.setDiskSize(metaParam.getDiskSize());
        }
    }
    String getZoneKeyStr(String zoneId, String bizType) {
        return String.format("%s,%s", zoneId, bizType);
    }

    public void setSuperAccount(PodCreateInsParam metaParam, String requestId) throws Exception {
        //判断高权限账号是不是RDS在使用的root账号，如果是，不落库
        // 高权限账号参数入库
        if (!StringUtils.isAnyBlank(metaParam.getSuperAccountName(), metaParam.getSuperAccountPassword()) &&
                !mysqlParaHelper.checkAccountIsReserved(metaParam.getDbVersion(), metaParam.getSuperAccountName())) {
            Account accountMeta = new Account();
            accountMeta.setName(metaParam.getSuperAccountName());
            accountMeta.setPassword(HashUtils.getMysqlHashString(metaParam.getSuperAccountPassword()));
            accountMeta.setPriviledgeType(Account.PriviledgeTypeEnum.ALIYUN_SUPER);
            accountMeta.setBizType(DbsSupport.BIZ_TYPE_USER);
            accountMeta.setStatus(Account.StatusEnum.CREATING);
            dBaasMetaService.getDefaultClient().createAccountForReplicaSet(requestId, metaParam.getDbInstanceName(), accountMeta);
        }
    }

    private void setXDBReplicaResourceForLogger(String requestId, PodCreateInsParam metaParam, ReplicaResourceRequest replicaResourceRequest) throws Exception {
        String dbType = metaParam.getDbType();
        String dbVersion = metaParam.getDbVersion();
        String classCode = metaParam.getClassCode();
        InstanceLevelListResult instanceLevels = dBaasMetaService
                .getDefaultClient().listInstanceLevelChildren(requestId, dbType, dbVersion, classCode);
        InstanceLevel loggerLevel = instanceLevels.getItems().stream().filter(x -> x.getClassCode().contains("logger")).collect(Collectors.toList()).get(0);
        if (PodParameterHelper.isAliGroup(metaParam.getBizType())) {
            String loggerStorageType = "";
            if(CreateReplicaSetDto.ArchEnum.ARM.toString().equalsIgnoreCase(metaParam.getInstructionSetArch())){
                loggerStorageType = "cloud_essd";
            }else{
                loggerStorageType = cacheService.getValueOrDefault("ALIGROUP_XDB_LOGGER_STORAGE_TYPE", "local_ssd");
            }
            replicaResourceRequest.setStorageType(loggerStorageType);
            String loggerClassCode = aligroupService.getAligroupLoggerClassCode(loggerStorageType);
            replicaResourceRequest.setClassCode(loggerClassCode);
            InstanceLevel aligroupLoggerLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, loggerClassCode, true);
            replicaResourceRequest.setDiskSize(aligroupLoggerLevel.getDiskSizeMB() / 1024);
        } else {
            replicaResourceRequest.setClassCode(loggerLevel.getClassCode());
            replicaResourceRequest.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
        }
    }

    /**
     * 用户备份导入功能
     * 从用户oss下载全量备份并尝试恢复出RDS实例
     * @param params 请求参数
     * @return 响应Map
     */
    public Map<String, Object> createDBInstanceWithOss(Map<String, String> params) {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);

        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        boolean isAllocate = false;
        String dbInstanceName = null;
        try {
            /** get params */
            ReplicaSet.BizTypeEnum bizType = ReplicaSet.BizTypeEnum.ALIYUN; // 默认为云上业务
            String bid = mysqlParamSupport.getAndCheckBID(params);
            String uid = mysqlParamSupport.getUID(params);
            mysqlParamSupport.getAndCreateUserId(params);
            dbInstanceName = mysqlParamSupport.getDBInstanceName(params);
            String dbType = mysqlParamSupport.getAndCheckDBType(params, null);
            String portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);
            String regionID = CustinsParamSupport.getParameterValue(params, "regionid");

            /** reload params by checking oss object */
            clusterBackUpService.reloadOssParams(params);

            /** get dbVersion */
            String dbVersion = mysqlParamSupport.getAndCheckDBVersion(params, dbType, true);

            /** get cluster config */
            Map<String, String> configMap = clusterBackUpService.getBackClusterConfig(regionID, resourceService);
            String zoneId = mysqlParamSupport.getParameterValue(params, ZONE_ID, "");
            String clusterName = null;
            String subDomain = null;
            if(StringUtils.isEmpty(zoneId)) {
                zoneId = configMap.getOrDefault("zoneId", zoneId);
                clusterName = configMap.getOrDefault("clusterName", mysqlParamSupport.getParameterValue(params, CLUSTER_NAME, ""));
            }
            subDomain = configMap.getOrDefault("subDomain", mysqlParamSupport.getParameterValue(params, SUB_DOMAIN, ""));
            if(StringUtils.isEmpty(subDomain)){ // try to get subdomain from rds_region_avzone
                String zoneKey = getZoneKeyStr(zoneId, "aliyun");
                if(zoneMapping.getIfPresent(zoneKey) == null){
                    List<SubDomain> items = dBaasMetaService.getDefaultClient().listSubDomains(requestId, null, null, null, null).getItems();
                    if(items != null) {
                        zoneMapping.putAll(items.stream().collect(
                                Collectors.toMap(
                                        s -> getZoneKeyStr(s.getAvz(), s.getBizType()),
                                        subDomain1 -> StringUtils.defaultIfEmpty(subDomain1.getName(), ""),
                                        (x1,x2) -> x1)
                        ));
                    }
                }
                subDomain = zoneMapping.getIfPresent(zoneKey);
            }
            //如果配置了subDomain则使用配置的subDomain，没配置就使用入参的
            if (StringUtils.isNotEmpty(subDomain)) {
                params.put("region", subDomain);
            }
            Replica.RoleEnum[] nodeRoles = new Replica.RoleEnum[]{Replica.RoleEnum.MASTER};
            AVZInfo avzInfo = avzSupport.getAVZInfo(params);

            String diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, "cloud_essd");
            if (StringUtils.isBlank(diskType)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            if (!PodCommonSupport.isSupportDiskType(diskType)) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            Integer diskSize = Integer.valueOf(mysqlParamSupport.getParameterValue(params, "RestoreSize", clusterBackUpService.getDiskSize(params)));
            params.put("restoresize", diskSize.toString());

            IPWhiteList ipWhiteList = podParameterHelper.getDefaultReplicaSetIpWhiteList("0.0.0.0/0");
            String classCode = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);

            String dbEngine = "MySQL";
            String insTypeDesc = ReplicaSet.InsTypeEnum.MAIN.toString();
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
            if (dBaasMetaService.getDefaultClient().getReplicaSet(requestId, dbInstanceName, true) != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            ReplicaSetResourceRequest replicaSetResourceRequest = clusterBackUpService.allocateClusterResource(bid, uid, dbInstanceName, zoneId, clusterName, dbType, dbVersion,
                    portStr, insTypeDesc, instanceLevel, bizType, classCode, diskType, diskSize, avzInfo, nodeRoles, dbEngine);

            /** 1.资源管理器申请资源
             2.OSS元数据落盘
             3.提交TASK_CHECK_USER_BAKFILE任务流
             4.更新任务流状态/task_id */
            Map<String, Object> data = null;
            isAllocate = clusterBackUpService.allocateDBInstanceResource(requestId, dbInstanceName, ipWhiteList, avzInfo, replicaSetResourceRequest);
            if (isAllocate) {
                logger.info("allocate dbInstance resource succeed requestId={} dbInstanceName={}", requestId, dbInstanceName);
                data = clusterBackUpService.createRestoreTaskWithOss(requestId, dbType, dbVersion, dbInstanceName, params);
            } else {
                logger.info("allocate dbInstance resource failed requestId={} dbInstanceName={}", requestId, dbInstanceName);
                data = new HashMap<>();
                data.put("DBInstanceName", dbInstanceName);
                data.put("Status", false);
            }
            return data;
        } catch (RdsException ex) {
            isAllocate = false;
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ex.getErrorCode());
        } catch (Exception ex) {
            isAllocate = false;
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isAllocate) {
                logger.error("createDBInstanceWithOss failed");
                //分配失败或者其它异常的情况下，要调用释放资源接口
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, dbInstanceName);
                } catch (ApiException e) {
                    //ignore
                    logger.error(requestId + " release ReplicaSet Exception: {}", e.getResponseBody());
                }
            }
        }
    }

    private String getTaskKey(PodCreateInsParam metaParam) {
        String taskKey = null;
        if (metaParam.isDisasterRestore()) {
            if (metaParam.getInstanceLevel().getCategory() == InstanceLevel.CategoryEnum.CLUSTER) {
                taskKey = PodDefaultConstants.TASK_DISASTER_RESTORE_CLUSTER_INS;
            } else {
                taskKey = PodDefaultConstants.TASK_DISASTER_RESTORE_INS;
            }
        } else if (metaParam.getSnapshotId() == null) {
            taskKey = "XDB".equalsIgnoreCase(metaParam.getDbEngine()) ?
                    ("tddl".equalsIgnoreCase(metaParam.getConnType()) ? PodDefaultConstants.TASK_CREATE_XDB_TDDL_INS : PodDefaultConstants.TASK_CREATE_XDB_INS)
                    :
                        metaParam.getInstanceLevel().getCategory() == InstanceLevel.CategoryEnum.CLUSTER ?
                            PodDefaultConstants.TASK_CREATE_CLUSTER_INS : PodDefaultConstants.TASK_CREATE_INS;
        } else {
            if ("XDB".equalsIgnoreCase(metaParam.getDbEngine())) {
                taskKey = PodDefaultConstants.TASK_CLONE_XDB_INS;
            } else if (MysqlParamSupport.isCluster(metaParam.getInstanceLevel().getCategory().toString())) {
                taskKey = PodDefaultConstants.TASK_CLONE_CLUSTER_INS;
            } else {
                taskKey = PodDefaultConstants.TASK_CLONE_INS;
            }
        }
        return taskKey;
    }

}
