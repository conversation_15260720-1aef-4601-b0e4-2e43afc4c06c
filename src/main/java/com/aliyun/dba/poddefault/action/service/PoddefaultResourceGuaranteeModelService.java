package com.aliyun.dba.poddefault.action.service;

import com.alicloud.apsaradb.inventory.model.SpecModificationRequest;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.common.consts.ResourceScheduleConsts;
import com.aliyun.dba.base.service.ResourceScheduleService;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.common.consts.ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_HAIGUANG;

@Service
public class PoddefaultResourceGuaranteeModelService {

    protected static final LogAgent logger = LogFactory.getLogAgent(PoddefaultResourceGuaranteeModelService.class);
    @Resource
    private ResourceScheduleService resourceScheduleService;

    public void addResourceGuaranteeModelForLocalUpgrade(SpecModificationRequest request, InstanceLevel instanceLevel) {
        try {
            Map<String, String> allResourceGuaranteeLevelMap = getAllResourceGuaranteeLevelMap(request.getUid(), instanceLevel);
            if (MapUtils.isEmpty(allResourceGuaranteeLevelMap)) {
                logger.info("Uid = {}, allResourceGuaranteeLevelMap is null. Skip addResourceGuaranteeModelForLocalUpgrade.", request.getUid());
                return;
            }
            request.setResourceGuaranteeLevel(allResourceGuaranteeLevelMap.get(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevel.name()));
            request.setResourceGuaranteeLevelType(allResourceGuaranteeLevelMap.get(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevelType.name()));
            if (allResourceGuaranteeLevelMap.containsKey(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeBackUpLevels.name())) {
                List<String> resGuaranteeBackUpLevelsList = Arrays.stream(allResourceGuaranteeLevelMap.get(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeBackUpLevels.name())
                        .split(",")).collect(Collectors.toList());
                request.setResourceGuaranteeBackUpLevels(resGuaranteeBackUpLevelsList);

            }
            //使用混开策略需补充的参数
            request.setUserId(request.getUid());
            request.setUseResourceGuarantee(true);

            logger.info("Uid ={}, specModificationRequest= {}.", request.getUid(), request);

        } catch (Exception e) {
            logger.warn("Uid = {}, addResourceGuaranteeModelForLocalUpgrade fail, error is {}", request.getUid(), e.getMessage());
        }
    }


    public Map<String, String> getAllResourceGuaranteeLevelMap(String uid, InstanceLevel instanceLevel) {
        Map<String, String> resourceGuaranteeLevelMapByIntanceLevel = getResourceGuaranteeLevelMapByIntanceLevel(instanceLevel);
        if (resourceGuaranteeLevelMapByIntanceLevel != null) {
            return resourceGuaranteeLevelMapByIntanceLevel;
        }
        return getAllResourceGuaranteeLevelMapForUid(uid);
    }

    /**
     * 根据uid获取当前用户的资源保障策略
     * 返回resource_guarantee_level和resource_guarantee_level_backup的拼接结果
     *
     * @param uid
     * @return
     */
    private Map<String, String> getAllResourceGuaranteeLevelMapForUid(String uid) {
        try {
            if (StringUtils.isEmpty(uid)) {
                logger.info("Uid is null. Skip getAllResourceGuaranteeLevelMapForUid.");
                return null;
            }

            String mapKey = resourceScheduleService.getResGuaranteeModelMapKey();
            //从resource表中获取当前uid对应的资源保障策略
            Map<String, String> resultMap = resourceScheduleService.getResourceGuaranteeModelPolicyMap(null, uid, mapKey);
            if (CollectionUtils.isEmpty(resultMap)) {
                logger.info("Uid = {}, resGuaranteeLevelMap is null. Skip.", uid);
                return null;
            }
            if (StringUtils.isEmpty(resultMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL))) {
                logger.info("Uid = {}, resGuaranteeLevel is null. Skip.", uid);
                return null;
            }


            // resultMap从resource表中获取, ecs与物理机通用, 示例
//        {
//            "resource_guarantee_level":"performance,super-user",
//            "resource_guarantee_level_backup":"regular,low-cost"
//        }

            //资源调度处理ecs实例时 入参与物理机不同, resGuaranteeLevel中只允许传输一种资源保障级别
            //因此获取resource_guarantee_level中的第一个标签作为默认保障级别，其他的均作为back_up
            String[] resGuaranteeLevels = resultMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL).split(",", 2);

            String defaultResGuaranteeLevel = resGuaranteeLevels[0];
            String resGuaranteeBackUpLevels = resGuaranteeLevels.length > 1 ? resGuaranteeLevels[1] : "";

            if (!StringUtils.isEmpty(resultMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP))) {
                resGuaranteeBackUpLevels += "," + resultMap.get(ResourceScheduleConsts.RESOURCE_GUARANTEE_LEVEL_BACKUP);
            }
            // 去除字符串多余的","
            if (!StringUtils.isEmpty(resGuaranteeBackUpLevels)) {
                resGuaranteeBackUpLevels = resGuaranteeBackUpLevels.trim().replaceAll("^,+", "");
            }


            Map<String, String> allResourceGuaranteeLevelMap = new HashMap<>();

            allResourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevel.name(), defaultResGuaranteeLevel);

            if (StringUtils.isEmpty(resGuaranteeBackUpLevels)) {
                allResourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevelType.name(),
                        ResourceScheduleConsts.ResourceGuaranteeLevelTypeEnum.force.name());
            } else {
                allResourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevelType.name(),
                        ResourceScheduleConsts.ResourceGuaranteeLevelTypeEnum.prefer.name());

                allResourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeBackUpLevels.name(), resGuaranteeBackUpLevels);
            }

            logger.info("getAllResourceGuaranteeLevelMapForUid: uid ={}, result = {}.", uid, allResourceGuaranteeLevelMap);
            return allResourceGuaranteeLevelMap;

        } catch (Exception e) {
            logger.warn("Uid = {}, getAllResourceGuaranteeLevelMapForUid fail, error is {}", uid, e.getMessage());
            return null;
        }
    }

    /**
     * 海光规格使用混开配置
     * 返回resource_guarantee_level和resource_guarantee_level_backup的拼接结果
     *
     * @param instanceLevel
     * @return
     */
    private Map<String, String> getResourceGuaranteeLevelMapByIntanceLevel(InstanceLevel instanceLevel) {
        if (instanceLevel == null || !PodCommonSupport.isX86HG(instanceLevel)) {
            return null;
        }
        Map<String, String> resourceGuaranteeLevelMap = new HashMap<>();
        resourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevel.name(), RESOURCE_GUARANTEE_LEVEL_HAIGUANG);
        resourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeLevelType.name(),
                ResourceScheduleConsts.ResourceGuaranteeLevelTypeEnum.force.name());
        resourceGuaranteeLevelMap.put(ResourceScheduleConsts.ScheduleResourceGuaranteePoliciesKey.resourceGuaranteeBackUpLevels.name(), "");
        return resourceGuaranteeLevelMap;
    }
}
