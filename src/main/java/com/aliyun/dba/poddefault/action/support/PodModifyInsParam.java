package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSON;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaResource;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.NetProtocolEnum;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.modify.BaseModifyDBInstanceService;
import com.aliyun.dba.poddefault.action.support.flag.MyFlag;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.base.support.MysqlErrorCode.INVALID_CLOUD_DISK_SIZE;
import static com.aliyun.dba.custins.support.CustinsParamSupport.ESSD0_PERFORMANCELEVEL;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.ECS_ClOUD_ESSD;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.*;
import static com.aliyun.dba.poddefault.action.support.CloudDiskCompressionHelper.CUSTINS_PARAM_TMP_DISK_SIZE_BEFORE_COMPRESSION;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.ParamConstants.*;

@Data
@Slf4j
public class PodModifyInsParam {
    public static final Long F_DHG = 1L;
    public static final Long F_TDDL = 1L << 1;
    public static final Long F_READ_INS = 1L << 2;
    public static final Long F_EMERGENCY_TRANS = 1L << 3;
    public static final Long F_MOD_AVZ = 1L << 4;
    public static final Long F_DISK_TYPE_CHANGED = 1L << 5;
    public static final Long F_DISK_SIZE_CHANGED = 1L << 6;
    public static final Long F_TRANS_INS = 1L << 7;
    public static final Long F_PFS = 1L << 8;
    public static final Long F_BASIC = 1L << 9;
    public static final Long F_STANDARD = 1L << 10;
    public static final Long F_ENTERPRISE = 1L << 11;
    public static final Long F_XDB = 1L << 12;              // 是否是XDB
    public static final Long F_TRANS_NC = 1L << 13;         // 是否使用NC
    public static final Long F_LOCAL_SSD = 1L << 14;
    public static final Long F_SINGLE_NODE = 1L << 15;
    public static final Long F_DOUBLE_NODE = 1L << 16;      //双节点实例
    public static final Long F_ELASTIC_MOD_POD_MODE = 1L << 17; // 标记是否为弹性升降配
    public static final Long F_TARGET_SERVERLESS = 1L << 18; // 标记目标规格是否为Serverless
    public static final Long F_SINGLE_TENANT_LOCAL_MODIFY = 1L << 19; // 标记单租户本地变配
    public static final Long F_ELASTIC_MOD_POD_LOCAL = 1L << 20; // 弹性升降配是否为全本地
    public static final Long F_NET_PROTOCOL_CHANGED = 1L << 21; // 是否为网络协议栈变更
    public static final Long F_CLUSTER = 1L << 22;
    public static final Long F_RUND = 1L << 23;  //是否是rund实例

    private MyFlag flags = new MyFlag();

    private AliyunInstanceDependency dependency;
    private Map<String, String> params;
    private Map<String, Boolean> initParams = new HashMap<>();

    private CustInstanceDO custins;
    private ReplicaSet replicaSetMeta;
    private String tmpReplicaSetName;
    private String requestId;

    private User user;
    private String bid;
    private String uid;
    private String dbInstanceName;
    private String dbType;
    private String dbVersion;
    private String srcMinorVersion;
    private String clusterName;
    private String orderId;
    private String accessId;
    private boolean isDHG = false;
    private boolean isTDDL = false;
    private boolean isReadIns = false;
    private boolean isEmergencyTransfer = false;
    private boolean isModifyAvz = false;
    private boolean isNodeModify = false;
    private boolean isDiskSizeChange = false;
    private boolean isDiskTypeChange = false;
    private boolean srcIoAccelerationEnabled;
    private boolean isIoAccelerationEnabledChange = false;
    private boolean isTransIns = false;
    private boolean isPfs = false;
    private boolean isNetProtocolChange = false;
    private boolean isShrinkIns = false;
    private GeneralCloudDisk generalCloudDisk;
    private boolean isPerfLevelChangedToPL0ForBasic = false;
    private boolean isProvisionedIopsChange = false;
    private boolean isBurstingEnabledChange = false;
    private boolean isRund = false;
    private boolean isCompressionModeChange = false;

    private ModifyReplicaSetResourceRequest.ModifyModeEnum modifyMode;
    private Integer diskSizeGB;
    private Integer targetDiskSizeGB;
    private String classCode;
    private String targetClassCode;
    private InstanceLevel srcInstanceLevel;
    private InstanceLevel targetInstanceLevel;
    private String srcComposeTag;
    private String targetComposeTag;
    private String srcDiskType;
    private String targetDiskType;
    private String srcPerformanceLevel;
    private String targetPerformanceLevel;
    private String regionId;
    private AVZInfo oldAvzInfo;
    private AVZInfo avzInfo;
    private Date switchTime;
    private Map<String, Object> switchInfo;
    private Map<Replica.RoleEnum, String> roleHostNameMapping;
    private ScheduleTemplate scheduleTemplate;
    private PodScheduleTemplate podScheduleTemplate;
    private boolean isSrcSingleTenant;
    private boolean isTargetSingleTenant;
    private NetProtocolEnum srcNetProtocol;
    private NetProtocolEnum targetNetProtocol;
    private Double srcRcu;
    private Double rcu;

    //autopl 配置
    private Long provisionedIops;
    private boolean burstingEnabled;

    //cold data 冷存配置
    private boolean coldDataEnabled;
    private boolean srcColdDataEnabled;

    //serverless
    private ServerlessSpec srcServerlessSpec;
    private ServerlessSpec targetServerlessSpec;

    //default runtime type
    private PodType podType = PodType.POD_RUNC;

    //vbm
    private boolean isVbm = false;

    //optimized writes
    private String primaryOptimizedWritesString;
    private String primaryInitOptimizedWritesString;
    private String targetOptimizedWritesString;
    private String targetInitOptimizedWritesString;

    // compression config
    private String srcCompressionMode;
    private String targetCompressionMode;
    private Double compressionRatio = 1.0;
    private Integer diskSizeGBBeforeCompression;

    public PodModifyInsParam(AliyunInstanceDependency dependency, Map<String, String> params) {
        this.dependency = dependency;
        this.params = params;
        requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID, "");
        this.setParamInitDone("requestId");
    }

    public PodModifyInsParam initCustins() throws Exception {
        custins = dependency.getMysqlParamSupport().getAndCheckCustInstance(params);
        setParamInitDone("custins");
        return this;
    }

    public PodModifyInsParam initReplicaSetMeta() throws Exception {
        replicaSetMeta = dependency.getReplicaSetService().getAndCheckUserReplicaSet(params);
        ReplicaSet primaryReplicaSet = dependency.getPodCommonSupport().getPrimaryReplicaSet(requestId, replicaSetMeta).getRight();
        String category = primaryReplicaSet.getCategory();
        //由于基础版只读实例category会与主实例有差异，所以只读实例的category以自身为准
        if (PodParameterHelper.isAliYun(replicaSetMeta.getBizType()) && (ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType()))) {
            category = replicaSetMeta.getCategory();
        }
        boolean isDoubleReadIns = dependency.getReplicaSetService().isAligroupDoubleNodeRead(requestId, replicaSetMeta.getName());
        flags.setFlags(F_BASIC, BASIC_LEVEL.equalsIgnoreCase(category));
        flags.setFlags(F_STANDARD, STANDARD_LEVEL.equalsIgnoreCase(category));
        flags.setFlags(F_CLUSTER, CLUSTER_LEVEL.equalsIgnoreCase(category));
        flags.setFlags(F_ENTERPRISE, ENTERPRISE_LEVEL.equalsIgnoreCase(category));
        flags.setFlags(F_DOUBLE_NODE, isDoubleReadIns);
        log.info("requestId:{} flags:{} category:{}", requestId, flags, category);
        setParamInitDone("replicaSetMeta");
        multiWriteEngineValidate();
        return this;
    }

    public PodModifyInsParam initDBType() throws Exception {
        checkDepend("replicaSetMeta");
        dbType = replicaSetMeta.getService();
        setParamInitDone("dbType");
        return this;
    }

    public PodModifyInsParam initDBVersion() throws Exception {
        checkDepend("replicaSetMeta");
        dbVersion = replicaSetMeta.getServiceVersion();
        setParamInitDone("dbVersion");
        return this;
    }

    public PodModifyInsParam initClusterName() throws Exception {
        checkDepend("replicaSetMeta");
        clusterName = replicaSetMeta.getResourceGroupName();
        setParamInitDone("clusterName");
        return this;
    }

    public PodModifyInsParam setClusterNameToParamsForDHG() throws Exception {
        checkDepend("clusterName").checkDepend("isDHG");
        if (isDHG) {
            params.put("ClusterName".toLowerCase(), clusterName);
        }
        setParamInitDone("setClusterNameToParamsForDHG");
        return this;
    }

    public PodModifyInsParam setIsDHG() throws Exception {
        checkDepend("clusterName");
        isDHG = dependency.getMysqlParamSupport().isDHGCluster(clusterName);
        flags.setFlags(F_DHG, isDHG);
        setParamInitDone("isDHG");
        return this;
    }

    public PodModifyInsParam setIsTDDL() throws Exception {
        checkDepend("replicaSetMeta");
        isTDDL = CONN_TYPE_TDDL.equalsIgnoreCase(Objects.requireNonNull(replicaSetMeta.getConnType()).toString());
        flags.setFlags(F_TDDL, isTDDL);
        setParamInitDone("isTDDL");
        return this;
    }

    public PodModifyInsParam setIsReadIns() throws Exception {
        checkDepend("replicaSetMeta");
        isReadIns = ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType()) ||
                ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType());
        flags.setFlags(F_READ_INS, isReadIns);
        setParamInitDone("isReadIns");
        return this;
    }

    public PodModifyInsParam setSingleTenantLocalModify(boolean isLocalModify) throws Exception {
        flags.setFlags(F_SINGLE_TENANT_LOCAL_MODIFY, isLocalModify);
        return this;
    }

    public PodModifyInsParam initModifyMode() throws Exception {
        String modifyModeStr = getDefaultModifyMode();
        log.info("{} get defaultModifyMode {}", requestId, modifyModeStr);
        modifyMode = ModifyReplicaSetResourceRequest.ModifyModeEnum.fromValue(modifyModeStr);
        if (LOCAL_MODIFY_POD_MODES.contains(modifyMode) && dependency.getPodCommonSupport().isXEngine(requestId, replicaSetMeta.getName())) {
            log.error("{} xengine not support local modify.", requestId);
            throw new RdsException(ErrorCode.INVALID_MODIFY_MODE);
        }
        setParamInitDone("modifyMode");
        return this;
    }

    public PodModifyInsParam initServerlessModifyMode() {
        modifyMode = ModifyReplicaSetResourceRequest.ModifyModeEnum.FORCEINPLACEUPDATEREPLICA;
        log.info("{} get defaultModifyMode {}", requestId, modifyMode.getValue());
        setParamInitDone("modifyMode");
        return this;
    }

    public PodModifyInsParam initOrderId() {
        orderId = getParameterValue(params, "OrderId");
        setParamInitDone("orderId");
        return this;
    }

    public PodModifyInsParam initAccessId() {
        accessId = getParameterValue(params, "AccessId");
        setParamInitDone("accessId");
        return this;
    }

    public PodModifyInsParam initTmpReplicaSetName() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("orderId").checkDepend("dbInstanceName");
        int MAX_TMP_INSTANCE_NAME_LENGTH = 38;
        String temporderId = PodParameterHelper.isAliGroup(replicaSetMeta.getBizType()) ? null : orderId;
        String tmpReplicaSetNamePrefix = BaseModifyDBInstanceService.getTempReplicaSetNamePrefix(requestId, dbInstanceName, temporderId, MAX_TMP_INSTANCE_NAME_LENGTH);
        tmpReplicaSetName = String.format("%s-%s", tmpReplicaSetNamePrefix.toLowerCase(), dbInstanceName);
        if (StringUtils.startsWith(replicaSetMeta.getName(), "rm-zhbdaily")) {
            tmpReplicaSetName = tmpReplicaSetName.replace('_', '-');
        }
        setParamInitDone("tmpReplicaSetName");
        return this;
    }

    public PodModifyInsParam initDiskSizeGB() throws Exception {
        checkDepend("replicaSetMeta");
        Integer diskSizeMB = replicaSetMeta.getDiskSizeMB();
        if (diskSizeMB == null || diskSizeMB == 0) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }

        diskSizeGB = diskSizeMB / 1024;
        setParamInitDone("diskSizeGB");
        return this;
    }

    public PodModifyInsParam initTargetDiskSizeGB() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("diskSizeGB");
        targetDiskSizeGB = diskSizeGB;
        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.STORAGE)) {
            targetDiskSizeGB = CheckUtils.parseInt(
                    dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.STORAGE),
                    ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
            // if srcCompressionMode is on, param storage should be storage*compression_ratio
            if (CloudDiskCompressionHelper.isCompressionModeOn(srcCompressionMode)) {
                // save diskSizeGB before compression
                diskSizeGBBeforeCompression = targetDiskSizeGB;
                // modify  targetDiskSizeGB
                targetDiskSizeGB = CheckUtils.parseInt(
                        String.valueOf(CloudDiskCompressionHelper.getLogicalSize(targetDiskSizeGB, compressionRatio)),
                        ESSD_MIN_DISK_SIZE, null, ErrorCode.INVALID_STORAGE);
                targetDiskSizeGB = Math.min(targetDiskSizeGB, 64000);
            }
        }
        setParamInitDone("targetDiskSizeGB");
        return this;
    }

    public PodModifyInsParam initClassCode() throws Exception {
        checkDepend("replicaSetMeta");
        classCode = replicaSetMeta.getClassCode();
        setParamInitDone("classCode");
        return this;
    }

    public PodModifyInsParam initTargetClassCode() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("classCode");
        targetClassCode = classCode;
        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
            targetClassCode = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
        }
        setParamInitDone("targetClassCode");
        return this;
    }

    public PodModifyInsParam initRcu() throws Exception {
        checkDepend("targetInstanceLevel").checkDepend("srcInstanceLevel");

        if (PodCommonSupport.isServerless(targetInstanceLevel)) {
            if (dependency.getMysqlParamSupport().hasParameter(params, PodDefaultConstants.RCU)) {
                rcu = Double.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, "Rcu"));
                if (rcu <= 0) {
                    log.error("{} rcu <= 0.", requestId);
                    throw new RdsException(ErrorCode.INVALID_PARAM);
                }
            }
        }
        if (PodCommonSupport.isServerless(srcInstanceLevel)) {
            String sourceRcu = dependency.getDBaasMetaService().getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, PodDefaultConstants.RCU.toLowerCase());
            if (sourceRcu != null) {
                srcRcu = Double.valueOf(sourceRcu);
            }
        }
        setParamInitDone("rcu");
        setParamInitDone("srcRcu");
        return this;
    }

    public PodModifyInsParam initUser() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("requestId");
        user = dependency.getDBaasMetaService().getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
        setParamInitDone("user");

        bid = user.getBid();
        uid = user.getAliUid();
        setParamInitDone("bid");
        setParamInitDone("uid");

        return this;
    }

    public PodModifyInsParam initInstanceName() {
        dbInstanceName = dependency.getMysqlParamSupport().getDBInstanceName(params);
        setParamInitDone("dbInstanceName");
        return this;
    }

    public PodModifyInsParam initSrcInstanceLevel() throws Exception {
        checkDepend("dbType").checkDepend("dbVersion").checkDepend("classCode");
        srcInstanceLevel = dependency.getDBaasMetaService().getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        setParamInitDone("srcInstanceLevel");
        return this;
    }

    public PodModifyInsParam initTargetInstanceLevel() throws Exception {
        checkDepend("dbType").checkDepend("dbVersion").checkDepend("targetClassCode").checkDepend("isReadIns");
        targetInstanceLevel = dependency.getDBaasMetaService().getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, null);

        val isBasicToStandard = InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.equals(targetInstanceLevel.getCategory());

        val isProvisionToServerless = InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.SERVERLESS_BASIC.equals(targetInstanceLevel.getCategory());

        val isServerlessToProvision = InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.BASIC.equals(targetInstanceLevel.getCategory());

        val isProvisionStandardToServerless = InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.SERVERLESS_STANDARD.equals(targetInstanceLevel.getCategory());

        val isServerlessStandardToProvision = InstanceLevel.CategoryEnum.SERVERLESS_STANDARD.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.STANDARD.equals(targetInstanceLevel.getCategory());

        val isServerlessBasicToStandard = InstanceLevel.CategoryEnum.SERVERLESS_BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.SERVERLESS_STANDARD.equals(targetInstanceLevel.getCategory());

        val isStandardToCluster = InstanceLevel.CategoryEnum.STANDARD.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.CLUSTER.equals(targetInstanceLevel.getCategory());

        val isBasicToCluster = InstanceLevel.CategoryEnum.BASIC.toString().equals(replicaSetMeta.getCategory())
                && InstanceLevel.CategoryEnum.CLUSTER.equals(targetInstanceLevel.getCategory());

        val isNotSupported = !isReadIns
                && !isBasicToStandard
                && !isServerlessBasicToStandard
                && !isStandardToCluster
                && !isBasicToCluster
                && !isServerlessToProvision
                && !isServerlessStandardToProvision
                && !isProvisionToServerless
                && !isProvisionStandardToServerless
                && targetInstanceLevel.getCategory() != srcInstanceLevel.getCategory();

        if (isNotSupported) {
            //非只读实例场景，目前不支持原规格和目标规格跨Category，相关任务流还没有
            throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
        }
        setParamInitDone("targetInstanceLevel");
        return this;
    }

    public PodModifyInsParam initSrcComposeTag() throws Exception {
        checkDepend("replicaSetMeta");
        srcComposeTag = dependency.getMinorVersionServiceHelper().getServiceSpecTagByCustinsId(
                requestId, replicaSetMeta.getId().intValue());
        setParamInitDone("srcComposeTag");
        return this;
    }

    public PodModifyInsParam initOldAVZInfo() throws Exception {
        checkDepend("custins");
        oldAvzInfo = dependency.getAvzSupport().getAVZInfoFromCustInstance(custins);
        setParamInitDone("oldAvzInfo");
        return this;
    }

    public PodModifyInsParam initAVZInfo() throws Exception {
        avzInfo = dependency.getAvzSupport().getAVZInfo(params);
        setParamInitDone("avzInfo");
        return this;
    }

    public PodModifyInsParam initRegionId() throws Exception {
        checkDepend("avzInfo");
        regionId = avzInfo.getRegionId();
        setParamInitDone("regionId");
        return this;
    }

    public PodModifyInsParam initTargetDiskType() throws Exception {
        checkDepend("dbInstanceName").checkDepend("targetDiskSizeGB");
        targetDiskType = dependency.getPodParameterHelper().getParameterValue(DB_INSTANCE_STORAGE_TYPE);
        if (StringUtils.isNotEmpty(targetDiskType) && !PodCommonSupport.isSupportDiskType(targetDiskType)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        if (StringUtils.isBlank(targetDiskType)) {
            targetDiskType = srcDiskType;
            targetPerformanceLevel = srcPerformanceLevel;
        } else {
            PodParameterHelper.checkCloudEssdStorageValid(targetDiskType, targetDiskSizeGB);
            targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
        }
        // 去除essdx -> essd0的限制
//        if(!checkIsSupportModifyEssdPerfLevel(srcPerformanceLevel,targetPerformanceLevel)){
//            throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
//        }
        // 即使磁盘等级不变也需要重新检查，因为缩容要满足磁盘购买空间下限
        if (StringUtils.isNotEmpty(targetPerformanceLevel)) {
            PodParameterHelper.checkCloudEssdStorageValidByPLEVEL(targetPerformanceLevel, targetDiskSizeGB);
        }
        setParamInitDone("targetDiskType");
        setParamInitDone("targetPerformanceLevel");
        return this;
    }


    public PodModifyInsParam initPodType() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("targetInstanceLevel");
        ReplicaListResult replicaListResult = dependency.getDBaasMetaService().getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetMeta.getName(), null, null, null, null);
        if (CollectionUtils.isNotEmpty(replicaListResult.getItems())) {
            ReplicaResource replicaResource = dependency.getDBaasMetaService().getDefaultClient().getReplica(requestId, replicaListResult.getItems().get(0).getId(), null);
            this.podType = dependency.getPodCommonSupport().getReplicaRuntimeType(replicaResource);
            if (targetInstanceLevel.getCategory() != InstanceLevel.CategoryEnum.BASIC && targetInstanceLevel.getCategory() != InstanceLevel.CategoryEnum.SERVERLESS_BASIC) {
                this.podType = PodType.POD_RUNC;
            }
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                this.isRund = true;
            }
            flags.setFlags(F_RUND, this.isRund);
        }
        this.setParamInitDone("podType");
        return this;
    }

    public PodModifyInsParam initVbm() throws Exception {
        checkDepend("replicaSetMeta").checkDepend("targetInstanceLevel").checkDepend("podType");
        if (this.podType == PodType.POD_VBM_RUND) {
            this.isVbm = true;
        }
        this.setParamInitDone("isVbm");
        return this;
    }

    public PodModifyInsParam initSrcDiskType() throws Exception {
        checkDepend("dbInstanceName");
        srcDiskType = dependency.getReplicaSetService().getReplicaSetStorageType(dbInstanceName, requestId);
        srcPerformanceLevel = dependency.getReplicaSetService().getVolumePerfLevel(requestId, dbInstanceName, srcDiskType);
        setParamInitDone("srcDiskType");
        setParamInitDone("srcPerformanceLevel");
        return this;
    }

    public PodModifyInsParam initAutoPLConfig() throws Exception {
        /**
         *  get AutoPL config parameters, burstingEnabled and provisionedIops
         */
        checkDepend("dbInstanceName");
        checkDepend("srcDiskType");
        checkDepend("diskSizeGB").checkDepend("uid");
        String paramProvisionedIops = getParameterValue(params, ParamConstants.AUTOPL_PROVISIONED_IOPS);
        String paramBurstingEnabled = getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
        String diskType = Objects.isNull(targetDiskType) ? srcDiskType : targetDiskType;
        Integer diskSize = Objects.isNull(targetDiskSizeGB) ? diskSizeGB : targetDiskSizeGB;
        provisionedIops = dependency.getReplicaSetService().getAutoConfigProvisionedIops(requestId, dbInstanceName, diskType, paramProvisionedIops, diskSize, uid);
        burstingEnabled = dependency.getReplicaSetService().getAutoConfigBurstingEnabled(requestId, dbInstanceName, diskType, paramBurstingEnabled);

        setParamInitDone("burstingEnabled");
        setParamInitDone("provisionedIops");
        /*
         * get AutoPL config change parameters and check change limit, isBurstingEnabledChange and isProvisionedIopsChange
         */
        initAutoPLConfigChange();
        return this;
    }

    private void initAutoPLConfigChange() throws Exception {
        checkDepend("burstingEnabled").checkDepend("provisionedIops");
        checkDepend("dbInstanceName").checkDepend("srcDiskType").checkDepend("diskSizeGB");
        boolean srcBurstingEnabled = dependency.getReplicaSetService().getAutoConfigBurstingEnabled(requestId, dbInstanceName, srcDiskType, null);
        isBurstingEnabledChange = (srcBurstingEnabled != burstingEnabled);
        setParamInitDone("isBurstingEnabledChange");
    }

    public PodModifyInsParam initColdDataConfig() throws Exception {
        checkDepend("dbInstanceName");

        srcColdDataEnabled = dependency.getReplicaSetService().getColdDataEnabled(requestId, dbInstanceName, null);
        setParamInitDone("srcColdDataEnabled");

        String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
        coldDataEnabled = dependency.getReplicaSetService().getColdDataEnabled(requestId, dbInstanceName, paramColdDataEnabled);
        setParamInitDone("coldDataEnabled");
        return this;
    }


    public PodModifyInsParam setDispenseMode() throws Exception {
        checkDepend("oldAvzInfo").checkDepend("replicaSetMeta");

        // 可用区迁移时会传入region，不再设置该参数
        // 组合可用区 -> 主可用区，不应该强制重置未跟原实例一样，根据请求传入参数进行设置
        String migratingAvz = params.get(PodDefaultConstants.MIGRATING_AVZ.toLowerCase());
        if (!Boolean.parseBoolean(migratingAvz)) {
            ParamConstants.DispenseMode dispenseMode = dependency.getCustinsParamService()
                    .getDispenseMode(Objects.requireNonNull(replicaSetMeta.getId()).intValue());
            params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), String.valueOf(dispenseMode.ordinal()));
            params.put(ParamConstants.REGION.toLowerCase(), oldAvzInfo.getRegion());
        }

        setParamInitDone("setDispenseMode");
        return this;
    }

    public PodModifyInsParam setParamsForEmergencyTransfer() throws Exception {
        checkDepend("oldAvzInfo");
        params.put(ParamConstants.REGION.toLowerCase(), oldAvzInfo.getRegion());
        params.put(ParamConstants.REGION_ID.toLowerCase(), oldAvzInfo.getRegionId());
        params.put(ParamConstants.REGION_CATEGORY.toLowerCase(), oldAvzInfo.getRegionCategory());
        params.put(ParamConstants.ZONE_ID.toLowerCase(), oldAvzInfo.getMasterZoneId());
        params.put(ParamConstants.DISPENSE_MODE.toLowerCase(), Integer.valueOf(oldAvzInfo.getDispenseMode().ordinal()).toString());
        params.put(ParamConstants.MULTI_AVZ_EX_PARAM.toLowerCase(), JSON.toJSONString(oldAvzInfo.getMultiAVZExParamDO()));
        setParamInitDone("setParamsForEmergencyTransfer");
        return this;
    }


    public PodModifyInsParam setIsModifyAvz() throws Exception {
        checkDepend("avzInfo").checkDepend("oldAvzInfo");
        if (!avzInfo.isValidForModify()) {
            setParamInitDone("isModifyAvz");
            avzInfo = oldAvzInfo;
            return this;
        }

        isModifyAvz = isModifyAvz(avzInfo, oldAvzInfo);
        flags.setFlags(F_MOD_AVZ, isModifyAvz);
        setParamInitDone("isModifyAvz");
        return this;
    }

    public PodModifyInsParam initRoleHostNameMapping() throws Exception {
        checkDepend("setClusterNameToParamsForDHG");
        roleHostNameMapping = dependency.getPodParameterHelper().getRoleHostNameMapping();
        setParamInitDone("roleHostNameMapping");
        return this;
    }

    public PodModifyInsParam setIsEmergencyTransfer() {
        isEmergencyTransfer = Integer.parseInt(dependency.getPodParameterHelper().getParameterValue("EmergencyTransfer", "0")) != 0;
        flags.setFlags(F_EMERGENCY_TRANS, isEmergencyTransfer);
        log.debug("setIsEmergencyTransfer is {}", isEmergencyTransfer);
        setParamInitDone("isEmergencyTransfer");
        return this;
    }

    public boolean getIsEmergencyTransfer() {
        return this.isEmergencyTransfer;
    }

    public PodModifyInsParam initSwitchInfo() throws Exception {
        switchTime = dependency.getMysqlParamSupport().parseCheckSwitchTimeTimeZoneSafe(params);
        switchInfo = dependency.getPodParameterHelper().getSwitchInfo(params);
        setParamInitDone("switchInfo");
        return this;
    }

    public PodModifyInsParam setIsDiskSizeChange() throws Exception {
        checkDepend("diskSizeGB").checkDepend("targetDiskSizeGB");
        isDiskSizeChange = targetDiskSizeGB != null && !diskSizeGB.equals(targetDiskSizeGB);
        flags.setFlags(F_DISK_SIZE_CHANGED, isDiskSizeChange);
        setParamInitDone("isDiskSizeChange");
        return this;
    }

    public PodModifyInsParam setIsDiskTypeChange() throws Exception {
        checkDepend("srcDiskType").checkDepend("targetDiskType");
        isDiskTypeChange = targetDiskType != null && !srcDiskType.equals(targetDiskType);
        flags.setFlags(F_DISK_TYPE_CHANGED, isDiskTypeChange);
        //只能云盘之间进行变更
        if (isDiskTypeChange && !StringUtils.contains(srcDiskType.toLowerCase(), "cloud")
                && !StringUtils.contains(targetDiskType.toLowerCase(), "cloud")) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
        }
        //cloud_auto暂时不支持类型变更
        if (isDiskTypeChange && StringUtils.equalsIgnoreCase(srcDiskType, ECS_CLOUD_AUTO)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
        }
        // 仅支持essd到auto的变更
        if (isDiskTypeChange && StringUtils.equalsIgnoreCase(targetDiskType, ECS_CLOUD_AUTO)
                && !StringUtils.contains(srcDiskType.toLowerCase(), "cloud_essd")) {
            throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
        }
        setParamInitDone("isDiskTypeChange");
        return this;
    }


    public PodModifyInsParam setIsPfs() throws Exception {
        checkDepend("replicaSetMeta");
        isPfs = dependency.getReplicaSetService().isCloudPfsDisk(requestId, replicaSetMeta.getName());
        flags.setFlags(F_PFS, isPfs);
        setParamInitDone("isPfs");
        return this;
    }

    public PodModifyInsParam initNetProtocol() throws Exception {
        checkDepend("dbInstanceName");
        srcNetProtocol = dependency.getReplicaSetService().getNetProtocol(requestId, dbInstanceName);
        setParamInitDone("srcNetProtocol");
        String targetNetProtocolStr = dependency.getMysqlParamSupport().getParameterValue(params, "TargetNetProtocol", srcNetProtocol);
        try {
            targetNetProtocol = NetProtocolEnum.valueOf(targetNetProtocolStr);
        } catch (IllegalArgumentException e) {
            targetNetProtocol = srcNetProtocol;
        }
        setParamInitDone("targetNetProtocol");
        return this;
    }

    public PodModifyInsParam setIsNetProtocolChange() throws Exception {
        checkDepend("srcNetProtocol").checkDepend("targetNetProtocol");
        isNetProtocolChange = targetNetProtocol != null && !srcNetProtocol.equals(targetNetProtocol);
        flags.setFlags(F_NET_PROTOCOL_CHANGED, isNetProtocolChange);
        setParamInitDone("isNetProtocolChange");
        return this;
    }

    public PodModifyInsParam setIsTransIns() throws Exception {
        List<String> dependencies = Arrays.asList(
                "replicaSetMeta", "isDiskSizeChange", "isDiskTypeChange", "srcPerformanceLevel", "targetPerformanceLevel", "isEmergencyTransfer", "rcu");
        checkDepend(dependencies);
        if (isAliyun()) {
            boolean isServerless = PodCommonSupport.isServerless(srcInstanceLevel) || PodCommonSupport.isServerless(targetInstanceLevel);
            isTransIns = !(isClassCodeChange() || isDiskSizeChange || isDiskTypeChange || isPerformanceLevelChanged() || isServerless || isIoAccelerationEnabledChange || isPerfLevelChangedToPL0ForBasic) || isEmergencyTransfer;
        } else {
            isTransIns = (!isClassCodeChange() && !isDiskSizeChange) || isEmergencyTransfer;
        }

        flags.setFlags(F_TRANS_INS, isTransIns);
        setParamInitDone("isTransIns");
        return this;
    }

    public boolean isEncryptionKeyAvailable() throws Exception {
        this.checkDepend("custins").checkDepend("uid");

        // 检查云盘加密kmsKeyId无效时，禁止变配，避免卸载云盘后无法挂载，导致实例不可用
        if (!dependency.getKmsService().isCustinsByokAndKeyEnableOrNoByok(custins, uid)) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
        return true;
    }

    public PodModifyInsParam initPerfLevelChangedToPL0ForBasic() throws Exception {
        checkDepend("targetInstanceLevel")
                .checkDepend("srcInstanceLevel")
                .checkDepend("targetPerformanceLevel")
                .checkDepend("srcPerformanceLevel");
        if (isAliyun() && ESSD0_PERFORMANCELEVEL.equalsIgnoreCase(targetPerformanceLevel) && !ESSD0_PERFORMANCELEVEL.equalsIgnoreCase(srcPerformanceLevel)) {
            // only basic ins can change to pl0
            if (Objects.equals(srcInstanceLevel.getCategory(), targetInstanceLevel.getCategory())
                    && CATEGORY_BASIC.equalsIgnoreCase(targetInstanceLevel.getCategory().toString())) {
                isPerfLevelChangedToPL0ForBasic = true;
            } else {
                throw new RdsException(UNSUPPORTED_DBINSTANCE_TYPE);
            }
        }
        return this;
    }

    public boolean isOnLineResize() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        // 云上实例支持磁盘大小和磁盘性能等级两个方面进行在线变更
        if (isAliyun()) {
            return (isDiskSizeChange || isPerformanceLevelChanged()) && !isClassCodeChange() && !isDiskTypeChange && !isLocalSSD() && !isShrinkIns && !isPerfLevelChangedToPL0ForBasic;
        } else {
            return isDiskSizeChange && !isClassCodeChange() && !isDiskTypeChange && !isLocalSSD() && !isShrinkIns;
        }
    }

    public boolean isClassCodeChange() throws Exception {
        checkDepend("classCode").checkDepend("targetClassCode");
        return !classCode.equals(targetClassCode);
    }

    public boolean isOnlyDiskTypeChange() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        return !isDiskSizeChange && !isClassCodeChange() && isDiskTypeChange;
    }

    public boolean isModifyCloudSSDToCloudEssd() throws Exception {
        String srcDataDiskCategory = getSrcDiskType();
        String targetDataDiskCategory = getTargetDiskType();
        String targetPerLevl = DockerOnEcsConstants.getEssdPerLevel(targetDataDiskCategory);
        return StringUtils.isNotEmpty(srcDataDiskCategory) &&
                StringUtils.isNotEmpty(targetDataDiskCategory) &&
                srcDataDiskCategory.equals(ECS_ClOUD_SSD) &&
                targetPerLevl != null;
    }

    public boolean isModifyCloudESSDToCloudAuto() throws Exception {
        String srcDataDiskCategory = getSrcDiskType();
        String targetDataDiskCategory = getTargetDiskType();
        return StringUtils.isNotEmpty(srcDataDiskCategory) &&
                StringUtils.isNotEmpty(targetDataDiskCategory) &&
                ECS_ClOUD_ESSD.equalsIgnoreCase(srcDataDiskCategory) &&
                ECS_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory);
    }

    /**
     * check only autopl config change
     *
     * @return
     * @throws Exception
     */
    public boolean isOnlyModifyCloudAutoConfig() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        checkDepend("burstingEnabled").checkDepend("provisionedIops");
        checkDepend("dbInstanceName");
        String targetDataDiskCategory = getTargetDiskType();
        if (isDiskSizeChange
                || isClassCodeChange()
                || isPerformanceLevelChanged()
                || isDiskTypeChange
                || (!ECS_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory))) {
            return false;
        }
        return isModifyCloudAutoConfig();
    }

    public boolean isChangeInvolvingOrderModification() throws Exception {
        return isDiskSizeChange
                || isClassCodeChange()
                || isPerformanceLevelChanged()
                || isDiskTypeChange;
    }

    /**
     * check autopl config change
     *
     * @return
     * @throws Exception
     */
    public boolean isModifyCloudAutoConfig() throws Exception {
        checkDepend("isBurstingEnabledChange");
        checkDepend("dbInstanceName").checkDepend("isDiskTypeChange");
        String targetDataDiskCategory = getTargetDiskType();
        if (isDiskTypeChange || !ECS_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory)) {
            return false;
        }
        return isBurstingEnabledChange;
    }

    public boolean isModifyColdDataConfig() throws Exception {
        checkDepend("coldDataEnabled").checkDepend("dbInstanceName");
        boolean srcColdDataEnabled = dependency.getReplicaSetService().getColdDataEnabled(requestId, dbInstanceName, null);
        return srcColdDataEnabled != coldDataEnabled;
    }

    public boolean checkModifyColdDataConfigConflict() throws Exception {
        checkDepend("isDiskSizeChange").checkDepend("isDiskTypeChange");
        checkDepend("burstingEnabled").checkDepend("provisionedIops");
        checkDepend("dbInstanceName").checkDepend("coldDataEnabled");
        checkDepend("srcMinorVersion").checkDepend("dbType").checkDepend("dbVersion");
        checkDepend("uid").checkDepend("regionId").checkDepend("targetInstanceLevel");
        String targetDataDiskCategory = getTargetDiskType();
        if (isDiskSizeChange
                || isClassCodeChange()
                || isPerformanceLevelChanged()
                || isDiskTypeChange
                || (!ECS_CLOUD_AUTO.equalsIgnoreCase(targetDataDiskCategory))
                || isModifyCloudAutoConfig()) {
            return false;
        }
        dependency.getPodCommonSupport().checkColdDataSupportLimit(requestId, targetInstanceLevel, dbType, dbVersion, srcDiskType, null, srcMinorVersion, uid, regionId);
        return isModifyColdDataConfig();
    }


    public void multiWriteEngineValidate() throws Exception {
        checkDepend("replicaSetMeta");
        if (!dependency.getAligroupService().isXdbMultiWriteEngine(requestId, replicaSetMeta)) {
            return;
        }
        log.info("current replicaset is multi engine");
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
    }

    /**
     * 设置参数初始化完成标，用于其他参数依赖检查
     */
    private void setParamInitDone(String paramName) {
        initParams.put(paramName, true);
    }

    public void setIsNodeModify(Boolean flag) {
        isNodeModify = flag;
    }

    public void diskSizeValidation() throws RdsException, ApiException {
        if (targetDiskSizeGB == null) {
            return;
        }
        if (isPfs && targetDiskSizeGB % 10 != 0) {
            throw new RdsException(INVALID_CLOUD_DISK_SIZE.toArray());
        }
        // 云盘可以降低磁盘空间
        if (targetDiskSizeGB < diskSizeGB) {
            isShrinkIns = true;
        }
        if (isReadIns && isDiskSizeChange && PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
            //变配只读实例盘大小时，校验只读盘大小 > 主实例盘大小
            ReplicaSet primaryReplicaSet = dependency.getDBaasMetaService().getDefaultClient().getReplicaSet(requestId, replicaSetMeta.getPrimaryInsName(), null);
            if (targetDiskSizeGB * 1024 < primaryReplicaSet.getDiskSizeMB()) {
                log.error("read ins disk size {} < primary disk size {}.", targetDiskSizeGB, primaryReplicaSet.getDiskSizeMB());
                throw new RdsException(ErrorCode.UNSUPPORTED_READ_DBINSTANCE_DISKSIZE);
            }
        }
    }

    /**
     * 依赖检查
     */
    public PodModifyInsParam checkDepend(String paramName) throws Exception {
        if (!initParams.containsKey(paramName)) {
            throw new Exception("must init " + paramName + " first!");
        }
        return this;
    }

    /**
     * 依赖检查
     */
    public PodModifyInsParam checkDepend(List<String> paramNames) throws Exception {
        for (String paramName : paramNames) {
            if (!initParams.containsKey(paramName)) {
                throw new Exception("must init " + paramName + " first!");
            }
        }
        return this;
    }

    public boolean isXDB() throws Exception {
        checkDepend("replicaSetMeta");
        return dependency.getReplicaSetService().isReplicaSetXDB(requestId, replicaSetMeta.getName());
    }

    public boolean isCloudDisk() throws Exception {
        checkDepend("targetDiskType");
        return ReplicaSetService.isStorageTypeCloudDisk(targetDiskType);
    }

    public boolean isLocalSSD() throws Exception {
        checkDepend("targetDiskType");
        return Replica.StorageTypeEnum.LOCAL_SSD.toString().equalsIgnoreCase(targetDiskType);
    }

    public boolean isTransByNc() throws Exception {
        boolean isForce = dependency.getMysqlParamSupport().getAndCheckIsForce(params).equals(1);
        return !isCloudDisk() || isForce;
    }


    public boolean isSingleNode() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.isSingleNode(srcInstanceLevel);
    }

    public boolean isStandard() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.isStandard(srcInstanceLevel);
    }

    public boolean isCluster() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.isCluster(srcInstanceLevel);
    }

    public boolean isBaiscCategory() throws Exception {
        checkDepend("srcInstanceLevel");
        return MysqlParamSupport.checkCategory(srcInstanceLevel, InstanceLevel.CategoryEnum.BASIC);
    }

    public static void replicaNameValidate(List<Replica> currentReplicas, String tmpReplicaSetName) throws RdsException {
        for (Replica currentReplica : currentReplicas) {
            // 这里要做校验，判断replica的名字是否正在使用，
            // 否则在Common资源申请失败会对正在使用的资源做释放
            if (Objects.requireNonNull(currentReplica.getName()).contains(tmpReplicaSetName)) {
                log.error("Replica's name is {}, tmpReplicaSetName is {}, not allow to allocate resource", currentReplica.getName(), tmpReplicaSetName);
                throw new RdsException(ErrorCode.INVALID_TARGET_DBINSTANCENAME);
            }
        }
    }


    /**
     * 对比可用区配置是否发现变化。可用区模式发生变化返回true，主可用区模式对比多个role，非主可用区模式对比ZoneID
     */
    private boolean isModifyAvz(AVZInfo avzInfo, AVZInfo oldAvzInfo) throws RdsException {
        boolean modifyAvz = false;
        if (avzInfo.getDispenseMode() != oldAvzInfo.getDispenseMode()) {
            modifyAvz = true;
        } else if (avzInfo.getDispenseMode() == ParamConstants.DispenseMode.MultiAVZDispenseMode) {
            avzInfo.getMultiAVZExParamDO().sortAvailableZoneInfoAsMasterFirst();
            oldAvzInfo.getMultiAVZExParamDO().sortAvailableZoneInfoAsMasterFirst();
            List<String> newZoneList = avzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().map(AvailableZoneInfoDO::getZoneID).collect(Collectors.toList());
            List<String> oldZoneList = oldAvzInfo.getMultiAVZExParamDO().getAvailableZoneInfoList().stream().map(AvailableZoneInfoDO::getZoneID).collect(Collectors.toList());
            if (!newZoneList.equals(oldZoneList)) {
                modifyAvz = true;
            }
        } else {
            // 变配时参数可能不带ZoneID
            modifyAvz = StringUtils.isNotBlank(avzInfo.getMasterZoneId()) &&
                    !avzInfo.getMasterZoneId().equals(oldAvzInfo.getMasterZoneId());
        }
        return modifyAvz;
    }

    public PodModifyInsParam initRsTemplate() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("targetInstanceLevel")
                .checkDepend("isTargetSingleTenant")
                .checkDepend("uid");
        // serverless 实例变配会失败
//                .checkDepend("generalCloudDisk");
        if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
            scheduleTemplate = dependency.getRundPodSupport().generateBaseScheduleTemplate(replicaSetMeta.getName(), uid, podType);
        } else if (!dependency.getPodCommonSupport().isCloudDiskForIoAcceleration(generalCloudDisk)
                && dependency.getPodCommonSupport().isIoAccelerationEnabled(generalCloudDisk)) {
            scheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant,
                    Boolean.TRUE.equals(Objects.requireNonNull(generalCloudDisk.getWarmDataDisk()).getIoAccelerationEnabled()));
        } else {
            scheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant, podType);
        }
        podScheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetPodScheduleTemplate(replicaSetMeta);
        setParamInitDone("scheduleTemplate");
        setParamInitDone("podScheduleTemplate");
        return this;
    }

    //cluster addnode 保持与原实例一致
    public PodModifyInsParam initSrcScheduleTemplate() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("srcInstanceLevel")
                .checkDepend("isSrcSingleTenant");
        scheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetScheduleTemp(replicaSetMeta, srcInstanceLevel, isSrcSingleTenant, null);
        podScheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetPodScheduleTemplate(replicaSetMeta);
        setParamInitDone("scheduleTemplate");
        setParamInitDone("podScheduleTemplate");
        return this;
    }

    private String getDefaultModifyMode() throws Exception {
        List<String> dependencies = new ArrayList<>();
        dependencies.add("replicaSetMeta");
        dependencies.add("isSrcSingleTenant");
        dependencies.add("isTargetSingleTenant");
        dependencies.add("isDiskTypeChange");
        checkDepend(dependencies);
        String modifyMode = getParameterValue(params, "ModifyMode");
        if (modifyMode != null) {
            return modifyMode;
        }
        modifyMode = dependency
                .getPodParameterHelper()
                .getModifyModeEnum(
                        requestId, replicaSetMeta,
                        srcInstanceLevel, targetInstanceLevel,
                        srcDiskType, targetDiskType,
                        isDHG, isIoAccelerationEnabledChange).toString();
        return modifyMode;
    }

    public PodModifyInsParam initIsSrcSingleTenant() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("srcDiskType")
                .checkDepend("srcInstanceLevel")
                .checkDepend("isDHG");
        if (dependency.getReplicaSetService().isCloudDiskSingleTenant(requestId, replicaSetMeta)) {
            isSrcSingleTenant = true;
        } else {
            isSrcSingleTenant = false;
        }
        setParamInitDone("isSrcSingleTenant");
        return this;
    }

    public PodModifyInsParam initIsTargetSingleTenant() throws Exception {
        checkDepend("replicaSetMeta")
                .checkDepend("targetDiskType")
                .checkDepend("targetInstanceLevel")
                .checkDepend("isDHG");
        if (dependency.getReplicaSetService().isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, isDHG)) {
            isTargetSingleTenant = true;
        } else {
            isTargetSingleTenant = false;
        }
        setParamInitDone("isTargetSingleTenant");
        return this;
    }

    public PodModifyInsParam initSrcMinorVersion() throws Exception {
        CustinsParamDO custinsParamDO = dependency.getCustinsParamService().getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
        if (custinsParamDO != null) {
            srcMinorVersion = custinsParamDO.getValue();
        } else {
            throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
        }
        setParamInitDone("srcMinorVersion");
        return this;
    }


    public PodModifyInsParam setServerlessInfo() throws ApiException, InvocationTargetException, IllegalAccessException {
        ServerlessSpec serverlessSpec = dependency.getServerlessResourceService()
                .getServerlessSpec(requestId, getDbInstanceName());
        setSrcServerlessSpec(serverlessSpec);

        targetServerlessSpec = new ServerlessSpec();
        BeanUtils.copyProperties(targetServerlessSpec, srcServerlessSpec);

        //Update target Serverless info
        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.SERVERLESS_RCU)) {

            targetServerlessSpec.setRcu(
                    Double.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.SERVERLESS_RCU)));
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.SCALE_MIN)) {
            targetServerlessSpec.setScaleMin(
                    Double.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.SCALE_MIN))
            );
        }
        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.SCALE_MAX)) {
            targetServerlessSpec.setScaleMax(
                    Double.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.SCALE_MAX))
            );
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.AUTO_PAUSE)) {
            targetServerlessSpec.setAutoPause(
                    Boolean.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.AUTO_PAUSE))
            );
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE)) {
            targetServerlessSpec.setSecondsUntilAutoPause(
                    Integer.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.SECONDS_UNTIL_AUTO_PAUSE))
            );
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.DAS_AUTO_PAUSE)) {
            targetServerlessSpec.setDasAutoPause(
                    Boolean.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.DAS_AUTO_PAUSE))
            );
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.KEEP_RUNNING_TIME)) {
            targetServerlessSpec.setKeepRunningTime(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.KEEP_RUNNING_TIME));
        }

        if (dependency.getMysqlParamSupport().hasParameter(params, ServerlessConstant.SWITCH_FORCE)) {
            targetServerlessSpec.setSwitchForce(
                    Boolean.valueOf(dependency.getMysqlParamSupport().getParameterValue(params, ServerlessConstant.SWITCH_FORCE)));
        }

        setParamInitDone("serverlessInfo");
        log.info("{} current serverlessspec is {} !", getDbInstanceName(), srcServerlessSpec);
        log.info("{} target serverlessspec is {} !", getDbInstanceName(), targetServerlessSpec);
        return this;
    }

    public boolean isModifyServerlessStorage() {
        String SourceBiz = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.SOURCE_BIZ);
        String Storage = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.STORAGE);
        if (Storage == null || SourceBiz == null) {
            return false;
        }

        if (SourceBiz.equalsIgnoreCase(ServerlessConstant.SERVERLESS_DISK_SCALE_SOURCEBIZ) && Integer.valueOf(Storage) > 0) {
            return true;
        }
        return false;
    }

    public boolean isServerlessConfigChanged() {
        return srcServerlessSpec.compareModifyParams(targetServerlessSpec);
    }

    public void isValidModifyRcu() throws RdsException {
        // RCU range check: 0.5 ~ 64 and is a multiple of 0.5
        if (!(0.5 <= targetServerlessSpec.getScaleMin() &&
                targetServerlessSpec.getScaleMin() <= targetServerlessSpec.getScaleMax() &&
                targetServerlessSpec.getScaleMax() <= 64 &&
                targetServerlessSpec.getScaleMin() % 0.5 == 0 &&
                targetServerlessSpec.getScaleMax() % 0.5 == 0
        )) {
            throw new RdsException(INVALID_PARAMETERS, "ModifyDB: RCU range should be 0.5 ~ 64 and a multiple of 0.5");
        }

        /*if (targetServerlessSpec.getScaleMax() < targetServerlessSpec.getScaleMin()){
            throw new RdsException(INVALID_PARAMETERS);

        }*/
    }

    public void isValidModifyStorage() throws RdsException {
        if (targetDiskSizeGB < diskSizeGB) {
            throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
        }
    }


    public boolean isYaoChiRequest() {
        return "yaochi".equalsIgnoreCase(getParameterValue(params, ParamConstants.ACCESSID));
    }


    public boolean isAliyun() {
        return PodParameterHelper.isAliYun(replicaSetMeta.getBizType());
    }

    public boolean isPerformanceLevelChanged() {
        // cloud auto  don't check performance level change
        if (ECS_CLOUD_AUTO.equalsIgnoreCase(targetDiskType)) {
            return false;
        }
        return !Objects.equals(srcPerformanceLevel, targetPerformanceLevel);
    }

    public void setFlags(TransferTask transferTask) throws Exception {
        flags.setFlags(F_XDB, isXDB());
        flags.setFlags(F_TRANS_NC, isTransByNc());
        flags.setFlags(F_LOCAL_SSD, isLocalSSD());
        flags.setFlags(F_SINGLE_NODE, isSingleNode());
        flags.setFlags(F_TARGET_SERVERLESS, PodCommonSupport.isServerless(targetInstanceLevel));

        // 申请资源时必须是支持本地升降配的模式
        if (isAliyun() && LOCAL_MODIFY_POD_MODES.contains(modifyMode)) {
            // 临时实例和原实例一样，是全本地
            if (Objects.equals(transferTask.getSrcReplicaSetName(), transferTask.getDestReplicaSetName())) {
                flags.setFlags(F_ELASTIC_MOD_POD_MODE, true);
                flags.setFlags(F_ELASTIC_MOD_POD_LOCAL, true);
            } else if (StringUtils.isNotEmpty(transferTask.getParameter())) {
                TransParams transParams = JSON.parseObject(transferTask.getParameter(), TransParams.class);
                if (transParams.getTransParamList() != null) {
                    for (TransParam transParam : transParams.getTransParamList()) {
                        // 前后ReplicaId一样，是半跨机
                        if (Objects.equals(transParam.getSrcReplicaId(), transParam.getDstReplicaId())) {
                            flags.setFlags(F_ELASTIC_MOD_POD_MODE, true);
                            return;
                        }
                    }
                }
            }
        }
    }


    public PodModifyInsParam setGeneralCloudDiskConfig() throws Exception {
        this.checkDepend("dbVersion");
        generalCloudDisk = new GeneralCloudDisk();
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);

        boolean ioAccelerationEnabled = dependency.getPodCommonSupport().transferIoAccelerationEnabledType(dependency.getDBaasMetaService().getDefaultClient().listReplicaSetLabels(requestId, replicaSetMeta.getName()).get(IO_ACCELERATION_ENABLED));
        srcIoAccelerationEnabled = ioAccelerationEnabled;
        setParamInitDone("srcIoAccelerationEnabled");

        boolean newIoAccelerationEnabled = ioAccelerationEnabled;
        if (dependency.getMysqlParamSupport().hasParameter(params, IO_ACCELERATION_ENABLED)) {
            newIoAccelerationEnabled = dependency.getPodCommonSupport().transferIoAccelerationEnabledType(dependency.getMysqlParamSupport().getParameterValue(params, IO_ACCELERATION_ENABLED));
        }
        isIoAccelerationEnabledChange = newIoAccelerationEnabled != ioAccelerationEnabled;
        if (isIoAccelerationEnabledChange) {
            ioAccelerationEnabled = newIoAccelerationEnabled;
        }
        log.info("target ioAccelerationEnabled:{}, isIoAccelerationEnabledChange:{}, requestId: {}", ioAccelerationEnabled, isIoAccelerationEnabledChange, requestId);

        WarmDataDisk warmDataDisk = new WarmDataDisk()
                .ioAccelerationEnabled(ioAccelerationEnabled);

        if (ioAccelerationEnabled) {
            warmDataDisk.ioAccelerationType(WarmDataDisk.IoAccelerationTypeEnum.BPE);

            // When adding a node to a cluster instance, there will only be two parameters: src Instance Level and src Disk Type
            InstanceLevel instanceLevel = initParams.containsKey(targetInstanceLevel) ? targetInstanceLevel : srcInstanceLevel;
            String diskType = initParams.containsKey(targetDiskType) ? targetDiskType : srcDiskType;

            dependency.getPodCommonSupport().checkIoAccelerationCondition(requestId, dbVersion, instanceLevel, diskType);

            //todo:mgr not supported
            if (dependency.getReplicaSetService().isMgr(requestId, replicaSetMeta.getName())) {
                throw new RdsException(ErrorCode.GROUP_REPLICATION_NOT_SUPPORT_Invalid_Parameters);
            }
        }

        generalCloudDisk.warmDataDisk(warmDataDisk);

        setParamInitDone("generalCloudDisk");
        return this;
    }

    public boolean isIoAccelerationEnabled() {
        return generalCloudDisk != null && generalCloudDisk.getWarmDataDisk() != null
                && Boolean.TRUE.equals(generalCloudDisk.getWarmDataDisk().getIoAccelerationEnabled());
    }

    /*
     ** 检查密钥是否为自定义密钥，自定义密钥只能变配为单租户（扩容除外）
     */
    public PodModifyInsParam checkEncryptionKey() throws Exception {
        this.checkDepend("custins");
        ResourceDO permitSkipEncryptionCheck = dependency.getResourceService().getResourceByResKey("PERMIT_SKIP_ENCRYPTION_CHECK");
        if (permitSkipEncryptionCheck != null) {
            String[] permitCustinsList = permitSkipEncryptionCheck.getRealValue().split(",");
            for (String custins : permitCustinsList) {
                if (this.getCustins().getInsName().equals(custins)) {
                    return this;
                }
            }
        }
        if (isClassCodeChange() || isDiskTypeChange) {
            boolean isSingletenant = dependency.getReplicaSetService().isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, isDHG);
            dependency.getMysqlEncryptionService().checkEncryptionKeyByReplicaSet(requestId, replicaSetMeta, isSingletenant);
        }
        return this;
    }

    public PodModifyInsParam initOptimizedWritesInfo() throws Exception {
        checkDepend("dbInstanceName");
        primaryOptimizedWritesString = null;
        primaryInitOptimizedWritesString = null;
        targetOptimizedWritesString = null;
        targetInitOptimizedWritesString = null;
        String primaryOptimizedWritesInfo = dependency.getDBaasMetaService().getDefaultClient().getReplicaSetLabel(requestId, dbInstanceName, OPTIMIZED_WRITES_INFO);
        if (!StringUtils.isEmpty(primaryOptimizedWritesInfo)) {
            primaryOptimizedWritesString = String.valueOf(dependency.getPodCommonSupport().isOptimizedWrites(primaryOptimizedWritesInfo));
            primaryInitOptimizedWritesString = String.valueOf(dependency.getPodCommonSupport().isInitOptimizedWrites(primaryOptimizedWritesInfo));
        }
        String optimizedWrites = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null);
        if (StringUtils.isNotBlank(optimizedWrites) && PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites)) {
            targetOptimizedWritesString = "true";
        }
        if ("true".equals(targetOptimizedWritesString) || "true".equals(primaryInitOptimizedWritesString)) {
            targetInitOptimizedWritesString = "true";
        } else {
            targetInitOptimizedWritesString = "false";
        }
        setParamInitDone("primaryOptimizedWritesString");
        setParamInitDone("primaryInitOptimizedWritesString");
        setParamInitDone("targetOptimizedWritesString");
        setParamInitDone("targetInitOptimizedWritesString");
        log.info("requestId:{} primaryOptimizedWritesString:{} primaryInitOptimizedWritesString:{} targetOptimizedWritesString:{} targetInitOptimizedWritesString:{}",
                requestId, primaryOptimizedWritesString, primaryInitOptimizedWritesString, targetOptimizedWritesString, targetInitOptimizedWritesString);
        return this;
    }


    public PodModifyInsParam initSrcCompressionMode() throws Exception {
        checkDepend("dbInstanceName");
        srcCompressionMode = dependency.getCloudDiskCompressionHelper().getCompressionMode(requestId, dbInstanceName, null);
        setParamInitDone("srcCompressionMode");
        if (CloudDiskCompressionHelper.isCompressionModeOn(srcCompressionMode)) {
            compressionRatio = dependency.getCloudDiskCompressionHelper().getCompressionRatio(requestId, dbInstanceName, null, null, null);
        }
        return this;
    }


    public PodModifyInsParam initTargetCompressionMode() throws Exception {
        checkDepend("dbInstanceName");
        String paramCompressionMode = dependency.getMysqlParamSupport().getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_MODE);
        targetCompressionMode = dependency.getCloudDiskCompressionHelper().getCompressionMode(requestId, dbInstanceName, paramCompressionMode);
        setParamInitDone("targetCompressionMode");


        checkDepend("srcCompressionMode");
        isCompressionModeChange = !StringUtils.equals(srcCompressionMode, targetCompressionMode);
        setParamInitDone("isCompressionModeChange");
        return this;
    }

    public PodModifyInsParam correctRuntimeParams() throws Exception {
        checkDepend("regionId").checkDepend("uid").checkDepend("targetInstanceLevel").checkDepend("avzInfo");
        PodType runtimeType = dependency.getRundPodSupport().getPodTypeByGrayConfig(regionId, uid, targetInstanceLevel, avzInfo);
        if (podType != runtimeType) {
            podType = runtimeType;
        }
        initRsTemplate();
        initVbm();
        return this;
    }

    public void checkCompressionLimit() throws Exception {
        checkDepend("isCompressionModeChange").checkDepend("targetCompressionMode").checkDepend("targetInstanceLevel")
                .checkDepend("dbType").checkDepend("uid").checkDepend("regionId").checkDepend("targetDiskType");
        // limit close
        if (isCompressionModeChange && CloudDiskCompressionHelper.isCompressionModeOff(targetCompressionMode)) {
            // todo: modify error code
            String errMsg = "cloud compression cannot be closed.";
            log.warn("{} - {}", requestId, errMsg);
            throw new RdsException(INTERNAL_FAILURE);
        }
        // compression on limit
        if (CloudDiskCompressionHelper.isCompressionModeOn(targetCompressionMode)) {
            // this check diskSizeGBBeforeCompression or don't check diskSizeGB
            dependency.getCloudDiskCompressionHelper().checkCompressionSupportLimit(requestId, targetInstanceLevel, dbType, uid, regionId, Optional.ofNullable(diskSizeGBBeforeCompression).map(Integer::longValue).orElse(null), targetDiskType, true);
            // check readIns,  if open master, should open readIns first.
            if (!isReadIns) {
                dependency.getCloudDiskCompressionHelper().checkReadInsCompressionModeOn(requestId, dbInstanceName, true);
            }
        }

        // compression change limit
        if (isCompressionModeChange) {
            Map<String, Boolean> changeLimitMap = new HashMap<>();
            changeLimitMap.put("ClassCodeChange", isClassCodeChange());
            changeLimitMap.put("DiskSizeChange", isDiskSizeChange);
            changeLimitMap.put("DiskTypeChange", isDiskTypeChange);
            changeLimitMap.put("PerformanceLevelChange", isPerformanceLevelChanged());
            changeLimitMap.put("BurstingChange", isBurstingEnabledChange || isProvisionedIopsChange);
            changeLimitMap.put("IoAccelerationEnabledChange", isIoAccelerationEnabledChange);
            changeLimitMap.put("ColdDataConfigChange", isModifyColdDataConfig());

            Map<String, Object> checkResult = dependency.getCloudDiskCompressionHelper().checkCompressionChangeLimit(true, changeLimitMap);
            String limitReason = CloudDiskCompressionHelper.VALUE_BLANK_REASON;
            boolean isSupport = true;
            if (MapUtils.isNotEmpty(checkResult)) {
                limitReason = Optional.ofNullable((String) checkResult.get(CloudDiskCompressionHelper.LIMIT_REASON)).orElse(CloudDiskCompressionHelper.VALUE_BLANK_REASON);
                isSupport = Optional.ofNullable((Boolean) checkResult.get(CloudDiskCompressionHelper.SUPPORT_COMPRESSION_CHANGE)).orElse(true);
            }
            if (!isSupport) {
                // todo: modify error code
                String errMsg = String.format("cloud compression limited, limit reason: [%s]", limitReason);
                log.warn("{} - {}", requestId, errMsg);
                throw new RdsException(INTERNAL_FAILURE);
            }
        }

        // if targetCompressionMode is on and diskSizeGBBeforeCompression is not null, save diskSizeGBBeforeCompression in custins_param temporarily
        if (CloudDiskCompressionHelper.isCompressionModeOn(targetCompressionMode) && Objects.nonNull(diskSizeGBBeforeCompression)) {
            Map<String, String> params = new HashMap<>();
            params.put(CUSTINS_PARAM_TMP_DISK_SIZE_BEFORE_COMPRESSION, String.valueOf(diskSizeGBBeforeCompression * 1024));
            dependency.getDBaasMetaService().getDefaultClient().updateReplicaSetLabels(requestId, dbInstanceName, params);
        }
    }


}
