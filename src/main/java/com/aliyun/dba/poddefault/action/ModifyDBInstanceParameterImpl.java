package com.aliyun.dba.poddefault.action;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.IntervalUtil;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.ParamContext;
import com.aliyun.dba.commonkindcode.support.ParamExprException;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.commonkindcode.support.checker.IntValueChecker;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamGroupsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.entity.MycnfChangeLog;
import com.aliyun.dba.instance.entity.MycnfTemplate;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.serverless.action.support.common.ServerlessParamsSupport;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.serverless.action.support.common.ServerlessConstant.SERVERLESS_CATEGORY_TYPE;


@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultModifyDBInstanceParameterImpl")
public class ModifyDBInstanceParameterImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(com.aliyun.dba.commonkindcode.action.ModifyDBInstanceParameterImpl.class);

    @Autowired
    protected MysqlParameterHelper mysqlParaHelper;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected ReplicaSetService replicaSetService;
    @Autowired
    protected DBaasMetaService dBaasMetaService;
    @Autowired
    protected WorkFlowService workFlowService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected CustinsParamGroupsService custinsParamGroupsService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private KmsService kmsService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    private ServerlessParamsSupport serverlessParamsSupport;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    MinorVersionServiceHelper minorVersionServiceHelper;

    private static final String PARAM_FLUSH_PLACES = "FlushPlaces";
    private static final String PARAM_DRY_RUN = "DryRun";
    @Autowired
    private MysqlParamGroupHelper mysqlParamGroupHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParaHelper.getParameterValue(ParamConstants.REQUEST_ID);
        try {
            ReplicaSet replicaSet = replicaSetService.getAndCheckUserReplicaSet(params);
            String dbInstanceName = mysqlParaHelper.getDBInstanceName();

            if ("true".equals(mysqlParaHelper.getParameterValue(PARAM_DRY_RUN))) {
                Map<String, Object> data = new HashMap<>();
                data.put("DBInstanceID", replicaSet.getId());
                data.put("DBInstanceName", dbInstanceName);
                return data;
            }

            // init info
            String bid = mysqlParaHelper.getBID();
            String uid = mysqlParaHelper.getUID();
            Integer userId = checkService.getAndCheckUserId(bid, uid, null);
            custins = checkService.getAndCheckCustInstance(dbInstanceName, bid, userId);

            String flushPlaces = mysqlParaHelper.getParameterValue(PARAM_FLUSH_PLACES);

            String accessId = mysqlParaHelper.getParameterValue(ParamConstants.ACCESSID);
            // 设置切换时间
            //立即切换，指定时间切换，运维时间切换
            Map<String, Object> switchInfoMap = podParameterHelper.getSwitchInfo(params);

            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), false);
            CustinsParamDO custinsParamGroupId = custinsParamService.getCustinsParam(replicaSet.getId().intValue(), CUSTINS_PARAM_NAME_PARAM_GROUP_ID);

            if (custins == null) {
                custins = custinsService.getCustInstanceByCustinsId(replicaSet.getId().intValue());
            }

            // 检查实例状态
            if (!custins.isActive()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
            } else if (!custins.isNoLock()) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
            }

            String requestParamGroupId = mysqlParaHelper.getParameterValue("ParameterGroupId");
            String currentReplicaSetParamGroupId = custinsParamGroupId == null ? "" : custinsParamGroupId.getValue();
            boolean isXengine = "xengine".equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(currentReplicaSetParamGroupId));
            boolean isCluster = MysqlParamSupport.isCluster(instanceLevel.getCategory().toString());


            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, dbInstanceName);
            boolean isArm = false;
            boolean isPolarxHatp = false;
            String paramGroupType = null;
            String minorVersion = "mysql_99999999";
            String sep = "_";
            if (labels != null) {
                isArm = "arm".equalsIgnoreCase(labels.get("instructionSetArch"));
                isPolarxHatp = StringUtils.isNotEmpty(labels.get("is_polarx_hatp"));
                minorVersion = labels.get("minor_version");
            }
            if (minorVersion.contains(":")) {
                sep = ":";
            }

            // Extract version number, compatible with both rds_11111 and rds_duckdb_11111 formats
            String versionNum = "99999999";
            if (minorVersion != null) {
                String[] parts = minorVersion.split(sep);
                if (parts.length >= 2) {
                    // For rds_11111 format, take the second part
                    // For rds_duckdb_11111 format, take the last part
                    versionNum = parts[parts.length - 1];
                }
            }

            ParamContext paramContext = new ParamContext(custins, currentReplicaSetParamGroupId);
            paramContext.setIgnoreVisible(true);
            paramContext.setAliGroup(PodParameterHelper.isAliGroup(replicaSet.getBizType()));
            paramContext.setArm(isArm);
            paramContext.setPolarxHatp(isPolarxHatp);
            paramContext.setCategory(instanceLevel.getCategory().toString());
            paramContext.setMinorVersion(Integer.valueOf(versionNum));
            paramContext.setCpuCoreCount(instanceLevel.getCpuCores());
            paramContext.setMemSize(instanceLevel.getMemSizeMB().longValue());
            paramContext.setDiskSize(replicaSet.getDiskSizeMB().longValue());
            paramContext.setMaxConnection(instanceLevel.getMaxConn());

            InstanceConfigListMapResult insConfig = dBaasMetaService.getDefaultClient().listReplicaSetConfigsBatch(requestId, Collections.singletonList(dbInstanceName), null, null, null, null);
            Optional.ofNullable(insConfig)
                .map(InstanceConfigListMapResult::getItems)
                .map(arr -> arr.stream().anyMatch(item -> "collation_server".equals(item.getName())))
                .ifPresent(paramContext::setExistCollationServer);

            ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(
                    requestId, dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(
                            requestId, custins.getInsName()));
            paramContext.set80Beta(StringUtils.startsWith(serviceSpec.getTag(),
                    MinorVersionServiceHelper.ServiceTag.TAG_ALISQL_BETA_DOCKER_IMAGE.getTagPrefix())
            );

            if ((custins.getInsType() == 3 || custins.getInsType() == 4) && custins.getPrimaryCustinsId() != 0) {
                CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
                if (primaryCustins != null) {
                    InstanceLevelDO primaryLevel = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
                    paramContext.setCategory(primaryLevel.getCategory());
                }
            }

            String taskKey = null;
            Integer syncMode = null;
            Map<String, MycnfTemplate> mycnfTemp;
            Map<String, Object> parameterMap = new HashMap<>();

            if (mysqlParaHelper.hasParameter("Parameters")) {
                // for change with parameters
                // Validate parameters
                mycnfTemp = parameterGroupTemplateGenerator.getSysBaseParamTempMap(paramContext);
                parameterMap = mysqlParaHelper.getAndCheckParameters();
                if (replicaSetService.isMgr(requestId, replicaSet.getName())) {
                    replicaSetService.isMgrSupportParam(parameterMap);
                }
                for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
                    if ("performance_schema".equals(entry.getKey()) && mycnfTemp.containsKey("performance_schema")) {
                        MycnfTemplate parameter = mycnfTemp.get(entry.getKey());
                        String keyValue = entry.getValue().toString();
                        String keyUnit = parameter.getUnit();
                        parameterMap.put("performance_schema", IntValueChecker.parseParamValueSwitchByUnit(keyValue, keyUnit));
                    }
                }

                ParamChecker.parameterCheck(parameterMap, accessId, mycnfTemp, paramContext);

            } else if (StringUtils.isNotEmpty(requestParamGroupId)) {
                // for change with parameter group
                boolean isMgrTemplate = isClusterTemplate(requestParamGroupId);
                if (isMgrTemplate && (ReplicaSetService.isReplicaSetAliGroup(replicaSet) || !isCluster)){
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
                }

                Map parameterGroup = mysqlParaHelper.getAndCheckParameterGroupId(replicaSet);
                long pid = (new BigInteger(parameterGroup.get("ParameterGroupId").toString())).longValue();
                paramGroupType = parameterGroup.get("ParameterGroupType").toString();
                boolean isSysParamGroup = "0".equals(paramGroupType);
                paramContext.setParamGroupId(requestParamGroupId);
                mycnfTemp = parameterGroupTemplateGenerator.getSysBaseParamTempMap(paramContext);
                if (!isSysParamGroup) {
                    // 用户参数模板校验
                    parameterMap = ParamChecker.getParameterGroupDetail(pid, custinsParamGroupsService);
                    // 形态拦截
                    for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
                        if ("innodb_buffer_pool_size".equals(entry.getKey()) && isXengine) {
                            return createErrorResponse(ErrorCode.INVALID_PARAM_DB_STORAGE_ENGINE);
                        }
                    }

                    ParamChecker.parameterCheck(parameterMap, accessId, mycnfTemp, paramContext);
                } else {
                    // 系统参数模板
                    Map<String, Object> finalParameterMap = parameterMap;
                    mycnfTemp.forEach((k, v) -> finalParameterMap.put(k, v.getDefaultValue()));
                    boolean isRequestXengine = "xengine".equalsIgnoreCase(SysParamGroupHelper.getDBStorageEngine(requestParamGroupId));
                    boolean isDefaultEngineChanged = isXengine != isRequestXengine;

                    // 系统参数模板校验版本，如果是arm，校验版本
                    if (isRequestXengine && PodCommonSupport.isArm(instanceLevel)) {
                        podCommonSupport.checkARMSupportXEngineMinorVersion(minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, replicaSet.getId().intValue()), requestParamGroupId);
                    }

                    //sync_binlog_mode
                    Integer syncBinlogMode = SysParamGroupHelper.getSyncBinlogMode(requestParamGroupId);

                    String performanceMode = SysParamGroupHelper.getPerformanceMode(requestParamGroupId);
                    boolean sysParamGroupIsMgr = replicaSetService.isMgr(performanceMode);
                    boolean currentIsMgr = replicaSetService.isMgr(requestId, replicaSet.getName());
                    if (sysParamGroupIsMgr != currentIsMgr) {
                        logger.info("replication type changed. currentIsMgr {} sysParamGroupIsMgr {}", currentIsMgr, sysParamGroupIsMgr);
                        syncMode = SysParamGroupHelper.getSyncMode(requestParamGroupId);
                        if (currentIsMgr) {
                            taskKey = SWITCH_MGR_TO_SEMI_SYNC;

                        } else {
                          replicaSetService.isSupportMgr(replicaSet.getName(), true);
                          taskKey = BUILD_GROUP_REPLICATION;
                        }
                    }

                    parameterMap.putAll(getSyncBinlogParams(String.valueOf(syncBinlogMode)));

                    // 当参数模板引擎发生变化的时候，为保证bp_size参数正常，做下列适配(aone:36749839)
                    if (isDefaultEngineChanged && !paramContext.isAliGroup()) {
                        String bpInstancesValue = BP_INSTANCES_XENGINE_DEFAULT_VALUE;
                        if (mycnfTemp.containsKey(INNODB_BUFFER_POOL_INSTANCES)) {
                            bpInstancesValue = parameterMap.get(INNODB_BUFFER_POOL_INSTANCES).toString();
                        }
                        if (isRequestXengine) {
                            // 由innodb>xengine，bp_instance设置为1 并且向changeLog中插入is_applied=1的数据
                            // 用于保证新架构刷参任务流中bp_size的divide_base计算结果不出异常
                            writeAppliedChangeLog(requestId, replicaSet, custins.getId(),
                                    INNODB_BUFFER_POOL_INSTANCES, BP_INSTANCES_XENGINE_DEFAULT_VALUE);
                        } else {
                            // 由xengine>innodb，bp_instance设置为参数模板默认值 并且向changeLog中插入is_applied=1的数据
                            // 用于保证新架构刷参任务流中bp_size的divide_base计算结果不出异常
                            writeAppliedChangeLog(requestId, replicaSet, custins.getId(),
                                    INNODB_BUFFER_POOL_INSTANCES, bpInstancesValue);
                        }
                    }
                    // 当通过xengine引擎参数模板刷参时，bp_instance设置为1，经过后面writeChangeLog比对后写入changelog 然后同步到 mycnf和快照
                    // 当通过innodb引擎参数模板刷参时，bp_instance当前为参数模板默认值，经过后面writeChangeLog比对后写入changelog 然后同步到 mycnf和快照
                    if (isRequestXengine) {
                        parameterMap.put(INNODB_BUFFER_POOL_INSTANCES, BP_INSTANCES_XENGINE_DEFAULT_VALUE);
                    }

                    // 默认存储引擎没有发现变化时，不刷bp size参数
                    // FIXME: 单租户场景下，如果实例从xengine变innodb，会刷成75%的BP
                    if (!isDefaultEngineChanged) {
                        parameterMap.remove(INNODB_BUFFER_POOL_SIZE);
                    }

                    if (isXengine && isDefaultEngineChanged) {
                        mysqlParamGroupHelper.checkXengineToInnoDBConversion(custins.getId(), requestParamGroupId);
                    }
                }

                // 核心参数在刷模板时不要改动，除非用户直接指定修改
                for (String param : PodDefaultConstants.MYSQL_RESERVED_PARAMS) {
                    if (parameterMap.containsKey(param)) {
                        logger.info("remove reserved param {}={}", param, parameterMap.get(param));
                        parameterMap.remove(param);
                    }
                }
            } else {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }

            // 拦截serverless刷连接数参数（单独刷参或者使用参数模板刷参）
            if (SERVERLESS_CATEGORY_TYPE.contains(instanceLevel.getCategory().getValue())) {
                Set<String> filterParams = serverlessParamsSupport.getKernelParamsNoSupportEdit();
                for (String param: filterParams) {
                    if (parameterMap.containsKey(param)) {
                        logger.error("serverless not support modify param: " + param);
                        return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                    }
                }

                serverlessParamsSupport.checkBpSize(String.valueOf(parameterMap.get("innodb_buffer_pool_size")),
                                                    paramContext.getParser());
            }

            //根据参数配置的内核小版本做一层过滤
            parameterMap = parameterMap.entrySet().stream().filter(k -> {
                try {
                    MycnfTemplate mycnfValue = mycnfTemp.get(k.getKey());
                    if (mycnfValue != null) {
                        return StringUtils.isEmpty(mycnfValue.getMinorVersion()) || IntervalUtil.isInTheInterval(versionNum, mycnfValue.getMinorVersion());
                    }
                } catch (Exception e) {
                    logger.warn("[{}] ignore filter", k.getKey());
                }
                return true;
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            // Write to change log
            List<ConfigChangeLog> changeLogs = generateChangeLog(requestId, replicaSet, parameterMap);
            // 如果changelog为空，且复制模式没有变化，则直接返回。
            if (changeLogs.isEmpty() && StringUtils.isEmpty(taskKey)) {
                Map<String, Object> data = new HashMap<>();
                data.put("TaskId", null);
                data.put("DBInstanceID", replicaSet.getId());
                data.put("DBInstanceName", dbInstanceName);
                data.put("Message", "no params need to modify");
                return data;
            }

            // 如果备份模板超过10个，自动删除时间最早的模板
            Integer backUpCount = custinsParamGroupsService.getBackUpParamGroupsByInsName(replicaSet.getName());
            if (backUpCount >= 10) {
                custinsParamGroupsService.deleteBackUpParamGroupsByInsName(custins.getInsName());
            }

            boolean flushMycnf = StringUtils.equalsIgnoreCase(flushPlaces, "Mycnf");
            boolean needRestart = false;
            for (ConfigChangeLog changeLog : changeLogs) {
                if (!mycnfTemp.containsKey(changeLog.getName())) {
                    continue;
                }
                if (mycnfTemp.get(changeLog.getName()).getIsDynamic().equals(0)) {
                    needRestart = true;
                    break;
                }
            }

            if (needRestart && !kmsService.isCustinsByokAndKeyEnableOrNoByok(replicaSet, mysqlParamSupport.getParameterValue(params, ParamConstants.UID))) {
                throw new RdsException(ErrorCode.INVALID_KMS_KEY);
            }

            var client = dBaasMetaService.getDefaultClient();
            // ~100ms each request
            // keep sequential if items count is less than or equal to 10
            var stream = changeLogs.size() > 10 ? changeLogs.parallelStream() : changeLogs.stream();
            var changeLogIds = stream.map(e -> {
                try {
                    var changeLog = client.commitReplicaSetConfigsChange(requestId, replicaSet.getName(), e);
                    return changeLog.getId();
                } catch (ApiException ex) {
                    throw new RuntimeException(ex);
                }
            }).collect(Collectors.toList());

            // dispatch task
            String domain = ReplicaSetService.isTDDL(replicaSet) ? "xdb" : replicaSet.getService();
            if (replicaSetService.isTddlTaskMigrate(requestId, replicaSet)) {
                domain = "mysql";
                taskKey = needRestart ? TASK_TDDL_FLUSH_PARAM_WITH_RESTART : TASK_FLUSH_PARAM;
            } else if (ReplicaSetService.isTDDL(replicaSet)) {
                taskKey = TASK_FLUSH_PARAM;  //集团只有这一个
            } else if (StringUtils.isEmpty(taskKey)) {
                if (flushMycnf) {
                    taskKey = TASK_FLUSH_PARAM_MYCNF;  //参数只刷配置文件
                } else {
                    taskKey = needRestart ? TASK_FLUSH_PARAM_WITH_RESTART : TASK_FLUSH_PARAM;
                }
            }

            Map<String, Object> parameterMapForRead = new HashMap<>();
            for (ConfigChangeLog changeLog : changeLogs) {
                if (PodDefaultConstants.MYSQL_CUSTINSPARAM_SYNC_READINS_SET.contains(changeLog.getName())) {
                    parameterMapForRead.put(changeLog.getName(), changeLog.getNewValue());
                }
            }

            //如果有需要刷只读的参数，这里要同步刷下去
            Map<String, String> readOnlyTaskParam = null;
            if (MapUtils.isNotEmpty(parameterMapForRead)) {
                // 获取该实例下面的只读实例
                List<ReplicaSet> readOnlyReplicaSetList = dBaasMetaService.getDefaultClient()
                        .listReplicaSetSubIns(requestId, replicaSet.getName(), ReplicaSet.InsTypeEnum.READONLY.toString())
                        .getItems();
                // 该实例下面是否有只读实例
                if (CollectionUtils.isNotEmpty(readOnlyReplicaSetList)) {
                    readOnlyTaskParam = new HashMap<>();
                    // 补充需要自动同步到只读实例的参数
                    for (ReplicaSet replicaSetReadOnly : readOnlyReplicaSetList) {
                        List<ConfigChangeLog> changeLogsReadOnly = generateChangeLog(requestId, replicaSetReadOnly, parameterMapForRead);
                        List<Long> changeLogIdsReadOnly = new ArrayList<>();
                        for (ConfigChangeLog roChangeLog : changeLogsReadOnly) {
                            ConfigChangeLog changeLog = dBaasMetaService.getDefaultClient().commitReplicaSetConfigsChange(requestId, replicaSetReadOnly.getName(), roChangeLog);
                            changeLogIdsReadOnly.add(changeLog.getId());
                        }
                        readOnlyTaskParam.put(replicaSetReadOnly.getName(), StringUtils.join(changeLogIdsReadOnly, ","));
                    }
                }
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("modifyParameter", true);
            jsonObject.put("requestId", requestId);
            jsonObject.put("replicaSetName", dbInstanceName);
            jsonObject.put("parameterGroupId", requestParamGroupId);
            jsonObject.put("restartInstance", needRestart);
            jsonObject.put("changeLogIds", StringUtils.join(changeLogIds, ","));
            jsonObject.put("parameterGroupType", paramGroupType);
            if (MapUtils.isNotEmpty(readOnlyTaskParam)) {
                jsonObject.put("readInsChangLogs", JSONObject.toJSON(readOnlyTaskParam));
            }
            if (syncMode != null) {
                jsonObject.put("syncMode", syncMode);
            }

            jsonObject.put(CustinsSupport.SWITCH_KEY, switchInfoMap);
            String parameter = jsonObject.toJSONString();

            Object taskId = workFlowService.dispatchTask("custins", dbInstanceName, domain, taskKey, parameter, WorkFlowService.TASK_PRIORITY_COMMON);
            if (needRestart && !flushMycnf) {
                //只刷配置文件，不需要设置实例状态
                dBaasMetaService.getDefaultClient().updateReplicaSetStatus(requestId, replicaSet.getName(), ReplicaSet.StatusEnum.RESTARTING.toString());
            }

            /*
             * 如果用户修改sync_binlog，需要额外记录到custins_param表当中
             */
            mysqlParaHelper.checkAddUserSyncBinlogCustinsParam(parameterMap, replicaSet.getId().intValue());

            Map<String, Object> data = new HashMap<>();
            data.put("TaskId", taskId);
            data.put("DBInstanceID", replicaSet.getId());
            data.put("DBInstanceName", dbInstanceName);


            return data;
        } catch (ParamExprException e) {
            logger.error(requestId + " ParamExprException: ", e);
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, e.getMessage());
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", e.getMessage()});
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            ActionParamsProvider.ACTION_PARAMS_MAP.remove();
        }
    }


    private List<ConfigChangeLog> generateChangeLog(String requestId, ReplicaSet replicaSet, Map<String, Object> parameterMap) throws ApiException {
        List<ConfigChangeLog> changeLogs = new ArrayList<>();
        // Write to change log
        Map<String, String> originConfig = dBaasMetaService.getDefaultClient().listReplicaSetConfigs(
                requestId, replicaSet.getName(), null, null, null, null).getItems();

        String oldValue;
        String newValue;
        for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
            oldValue = originConfig.get(entry.getKey());
            newValue = entry.getValue().toString();

            ConfigChangeLog changelog = new ConfigChangeLog();
            changelog.setName(entry.getKey());
            changelog.setOldValue(oldValue);
            changelog.setNewValue(newValue);
            changelog.setIsApplied(false);
            changeLogs.add(changelog);
        }
        return changeLogs;
    }

    /**
     * 默认模板: performance_mode=safe, 主备双1 & 半同步复制
     * 异步模板: performance_mode=normal, 主库双1 & 备库innodb_flush_log_at_trx_commit=2 & sync_binlog=1000 & 异步复制
     * 高性能模板: performance_mode=high, 主备innodb_flush_log_at_trx_commit=2 & sync_binlog=1000 & 异步复制
     *
     * @param syncBinlogMode
     * @return
     */
    private Map<String, Object> getSyncBinlogParams(String syncBinlogMode) {
        if (!CustinsParamSupport.CUSTINS_PARAM_VALUE_SET_SYNC_BINLOG_MODE.contains(syncBinlogMode)) {
            return Collections.emptyMap();
        }
        if (StringUtils.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_BINLOG_MODE_HIGH, syncBinlogMode)) {
            return new HashMap<String, Object>() {{
                this.put("sync_binlog", "1000");
                this.put("innodb_flush_log_at_trx_commit", "2");
            }};
        }
        return new HashMap<String, Object>() {{
            this.put("sync_binlog", "1");
            this.put("innodb_flush_log_at_trx_commit", "1");
        }};
    }

    /**
     * 直接将is_applied=1 的数据 写入change_log表
     *
     * @param requestId
     * @param replicaSet
     * @param custinsId
     * @param paramName
     * @param newValue
     * @throws ApiException
     */
    private void writeAppliedChangeLog(String requestId, ReplicaSet replicaSet,
                                       Integer custinsId, String paramName, String newValue) throws ApiException {
        Map<String, String> originConfig = dBaasMetaService.getDefaultClient().listReplicaSetConfigs(requestId,
                replicaSet.getName(), null, null, null, null).getItems();
        String oldValue = originConfig.get(paramName);

        MycnfChangeLog changeLog = new MycnfChangeLog(custinsId, paramName, oldValue, newValue, null);
        changeLog.setIsApplied(1);
        mycnfService.createMycnfChangeLog(changeLog);
    }

    private Boolean isClusterTemplate(String paramGroupId) throws RdsException {
        if (StringUtils.isNotEmpty(paramGroupId)){
            String performanceInfo = SysParamGroupHelper.getPerformanceMode(paramGroupId);
            return StringUtils.isNotEmpty(performanceInfo) && performanceInfo.equalsIgnoreCase(CLUSTER_MGR);
        }
        return false;
    }

    private void checkReplicaSetResourceArch(String requestId, ReplicaSet replicaSet, Boolean isArm) throws ApiException, RdsException {
        logger.info("start checkReplicaSetResourceArch, replicaSetName: {}, isArm: {}", replicaSet.getName(), isArm);
        List<Replica> replicaList = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSet.getName(), null, null, null, null).getItems();
        for (Replica replica : replicaList) {
            String arch = ReplicaSetResourceRequest.ArchEnum.AMD64.toString();
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, replica.getId(), false);
            String hostName = replicaResource.getReplica().getHostName();
            Map<String, String> hostLabels = dBaasMetaService.getDefaultClient().listHostLabels(requestId, hostName);
            if (hostLabels.containsKey(PARAM_ARCH_LABEL)) {
                arch = hostLabels.get(PARAM_ARCH_LABEL);
            }
            logger.info("replica: {}, host: {}, host param arch label: {}", replica.getId(), hostName, arch);
            if (isArm == ReplicaSetResourceRequest.ArchEnum.ARM.toString().equalsIgnoreCase(arch)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_STORAGE_ENGINE);
            }
        }
    }

    private Object enableMgr(String requestId, ReplicaSet replicaSet) throws Exception {
        Object taskId = workFlowService.dispatchTask("custins", replicaSet.getName(), DOMAIN_MYSQL, "build_group_replication", new JSONObject().toJSONString(), 0);
        return taskId;
    }

}
