package com.aliyun.dba.poddefault.action.support;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.apsaradb.activityprovider.model.*;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreLiveMountBackupParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.response.backup.DescribeRestoreLiveMountBackupResponse;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.commonkindcode.support.ParamChecker;
import com.aliyun.dba.commonkindcode.support.ParamTransHelper;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.support.urd.URDZoneDescriptor;
import com.aliyun.dba.poddefault.action.support.urd.UniformResourceDescriptor;
import com.aliyun.dba.rdscustom.action.support.ECSActionConstant;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.support.common.ServerlessConstant;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.DateSupport;
import com.aliyun.dba.support.utils.SupportUtils;
import com.aliyun.dba.support.utils.Validator;
import com.aliyuncs.kms.model.v20160120.DescribeKeyResponse;
import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

import static com.aliyun.dba.base.support.MySQLParamConstants.*;
import static com.aliyun.dba.base.support.MysqlErrorCode.INVALID_TDDL_CLUSTER_NAME;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.ESSD_MIN_DISK_SIZE;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ParamConstants.*;

@Data
@Slf4j
public class PodCreateInsParam {
    private String bid;
    private String uid;
    private Integer userId;

    private String accessId;
    private String orderId;
    private String regionId;
    private String clusterName;

    private String dbInstanceName;
    private String dbType;
    private String dbVersion;
    private String portStr;
    private String dbName;
    private String timeZone;
    private String charSet;
    private String dbEngine;
    private String snapshotId;
    private String diskType;
    private String performanceLevel;
    private String classCode;
    private String resourceGroupId;
    private String paramGroupId;
    private String performanceCode;
    private String targetMinorVersion;
    private String autoUpgradeMinorVersion;
    private String rsTemplateName;
    private Integer diskSize;
    private Integer bakRetention;
    private String preferredBackupTime;
    private String preferredBackupPeriod;
    private String trafficPolicy;                // dbstack 专用，cluster or local，目前仅支持cluster
    private String sourceDBInstanceId;
    private CustInstanceDO sourceCustInstanceDO;

    private String vpcId = null;
    private String vswitchId = null;
    private String vpcInstanceId;
    private String connectionString = null;
    private String ipAddress = null;
    private String connType;
    private String tddlClusterName;
    private String tddlRegionConfig;
    private String instructionSetArch;
    private String description;

    //创建实例时的，指定创建的高权限账号和密码
    protected String superAccountName;
    protected String superAccountPassword;

    // 网络协议栈 目前仅适用于集团IPV6场景
    private InternetProtocolEnum netProtocol = InternetProtocolEnum.IPV4;

    //autoScale
    private String storageAutoScale;
    private String storageUpperBound;
    private String storageThreshold;

    private boolean isForMigrate;
    private boolean isTDDL;
    private boolean isPhysical;
    private boolean isK8sService;
    private boolean isDhg;
    private boolean isSingleNode;
    private boolean isPEngineBackupSet;

    //是否vbm形态,根据<region,class_code,uid>进行灰度
    private boolean isVbm;

    // 跨地域恢复
    private boolean isDisasterRestore;
    private String sourceDBInstanceName;
    private String sourceRegionId;
    private String restoreType;
    private String backupSetId;
    private String backupSetRegionId;
    private String restoreTime;
    private String consistentTime;

    private boolean isRestoreFromBackupSet = false;

    // 云盘加密
    private String encryptionKey;
    private String encryptionType;
    private String roleArn;

    // TDE
    private boolean tdeEnabled;
    private String tdeEncryptionKeyId;

    // CLS
    private String clsKeyMode;
    private String clsEncryptionKeyId;

    //serverless
    private ServerlessSpec serverlessSpec;

    //default runtime type
    private PodType podType = PodType.POD_RUNC;

    //autopl config
    private Long provisionedIops;

    private boolean burstingEnabled;

    //cold data config
    private boolean coldDataEnabled = false;
    private String coldSourceBakId = null;
    private String coldDataSnapshotId = null;
    private String coldDataJFSId = null;
    private String coldDataSourceInstanceName = null;

    //enable or disable initOptimizedWrites
    private boolean initOptimizedWrites;

    private Map<String, Object> parameterPersistCustinsParams;

    private ReplicaSet.BizTypeEnum bizType;
    private InstanceLevel instanceLevel;
    private AVZInfo avzInfo;
    private UniformResourceDescriptor urd;
    private IPWhiteList ipWhiteList;
    private IPWhiteList[] templateList;
    private int[] templateIdList;
    private Map<String, String> customMysqlParams;
    private ScheduleTemplate scheduleTemplate;
    private PodScheduleTemplate podScheduleTemplate;
    private ReplicaSet.InsTypeEnum insType;


    private AliyunInstanceDependency dependency;
    private Map<String, String> params;

    private Map<String, Boolean> initParams = new HashMap<>();
    private Map<String, Boolean> activities;

    private GeneralCloudDisk generalCloudDisk;

    private Boolean autoCreateProxy;

    // compression config
    private String compressionMode;
    private Double compressionRatio = 1.0;
    private Integer diskSizeGBBeforeCompression;

    // 原生复制
    private boolean externalReplication = false;

    public PodCreateInsParam(AliyunInstanceDependency dependency, Map<String, String> params) throws Exception {
        this.dependency = dependency;
        this.params = params;
    }

    public PodCreateInsParam setAutoUpgradeMinorVersion() throws RdsException {
        String upgradeMinorVersionOption;
        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION)) {
            upgradeMinorVersionOption = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.AUTO_UPGRADE_MINOR_VERSION);
            if (!ParamConstants.AUTO_UPGRADE_MINOR_VERSION_OPTIONS.contains(upgradeMinorVersionOption)) {
                throw new RdsException(ErrorCode.INVALID_ITEM_VALUE);
            }
        } else {
            upgradeMinorVersionOption = dependency.getResourceService().
                    getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).size() > 0 ?
                    dependency.getResourceService().getResourceRealValueList(ParamConstants.UPGRADE_MINOR_VERSION_OPTION).get(0) : "Auto";
        }
        autoUpgradeMinorVersion = upgradeMinorVersionOption;
        setParamInitDone("autoUpgradeMinorVersion");
        return this;
    }

    public PodCreateInsParam setServerlessInfo() throws RdsException {
        // 用户信息初始化
        serverlessSpec = new ServerlessSpec(params);
        if (serverlessSpec.getRcu() == null) {
            serverlessSpec.setRcu(serverlessSpec.getScaleMin());
        }

        // RCU range check: 0.5 <= RCU value <= 64 and is a multiple of 0.5
        if(!(0.5 <= serverlessSpec.getScaleMin() &&
                serverlessSpec.getScaleMin() <= serverlessSpec.getScaleMax() &&
                serverlessSpec.getScaleMax() <= 64 &&
                serverlessSpec.getScaleMin() % 0.5 == 0 &&
                serverlessSpec.getScaleMax() % 0.5 == 0)){
            throw new RdsException(ErrorCode.INVALID_PARAMETERS, "CreateDB: RCU range should be 0.5 ~ 64 and a multiple of 0.5");
        }

        setParamInitDone("serverlessInfo");
        return this;
    }

    public PodCreateInsParam setUserId() throws RdsException {
        // 用户信息初始化
        bid = dependency.getMysqlParamSupport().getAndCheckBID(params);
        uid = dependency.getMysqlParamSupport().getUID(params);

        // auto create User
        userId = dependency.getMysqlParamSupport().getAndCreateUserId(params);

        setParamInitDone("bid");
        setParamInitDone("uid");
        return this;
    }

    public PodCreateInsParam setInstanceName() throws Exception {
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        dbInstanceName = dependency.getMysqlParamSupport().getDBInstanceName(params);
        checkInstanceExisted(dependency.getDBaasMetaService().getDefaultClient(), requestId);
        setParamInitDone("dbInstanceName");
        return this;
    }

    public PodCreateInsParam setClusterName() {
        clusterName = dependency.getMysqlParamSupport().getParameterValue(params, CLUSTER_NAME);
        setParamInitDone("clusterName");
        return this;
    }

    public PodCreateInsParam setOrderId() {
        orderId = dependency.getMysqlParamSupport().getParameterValue(params, ORDERID);
        return this;
    }

    public PodCreateInsParam setAccessId() {
        accessId = dependency.getMysqlParamSupport().getParameterValue(params, ACCESS_ID);
        return this;
    }

    public PodCreateInsParam setDBType() throws RdsException {
        dbType = dependency.getMysqlParamSupport().getAndCheckDBType(params, null);
        setParamInitDone("dbType");
        return this;
    }

    public PodCreateInsParam setDBVersion() throws RdsException {
        dbVersion = dependency.getMysqlParamSupport().getAndCheckDBVersion(params, dbType, true);
        setParamInitDone("dbVersion");
        return this;
    }

    public PodCreateInsParam setPortStr() throws RdsException {
        portStr = CustinsSupport.getConnPort(getParameterValue(params, ParamConstants.PORT), dbType);
        setParamInitDone("portStr");
        return this;
    }

    public PodCreateInsParam setDBName() throws RdsException {
        dbName = dependency.getMysqlParamSupport().getDBName(params);
        setParamInitDone("dbName");
        return this;
    }

    public PodCreateInsParam setTimezone() throws RdsException {
        timeZone = dependency.getMysqlParamSupport().hasParameter(params, TIME_ZONE)
                ? getParameterValue(params, TIME_ZONE) : getParameterValue(params, "DBTimeZone");
        setParamInitDone("timeZone");
        return this;
    }

    public PodCreateInsParam setCharSet() throws Exception {
        this.checkDepend("dbType").checkDepend("dbVersion");
        charSet = dependency.getMysqlParamSupport().getAndCheckCharacterSetName(params, dbType, dbVersion);
        setParamInitDone("charSet");
        return this;
    }

    public PodCreateInsParam setDisasterRestore() throws Exception {
        String isDisaster = getParameterValue(params, "IsDisasterRestore");
        if ("1".equals(isDisaster)) {
            isDisasterRestore = true;
            isRestoreFromBackupSet = true;
            sourceDBInstanceName = getParameterValue(params, ParamConstants.SOURCE_DB_INSTANCE_NAME);
            sourceRegionId = getParameterValue(params, PodDefaultConstants.PARAM_SOURCE_REGION);
            restoreType = getParameterValue(params, ParamConstants.RESTORE_TYPE);
            backupSetId = getParameterValue(params, ParamConstants.BACKUP_SET_ID);
            backupSetRegionId = getParameterValue(params, PodDefaultConstants.PARAM_BACKUP_SET_REGION);
            snapshotId = getParameterValue(params, PodDefaultConstants.PARAM_SNAPSHOT_ID);
            restoreTime = getParameterValue(params, ParamConstants.RESTORE_TIME);
            consistentTime = getParameterValue(params, PodDefaultConstants.PARAM_CONSISTENT_TIME);
        }
        setParamInitDone("isDisasterRestore");
        return this;
    }

    public PodCreateInsParam setSnapshotId() throws Exception {
        // if sourceDBInstanceId
        this.checkDepend("bid").checkDepend("uid").checkDepend("dbType").checkDepend("dbVersion").checkDepend("regionId");
        snapshotId = getSnapshotIdIfNeed(dependency, params);
        setParamInitDone("snapshotId");
        setParamInitDone("isPEngineBackupSet");
        return this;
    }

    public PodCreateInsParam setDBEngine() throws RdsException {
        String inputDBEngine = dependency.getMysqlParamSupport().getAndCheckDBType(params, null);
        if (StringUtils.equalsIgnoreCase(inputDBEngine, "MySQL")) {
            dbEngine = dependency.getMysqlParamSupport().isMysqlXDBByParams(params) ? "XDB" : "MySQL";
        } else {
            dbEngine = inputDBEngine;
        }
        setParamInitDone("dbEngine");
        return this;
    }

    public PodCreateInsParam setConnType() throws RdsException {
        connType = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.DB_INSTANCE_CONN_TYPE, CustinsSupport.CONN_TYPE_LVS);
        if (StringUtils.isNotBlank(tddlClusterName)) {
            connType = CONN_TYPE_TDDL;
        }

        if ("k8sService".equalsIgnoreCase(connType)) {
            // DBStack环境特有参数，公共云可忽略
            trafficPolicy = "Cluster";
            isK8sService = true;
        }

        setParamInitDone("connType");
        return this;
    }

    public PodCreateInsParam setAccount() throws Exception {
        // 读取super账号信息
        superAccountName = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.SUPER_ACCOUNT_NAME);
        String encryptSuperAccountPassword = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.ENCRYPT_SUPER_ACCOUNT_PASSWORD);

        //其中有一个值为空，则不进行super账号创建
        if (StringUtils.isAnyBlank(superAccountName, encryptSuperAccountPassword)) {
            setParamInitDone("superAccountName");
            setParamInitDone("superAccountPassword");
            return this;
        }
        //后面需要根据数据库版本来判断账号是否合法，需要先确认相关参数有值
        this.checkDepend("dbType").checkDepend("dbVersion");

        //对密码进行解密，并对账号名和密码的合法性进行check
        //如果这两步执行异常，抛出错误，创建接口报错
        superAccountName = dependency.getParameterHelper().checkAccountName(superAccountName, this.getDbVersion());
        superAccountPassword = dependency.getParameterHelper().getAndCheckSuperAccountPassword();

        setParamInitDone("superAccountName");
        setParamInitDone("superAccountPassword");
        return this;
    }

    public PodCreateInsParam setBizType() throws Exception {
        checkDepend("regionId");
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        bizType = dependency.getPodParameterHelper().getBizType(requestId, regionId);
        if (StringUtils.isNotBlank(tddlClusterName)) {
            bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
        }
        setParamInitDone("bizType");
        return this;
    }

    public PodCreateInsParam setTddlClusterName() throws RdsException {
        tddlClusterName = dependency.getMysqlParamSupport().getParameterValue(params, "TddlClusterName");
        if (StringUtils.isNotBlank(tddlClusterName)) {
            connType = CONN_TYPE_TDDL;
            bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
        }
        setParamInitDone("tddlClusterName");
        return this;
    }

    public PodCreateInsParam setTddlRegionConfig() throws RdsException {
        tddlRegionConfig = dependency.getMysqlParamSupport().getParameterValue(params, "TddlRegionConfig");
        setParamInitDone("tddlRegionConfig");
        return this;
    }

    public PodCreateInsParam setInstructionSetArch() throws RdsException {
        instructionSetArch = dependency.getMysqlParamSupport().getParameterValue(params, "InstructionSetArch");
        if (Objects.isNull(instructionSetArch)) {
            if (PodCommonSupport.isArm(instanceLevel)) {
                instructionSetArch = CreateReplicaSetDto.ArchEnum.ARM.name();
            } else if (PodCommonSupport.isX86HG(instanceLevel)) {
                instructionSetArch = X86HG_ARCH;
            }
        }
        setParamInitDone("instructionSetArch");
        return this;
    }

    public PodCreateInsParam setIsForMigrate() throws RdsException {
        isForMigrate = "true".equalsIgnoreCase(dependency.getMysqlParamSupport().getParameterValue(params, "ForMigrate", "false"));
        setParamInitDone("isForMigrate");
        return this;
    }

    public PodCreateInsParam setIsTDDL() throws Exception {
        this.checkDepend("connType");
        isTDDL = CONN_TYPE_TDDL.equalsIgnoreCase(connType);
        if (isTDDL) {
            this.checkDepend("dbName")
                    .checkDepend("tddlClusterName")
                    .checkDepend("isForMigrate")
                    .checkDepend("dbEngine")
                    .checkDepend("timeZone")
                    .checkDepend("tddlRegionConfig")
                    .checkDepend("regionId")
                    .checkDepend("clusterName");
            tddlClusterName = reFormatTddlClusterName(dbName, tddlClusterName, isForMigrate);
            aliGroupParamValidate(dbName, timeZone, tddlRegionConfig);
            setParamInitDone("tddlClusterName");
            // XDB如果只传了一个机房，需要补充机房
            dependency.getReplicaSetService().mazPreCheckForXDB(params, dbEngine);
            clusterName = clusterName != null ? clusterName : String.format(ALIGROU_DHG_PARTERN, regionId);
            setParamInitDone("clusterName");
        }
        setParamInitDone("isTDDL");
        return this;
    }

    public PodCreateInsParam setIsPhysical() throws Exception {
        this.checkDepend("connType");
        isPhysical = CONN_TYPE_PHYSICAL.equals(connType);
        setParamInitDone("isPhysical");
        return this;
    }

    public PodCreateInsParam setIsDHG() throws Exception {
        this.checkDepend("clusterName");
        isDhg = dependency.getMysqlParamSupport().isDHGCluster(clusterName);
        setParamInitDone("isDhg");
        return this;
    }

    public PodCreateInsParam setAvzInfo() throws Exception {
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        this.checkDepend("setDispenseModeForce");
        avzInfo = dependency.getAvzSupport().getAVZInfo(params);
        log.info("avzInfo: {}", JSONObject.toJSONString(avzInfo));
        checkValidDispenseMode(avzInfo);
        dependency.getAvzSupport().validateCloudBoxZone(requestId, avzInfo);
        setParamInitDone("avzInfo");
        return this;
    }

    public PodCreateInsParam initVbmConfig() throws Exception {
        this.checkDepend("uid").checkDepend("instanceLevel").checkDepend("podType");
        isVbm = podType == PodType.POD_VBM_RUND;
        setParamInitDone("isVbm");
        return this;
    }

    public PodCreateInsParam setURD() throws Exception {
        this.checkDepend("avzInfo").checkDepend("instanceLevel");
        Replica.RoleEnum[] roleEnums = PodCommonSupport.getRoles(dbEngine, instanceLevel, false, avzInfo);
        urd = new UniformResourceDescriptor(instanceLevel, avzInfo, Arrays.asList(roleEnums));
        if (dependency.getPodParameterHelper().hasParameter("customResourceDesc")) {
            urd = new UniformResourceDescriptor();
            String customResourceDesc = dependency.getPodParameterHelper().getParameterValue("customResourceDesc");
            log.info("customResourceDesc: {}", customResourceDesc);
            List<URDZoneDescriptor> zoneDescriptorList = URDZoneDescriptor.parseFromJSON(customResourceDesc);
            if (zoneDescriptorList.isEmpty()) {
                throw new RdsException(ErrorCode.INVALID_PARAM);
            }
            zoneDescriptorList.forEach(zoneDescriptor ->
                    zoneDescriptor.getInstances().forEach((role, count) ->
                            urd.addInstance(zoneDescriptor.getRegion(),
                                    // 这里忽略region权重，权重以机房权重为准
                                    zoneDescriptor.getRegionId(), 0,
                                    zoneDescriptor.getZoneId(),
                                    zoneDescriptor.getZoneWeight(),
                                    role, count)));
        }

        log.info("UniformResourceDescriptor: {}", JSONObject.toJSONString(urd.getZoneList()));
        setParamInitDone("urd");
        return this;
    }

    public PodCreateInsParam setInsTypeDesc(ReplicaSet.InsTypeEnum insType) throws Exception {
        this.insType = insType;
        setParamInitDone("insType");
        return this;
    }


    public PodCreateInsParam setDispenseModeForce() throws Exception {
        this.checkDepend("instanceLevel").checkDepend("dbEngine");
        dependency.getPodParameterHelper().resetDispenseMode(params, instanceLevel, dbEngine, false);
        setParamInitDone("setDispenseModeForce");
        return this;
    }

    public PodCreateInsParam setRegionId() throws Exception {
        this.checkDepend("avzInfo");
        regionId = avzInfo.getRegionId();
        setParamInitDone("regionId");
        return this;
    }

    public PodCreateInsParam setVpcId() throws Exception {
        this.checkDepend("isPhysical").checkDepend("isTDDL");
        if (isIgnoreVPC()) {
            setParamInitDone("vpcId");
            return this;
        }
        regionId = avzInfo.getRegionId();
        vpcId = CheckUtils.checkValidForVPCId(getParameterValue(params, ParamConstants.VPC_ID));
        setParamInitDone("vpcId");
        return this;
    }

    public PodCreateInsParam setVswitchId() throws Exception {
        this.checkDepend("isPhysical").checkDepend("isTDDL");
        if (isIgnoreVPC()) {
            setParamInitDone("vswitchId");
            return this;
        }
        regionId = avzInfo.getRegionId();
        vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(params, ParamConstants.VSWITCH_ID));
        setParamInitDone("vswitchId");
        return this;
    }

    public PodCreateInsParam setIpAddress() throws Exception {
        ipAddress = dependency.getPodParameterHelper().getParameterValue(ParamConstants.IP_ADDRESS);
        if (StringUtils.isNotBlank(ipAddress) && !Validator.isIp(ipAddress)) {
            throw new RdsException(ErrorCode.INVALID_IP); //校验IP的格式
        }
        setParamInitDone("ipAddress");
        return this;
    }

    public PodCreateInsParam setVpcInstanceId() throws Exception {
        vpcInstanceId = dependency.getPodParameterHelper().getParameterValue(ParamConstants.VPC_INSTANCE_ID);
        setParamInitDone("vpcInstanceId");
        return this;
    }

    public PodCreateInsParam setConnectionString() throws Exception {
        this.checkDepend("isPhysical").checkDepend("isTDDL");
        if (isIgnoreVPC()) {
            setParamInitDone("connectionString");
            return this;
        }
        regionId = avzInfo.getRegionId();
        connectionString = CheckUtils.checkValidForConnAddrCust(getParameterValue(params, ParamConstants.CONNECTION_STRING));
        setParamInitDone("connectionString");

        String sourceConnectionString = dependency.getMysqlParamSupport().getParameterValue(params, "SourceConnectionString");
        if (StringUtils.isNotEmpty(sourceConnectionString)) {
            String sourceDBInstanceId = dependency.getMysqlParamSupport()
                    .getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);
            if (StringUtils.isEmpty(sourceDBInstanceId)) {
                return this;
            }
            //回收站场景，获取保留的连接串
            String retainConnAddrString = dependency.getConnAddrService().getRetainSourceConnAddressIfNeeded(params);
            if (StringUtils.isNotEmpty(retainConnAddrString)) {
                this.connectionString = retainConnAddrString;
            }
        }
        return this;
    }

    public PodCreateInsParam setClassCode() throws Exception {
        classCode = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.DB_INSTANCE_CLASS);
        setParamInitDone("classCode");
        return this;
    }

    public PodCreateInsParam setInstanceLevel() throws Exception {
        this.checkDepend("dbType").checkDepend("dbVersion").checkDepend("classCode");
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        instanceLevel = dependency.getDBaasMetaService().getDefaultClient()
                .getInstanceLevel(requestId, dbType, dbVersion, classCode, null);
        setParamInitDone("instanceLevel");
        return this;
    }

    public PodCreateInsParam setDiskType() throws Exception {
        this.checkDepend("instanceLevel").checkDepend("diskSize");
        diskType = checkValidDiskType(dependency.getPodParameterHelper()
                .getDiskType(instanceLevel));
        PodParameterHelper.checkCloudEssdStorageValid(diskType, diskSize);
        performanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(diskType);
        diskType = PodParameterHelper.transferDiskTypeParam(diskType);
        if (Replica.StorageTypeEnum.LOCAL_SSD.toString().equals(diskType)
                && DockerOnEcsConstants.HOST_TYPE_ECS.equals(getInstanceLevel().getHostType())) {
            throw new RdsException(new Object[]{400, "InvalidInstanceLevel.DiskType", "Specified instance level not support request disk type"});
        }
        setParamInitDone("diskType");
        return this;
    }

    public PodCreateInsParam setDiskSize() throws Exception {
        Integer defaultDiskSize = null;
        if (instanceLevel != null) {
            defaultDiskSize = this.instanceLevel.getDiskSizeMB() != null ? this.instanceLevel.getDiskSizeMB() / 1024 : null;
        }
        diskSize = CheckUtils.parseInt(dependency.getMysqlParamSupport()
                        .getParameterValue(params, ParamConstants.STORAGE, defaultDiskSize),
                CustinsSupport.ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
        if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
            // save diskSize before compression
            diskSizeGBBeforeCompression = diskSize;
            // modify  diskSize
            diskSize = CheckUtils.parseInt(
                    String.valueOf(CloudDiskCompressionHelper.getLogicalSize(diskSize, compressionRatio)),
                    ESSD_MIN_DISK_SIZE, 64000, ErrorCode.INVALID_STORAGE);
        }
        setParamInitDone("diskSize");
        return this;
    }

    public PodCreateInsParam setCompressionMode() throws Exception {
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        String paramCompressionMode = dependency.getMysqlParamSupport().getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_MODE);
        compressionMode = dependency.getCloudDiskCompressionHelper().getCompressionMode(requestId, null, paramCompressionMode);
        setParamInitDone("compressionMode");
        if (CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
            String paramCompressionRatio = dependency.getMysqlParamSupport().getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_RATIO);
            compressionRatio = dependency.getCloudDiskCompressionHelper().getCompressionRatio(requestId, null, paramCompressionRatio);
        }
        return this;
    }

    public PodCreateInsParam setIsSingleNode() throws Exception {
        this.checkDepend("instanceLevel");
        isSingleNode = MysqlParamSupport.isSingleNode(instanceLevel);
        setParamInitDone("isSingleNode");
        return this;
    }

    public PodCreateInsParam setResourceGroupId() throws Exception {
        resourceGroupId = getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "").trim();
        setParamInitDone("resourceGroupId");
        return this;
    }

    public PodCreateInsParam setTargetMinorVersion() throws Exception {
        targetMinorVersion = dependency.getMysqlParamSupport().getParameterValue(params, "TargetMinorVersion");
        setParamInitDone("targetMinorVersion");
        return this;
    }

    public PodCreateInsParam setParamGroupId() throws Exception {
        paramGroupId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.DB_PARAM_GROUP_ID);

        if (StringUtils.isNotBlank(paramGroupId)) {
            // 参数模板信息检查
            SysParamGroupHelper.sysParamGroupIdValidation(paramGroupId, dbType, dbVersion, "", "");
            Map paramGroup = dependency.getParameterGroupTemplateGenerator().paramGroupMatchValidate(dbType, dbVersion, "", paramGroupId, false);
            boolean isSys = StringUtils.startsWith(paramGroupId, ParamTransHelper.SYS_PARAM_GROUP_ID_PREFIX);

            //用户参数模版包含sync_binlog，需要持久化到custins_params表
            if (!isSys) {
                long pid = Long.parseLong(paramGroup.get("ParameterGroupId").toString());
                Map<String, Object> parameterMapGroupDetail = ParamChecker.getParameterGroupDetail(pid, dependency.getCustinsParamGroupsService());
                if (!parameterMapGroupDetail.isEmpty() && parameterMapGroupDetail.containsKey("sync_binlog")) {
                    parameterPersistCustinsParams = new HashMap<String, Object>(){{
                        this.put("sync_binlog", parameterMapGroupDetail.get("sync_binlog"));
                    }};
                }
            }
            performanceCode = SysParamGroupHelper.getPerformanceMode(paramGroupId);
        }

        if (dependency.getReplicaSetService().isServerless(instanceLevel) &&
                dependency.getPodCommonSupport().isXEngine(paramGroupId)) {
            throw new RdsException(ErrorCode.UNSUPPORTED_STORAGE_ENGINE);
        }
        setParamInitDone("paramGroupId");
        return this;
    }

    public PodCreateInsParam setBakRetention() throws Exception {
        bakRetention = CheckUtils.parseInt(
                getParameterValue(params, ParamConstants.BACKUP_RETENTION, 7),
                1, 730, ErrorCode.INVALID_BACKUPRETENTIONPERIOD);
        setParamInitDone("bakRetention");
        return this;
    }

    public PodCreateInsParam setDescription() throws Exception {
        String descriptionInput = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.DB_INSTANCE_DESCRIPTION);
        if (StringUtils.isNotBlank(descriptionInput)) {
            description = CheckUtils.checkLength(SupportUtils.decode(descriptionInput), 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        }
        return this;
    }

    public PodCreateInsParam setPreferredBackupTime() throws Exception {
        preferredBackupTime = getParameterValue(params, "PreferredBackupTime");
        if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
            throw new RdsException(ErrorCode.INVALID_PREFERREDBACKUPTIME);
        }
        setParamInitDone("preferredBackupTime");
        return this;
    }

    public PodCreateInsParam setPreferredBackupPeriod() throws Exception {
        preferredBackupPeriod = CheckUtils.checkValidForBackupPeriod(getParameterValue(params, "preferredbackupperiod"));
        setParamInitDone("preferredBackupPeriod");
        return this;
    }

    public PodCreateInsParam setExternalReplication() throws Exception {
        this.checkDepend("dbType").checkDepend("dbVersion").checkDepend("instanceLevel");
        externalReplication = Boolean.parseBoolean(getParameterValue(params, "ExternalReplication"));
        if (externalReplication) {
            // only support MySQL 5.7 basic ins
            if (!CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(dbType)) {
                throw new RdsException(ErrorCode.INVALID_PARAM_DB_TYPE);
            } else if (!CustinsSupport.DB_VERSION_MYSQL_57.equals(dbVersion)) {
                throw new RdsException(ErrorCode.INVALID_PARAM_DB_VERSION);
            } else if (!CustinsSupport.BASIC_LEVEL.equalsIgnoreCase(instanceLevel.getCategory().getValue())) {
                throw new RdsException(ErrorCode.INVALID_PARAM_CATEGORY);
            }
        }
        this.setParamInitDone("externalReplication");
        return this;
    }

    public PodCreateInsParam setPodType() throws Exception {
        this.checkDepend("uid").checkDepend("regionId").checkDepend("instanceLevel").checkDepend("avzInfo").checkDepend("externalReplication");
        if (!PodParameterHelper.isAliGroup(bizType)) {
            this.podType = externalReplication ? PodType.POD_VBM_RUND : dependency.getRundPodSupport().getPodTypeByGrayConfig(regionId, uid, instanceLevel, avzInfo);
            if (podType == PodType.POD_VBM_RUND) {
                dependency.getPodParameterHelper().fixAvailableZoneInfo(avzInfo);
            }
        }
        this.setParamInitDone("podType");
        return this;
    }

    /**
     * 初始化从CDM恢复的参数
     * CDM恢复的备份集ID都是以"dbs-cdm-"开头的字符串
     */
    public PodCreateInsParam setRecoverFromCDM() throws Exception {
        this.checkDepend("bid").checkDepend("uid");
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID, null);
        if (bakId != null && bakId.startsWith(MySQLParamConstants.CDM_PREFIX)) {  // 以 "dbs-cdm-" 开头表示从CDM恢复
            backupSetId = bakId;
            DescribeRestoreLiveMountBackupResponse backupSetInfo;
            try {
                backupSetInfo = dependency.getDbsGateWayService().describeRestoreLiveMountBackup(
                        DescribeRestoreLiveMountBackupParam.builder()
                                .userId(uid)
                                .callerBid(bid)
                                .backupId(bakId)
                                .regionCode(regionId)
                                .build()
                );
            } catch (BaseServiceException ex) {
                log.error("GetBackupSet failed: ", ex);
                if (StringUtils.equalsIgnoreCase(ex.getCode(), "InvalidBackupSetID.NotFound")) {
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                }
                throw ex;
            }
            if (backupSetInfo != null) {
                snapshotId = backupSetInfo.getBackupSetInfo().getExtraInfo().getSnapshotId();
                restoreType = "0";  // cdm恢复可以看做是从备份集恢复
                setParamInitDone("snapshotId");
                setParamInitDone("restoreType");
            } else {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
        }
        return this;
    }

    // 网络设置 IPv4 / IPv6 (暂不支持) / IPv4IPv6 双栈
    public PodCreateInsParam setNetProtocol() throws Exception {
        String netProtocol = dependency.getMysqlParamSupport().getParameterValue(params, "NetProtocol", "IPv4");
        if (PodParameterHelper.isAliGroup(bizType)) {
            switch (netProtocol) {
                case "IPv4":
                    this.netProtocol = InternetProtocolEnum.IPV4;
                    break;
                case "IPv6":
                    this.netProtocol = InternetProtocolEnum.IPV6;
                    break;
                case "IPv4IPv6":
                    this.netProtocol = InternetProtocolEnum.IPV4IPV6;
                    break;
                default:
                    throw new IllegalArgumentException("unsupported internet protocol: " + netProtocol);
            }
        }
        setParamInitDone("NetProtocol");
        return this;
    }

    public PodCreateInsParam setRsTemplate() throws Exception {
        this.checkDepend("bid").checkDepend("uid").checkDepend("classCode").checkDepend("bizType");
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        rsTemplateName = dependency.getMysqlParamSupport().getParameterValue(params, "RsTemplateName");
        if (PodParameterHelper.isAliGroup(bizType)) {
            // 集团使用用户调度模板
            if (StringUtils.isNotBlank(tddlClusterName)) {
                String hack128VolumeTemplcat = dependency.getCacheService().getValueOrDefault("ALIGROUP_128_VOLUME_SWITCH", "off");
                //新建实例分配128块盘
                if (hack128VolumeTemplcat.equalsIgnoreCase("on")) {
                    rsTemplateName = PodDefaultConstants.TEMPLATE_ALIGROUP_128_VOLUME;
                } else {
                    rsTemplateName = PodDefaultConstants.RS_TEMPLATE_USER_PREFIX + tddlClusterName;
                }
                podScheduleTemplate = dependency.getPodTemplateHelper()
                        .getPodTemplateByRsTemplateNameAndLoginId(requestId, rsTemplateName, bid + "_" + uid);
                if (podScheduleTemplate != null) {
                    scheduleTemplate = dependency.getPodTemplateHelper().getReplicaSetScheduleTemplate(podScheduleTemplate);
                }
            }
        } else if (!dependency.getPodCommonSupport().isCloudDiskForIoAcceleration(generalCloudDisk)
                && dependency.getPodCommonSupport().isIoAccelerationEnabled(generalCloudDisk)) {
            //云上使用系统调度模板（for IO 加速)
            Pair<String, ScheduleTemplate> scheduleTemplatePair = dependency
                    .getPodTemplateHelper()
                    .getBizSysScheduleTemplate(
                            podType,
                            bizType,
                            dbEngine,
                            instanceLevel,
                            isSingleTenant(),
                            Objects.isNull(insType) ? null : insType.getValue(),
                            dbInstanceName,
                            null,
                            dependency.getMysqlParamSupport().getUID(params));
            rsTemplateName = scheduleTemplatePair.getKey();
            scheduleTemplate = scheduleTemplatePair.getValue();
        } else {
            //云上使用系统调度模板
            Pair<String, ScheduleTemplate> scheduleTemplatePair = dependency
                    .getPodTemplateHelper()
                    .getBizSysScheduleTemplate(
                            podType,
                            bizType,
                            dbEngine,
                            instanceLevel,
                            isSingleTenant(),
                            Objects.isNull(insType) ? null : insType.getValue(),
                            dbInstanceName, null, dependency.getMysqlParamSupport().getUID(params));
            rsTemplateName = scheduleTemplatePair.getKey();
            scheduleTemplate = scheduleTemplatePair.getValue();
        }

        setParamInitDone("rsTemplateName");
        setParamInitDone("scheduleTemplate");
        return this;
    }

    public PodCreateInsParam setCustomMysqlParams() throws Exception {
        this.checkDepend("paramGroupId").checkDepend("dbType")
                .checkDepend("dbVersion").checkDepend("instanceLevel");
        customMysqlParams = dependency.getMysqlParamSupport().getAndCheckMysqlCustomParams(params);
        if(instanceLevel.getCategory().getValue().equalsIgnoreCase(ServerlessConstant.SERVERLESS_BASIC)){
            SysParamGroupHelper.sysParamGroupIdValidation(
                    paramGroupId, dbType, dbVersion, ServerlessConstant.SERVERLESS_BASIC_PARAMS_GROUP_CATEGORY, "");
            setParamInitDone("customMysqlParams");
            return this;
        }


        if(instanceLevel.getCategory().getValue().equalsIgnoreCase(ServerlessConstant.SERVERLESS_STANDARD)){
            SysParamGroupHelper.sysParamGroupIdValidation(
                    paramGroupId, dbType, dbVersion, ServerlessConstant.SERVERLESS_STANDARD_PARAMS_GROUP_CATEGORY, "");
            setParamInitDone("customMysqlParams");
            return this;
        }

        SysParamGroupHelper.sysParamGroupIdValidation(
                paramGroupId, dbType, dbVersion,
                Objects.requireNonNull(instanceLevel.getCategory()).toString(), "");
        setParamInitDone("customMysqlParams");
        return this;
    }

    public PodCreateInsParam setIPWhiteList() throws Exception {
        this.checkDepend("isPhysical").checkDepend("isTDDL");
        if (isIgnoreVPC()) {
            // physical链路白名单固定设置
            params.put(ParamConstants.SECURITY_IP_LIST.toLowerCase(), "0.0.0.0/0");
        }
        ipWhiteList = getAndCheckIpWhiteList();
        setParamInitDone("ipWhiteList");
        return this;
    }
    public PodCreateInsParam setWhitelistTemplateList() throws Exception {
        this.checkDepend("isPhysical").checkDepend("isTDDL");
        templateList = dependency.getPodParameterHelper().getAndCheckTemplateList(this.getUserId());
        setParamInitDone("whitelistTemplateList");
        return this;
    }
    public PodCreateInsParam setTemplateIdList() throws Exception {
        this.checkDepend("whitelistTemplateList");
        try {
            templateIdList = dependency.getPodParameterHelper().getAndCheckTemplateIdList();
            if(templateIdList !=null && templateIdList.length!=templateList.length){
                throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
            }
            setParamInitDone("whitelistTemplateList");
        }catch (RdsException re){
            throw new RdsException(ErrorCode.INVALID_WHITELIST_TEMPLATE_ID);
        }
        return this;
    }

    public PodCreateInsParam setStorageAutoScale() throws Exception {
        this.storageAutoScale = dependency.getMysqlParamSupport().getParameterValue(params, DockerOnEcsConstants.STORAGE_AUTO_SCALE);
        this.storageUpperBound = dependency.getMysqlParamSupport().getParameterValue(params, DockerOnEcsConstants.STORAGE_UPPER_BOUND);
        this.storageThreshold = dependency.getMysqlParamSupport().getParameterValue(params, DockerOnEcsConstants.STORAGE_THRESHOLD);
        setParamInitDone("storageAutoScale");
        return this;
    }

    public PodCreateInsParam setServerlessStorageAutoScale() throws Exception {
        this.storageAutoScale = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_AUTO_SCALE);
        this.storageUpperBound = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_UPPER_BOND);
        this.storageThreshold = String.valueOf(ServerlessConstant.SERVERLESS_STORAGE_THRESHOLD);
        setParamInitDone("storageAutoScale");
        return this;
    }

    public PodCreateInsParam resetBizTypeAndDiskTypeForAliGroup() throws Exception {
        this.checkDepend("clusterName").checkDepend("urd");
        if (dependency.getAligroupService().isAligroupDHG(urd.getMasterZoneDesc().getRegionId(), clusterName)) {
            // 大客户主机组为集团的，就是集团业务
            bizType = ReplicaSet.BizTypeEnum.ALIGROUP;
            // FIXME: 集团实例的数据节点磁盘类型应该与规格一致，否则计量计费会有坑
            diskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE, "local_ssd");
            setParamInitDone("bizType");
            setParamInitDone("diskType");
        }
        return this;
    }

    public PodCreateInsParam setAutoCreateProxy() throws Exception {
        this.checkDepend("instanceLevel");
        autoCreateProxy = dependency.getPodParameterHelper().getAutoCreateProxy(instanceLevel);
        return this;
    }

    // 根据生成的参数判断是否使用PFS
    public boolean isUsePfs() throws Exception {
        this.checkDepend("bizType").checkDepend("diskType").checkDepend("isSingleNode");
        //目前集团业务的云盘使用pfs
        return PodParameterHelper.isAliGroup(bizType)
                && dependency.getReplicaSetService().isStorageTypeCloudDisk(diskType)
                && !isSingleNode;
    }

    // 根据生成的参数判断是否是单租户
    public boolean isSingleTenant() throws Exception {
        this.checkDepend("diskType").checkDepend("instanceLevel").checkDepend("isDhg").checkDepend("bizType");
        //目前集团业务的云盘使用pfs
        return dependency.getReplicaSetService().isCloudSingleTenant(bizType, diskType, instanceLevel, isDhg);
    }

//    public ScheduleTemplate getScheduleTemplate() throws Exception {
//        this.checkDepend("podScheduleTemplate");
//        return podScheduleTemplate == null ? null
//                : dependency.getPodTemplateHelper().getReplicaSetScheduleTemplate(podScheduleTemplate);
//    }

    public String getDHGHostGroupId() throws Exception {
        this.checkDepend("clusterName").checkDepend("isDhg");
        if (StringUtils.isNotBlank(clusterName) && isDhg) {
            return clusterName;
        }
        return null;
    }

    public String getDedicatedBizGroup() throws Exception {
        this.checkDepend("tddlClusterName");
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);
        if (StringUtils.isNotEmpty(tddlClusterName) && dependency.getAligroupService().isTddlClusterNeedAllocateDedicatedResourceGroup(requestId, tddlClusterName)) {
            return tddlClusterName;
        }
        return null;
    }

    public PodCreateInsParam setEncryptionKey() throws Exception {
        this.checkDepend("roleArn").checkDepend("uid").checkDepend("regionId");
        encryptionKey = dependency.getMysqlParamSupport().getParameterValue(params, "EncryptionKey", null);
        // encryptionType是Common Provider那边使用，需要配置云盘加密时，固定配置成该值
        String region = dependency.getMysqlParamSupport().getParameterValue(params, "region", null);
        if (StringUtils.isBlank(region)) {
            region = dependency.getMysqlParamSupport().getParameterValue(params, "SubDomain",null);;
        }
        if (StringUtils.isNotEmpty(roleArn)) {
            boolean isSingletenant = this.isSingleTenant();
            //判断云盘加密使用的key是否为自定义密钥，多租户不能使用自定义密钥
            dependency.getMysqlEncryptionService().checkEncryptionKeyForNewCustins(null,params,isSingletenant);
            if (StringUtils.isEmpty(encryptionKey)) {
                if (StringUtils.isBlank(clusterName)) {
                    List<String> clusterNameList = dependency.getClusterIDao().getClusterNamesByRegion(region, "global", true);
                    if (clusterNameList.isEmpty()) {
                        throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
                    }
                    clusterName = clusterNameList.get(0);
                }
                encryptionKey = dependency.getMysqlEncryptionService().getServiceKey(clusterName, uid, userId);
            }
            dependency.getCloudSSDEncryptionService().checkByokKeyAvail(regionId, roleArn, encryptionKey, uid);
            encryptionType = PodDefaultConstants.ENCRYPTION_CLOUD_DISK_TYPE;
        } else {
            encryptionType = null;
        }
        setParamInitDone("encryptionKey");
        setParamInitDone("encryptionType");
        return this;
    }

    public PodCreateInsParam setRoleArn() throws Exception {
        roleArn = dependency.getMysqlParamSupport().getParameterValue(params, "RoleArn", null);
        setParamInitDone("roleArn");
        return this;
    }

    public PodCreateInsParam setAutoPLConfig() throws Exception {
        /*
         * autopl config parameter，provisionedIops and burstingEnabled
         */
        this.checkDepend("diskType").checkDepend("diskSize").checkDepend("uid");
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        String paramProvisionedIops = getParameterValue(params, ParamConstants.AUTOPL_PROVISIONED_IOPS);
        String paramBurstingEnabled = getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
        // replicaSetName parameter is blank, skip this priority
        provisionedIops = dependency.getReplicaSetService().getAutoConfigProvisionedIops(requestId, "", diskType, paramProvisionedIops, diskSize, uid);
        burstingEnabled = dependency.getReplicaSetService().getAutoConfigBurstingEnabled(requestId, "", diskType, paramBurstingEnabled);

        setParamInitDone("provisionedIops");
        setParamInitDone("burstingEnabled");
        return this;
    }

    public PodCreateInsParam setInitOptimizedWrites() throws Exception {
        if (isRestoreFromBackupSet) {
            // restore from backup set, skip this priority
            log.info("restore from backup set, skip this priority");
            return this;
        }
        this.checkDepend("dbVersion").checkDepend("diskType");
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        // When creating an instance, the init Optimized Writes parameter value is optimized Writes
        initOptimizedWrites = dependency.getPodCommonSupport().getOptimizedWrites(requestId, params, dbVersion, diskType, null, null);
        log.info("requestId : {} , initOptimizedWrites : {}", requestId, initOptimizedWrites);
        setParamInitDone("initOptimizedWrites");
        return this;
    }

    public PodCreateInsParam setColdDataConfig() throws Exception {
        String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        coldDataEnabled = (coldDataEnabled || dependency.getReplicaSetService().getColdDataEnabled(requestId, "", paramColdDataEnabled));
        setParamInitDone("coldDataEnabled");
        return this;
    }

    private PodCreateInsParam checkDepend(String paramName) throws Exception {
        if (!initParams.containsKey(paramName)) {
            throw new Exception("must init " + paramName + " first!");
        }
        return this;
    }


    private void checkInstanceExisted(DefaultApi metaApi, String requestId) throws Exception {
        if (metaApi.getReplicaSet(requestId, dbInstanceName, true) == null) {
            return;
        }
        throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
    }

    /**
     * 获取快照ID
     */
    private String getSnapshotIdIfNeed(AliyunInstanceDependency dependency, Map<String, String> params) throws Exception {
        String sourceDBInstanceId = dependency.getMysqlParamSupport()
                .getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID);
        if (StringUtils.isEmpty(sourceDBInstanceId)) {
            return null;
        }
        if (!StringUtils.isNumeric(sourceDBInstanceId)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID, null);
        if (dependency.getMySQLservice().isRebuildBackupSet(bakId)) {
            dependency.getMysqlParamSupport().setParameter(params, ParamConstants.SOURCE_DBINSTANCE_ID,null);
            return null;
        }
        GetBackupSetResponse backupSet = dependency.getBackupService().getBackupSet(
                BackupSetParam.builder()
                        .uid(uid)
                        .user_id(bid)
                        .dBInstanceId(Integer.parseInt(sourceDBInstanceId))
                        .backupSetId(StringUtils.isNotEmpty(bakId) ? Long.parseLong(bakId) : null).build());
        log.info("backupSet is:{}", backupSet);
        if (!backupSet.getEngine().equalsIgnoreCase(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        if (!backupSet.getEngineVersion().equalsIgnoreCase(dbVersion)) {
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }

        BakhistoryDO bakHistory = dependency.getBakService().getBakhistoryByBackupSetId(
            Integer.valueOf(sourceDBInstanceId), backupSet.getBackupSetId());
        tdeEnabled         = backupSet.getSlaveStatusObj().isTdeEnabled();
        tdeEncryptionKeyId = backupSet.getSlaveStatusObj().getTdeEncryptionKeyId();
        clsKeyMode = backupSet.getSlaveStatusObj().getClsKeyMode();
        clsEncryptionKeyId = backupSet.getSlaveStatusObj().getClsEncryptionKeyId();
        coldDataSnapshotId = backupSet.getSlaveStatusObj().getColdDataSnapshotId();
        coldDataJFSId = backupSet.getSlaveStatusObj().getColdDataFsId();
        snapshotId = backupSet.getSlaveStatusObj().getSnapshotId();
        this.coldSourceBakId = String.valueOf(backupSet.getBackupSetId());
        this.coldDataEnabled = (coldDataEnabled || StringUtils.isNotBlank(coldSourceBakId) && StringUtils.isNotBlank(coldDataJFSId) && StringUtils.isNotBlank(coldDataSnapshotId));
        String variablesString = backupSet.getSlaveStatusObj().getVariables();
        if (StringUtils.isNotEmpty(variablesString)) {
            Map<String, String> variables = new Gson().fromJson(variablesString, new TypeReference<Map<String, String>>() {}.getType());
            initOptimizedWritesFromBackUpSetVariables(variables);
        }
        if (snapshotId == null) {
            throw new RdsException(ErrorCode.INVALID_BACKUPSET);
        }

        log.info("create ins with snapshot id {}", snapshotId);
        isPEngineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);
        if (diskSize != null && (long) diskSize * CustinsSupport.GB_TO_KB < bakHistory.getBaksetSize()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
        }
        CustInstanceDO sourceCustInstanceDO = dependency.getCustinsService().getCustInstanceByCustinsIdIgnoreDelete(userId, Integer.valueOf(sourceDBInstanceId), 0);
        if (sourceCustInstanceDO == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        this.sourceCustInstanceDO = sourceCustInstanceDO;
        this.sourceDBInstanceId = sourceDBInstanceId;
        this.isRestoreFromBackupSet = true;
        return backupSet.getSlaveStatusObj().getSnapshotId();
    }

    /**
     * 格式化TDDLClusterName
     */
    public static String reFormatTddlClusterName(String dbName, String tddlClusterName, boolean isForMigrate) throws RdsException {
        if (isForMigrate && StringUtils.isEmpty(tddlClusterName)) {
            throw new RdsException(INVALID_TDDL_CLUSTER_NAME.toArray());
        }
        if (!isForMigrate) {
            tddlClusterName = String.format("%s_APP", dbName.toUpperCase());
        }

        return tddlClusterName;
    }

    /**
     * 集团属性检查
     */
    public static void aliGroupParamValidate(String dbName, String timeZone, String tddlRegionConfig) throws RdsException {
        if (StringUtils.isEmpty(tddlRegionConfig)) {
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR,
                    "TddlRegionConfigIsEmpty", "TddlRegionConfig can not be null for creating new TDDL instance."});
        }
        if (StringUtils.isEmpty(dbName)) {
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR,
                    "DBNameIsEmpty", "DBName can not be null for creating new TDDL instance."});
        }
        if (StringUtils.isEmpty(timeZone)) {
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR,
                    "TimeZoneIsEmpty", "TimeZone can not be null for creating new TDDL instance."});
        }
    }

    /**
     * 磁盘类型校验
     */
    public static String checkValidDiskType(String diskType) throws RdsException {
        if (StringUtils.isBlank(diskType)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        if (!PodCommonSupport.isSupportDiskType(diskType)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        return diskType;
    }

    /**
     * 主可用区模式校验
     */
    public static void checkValidDispenseMode(AVZInfo avzInfo) throws RdsException {
        if (avzInfo.getDispenseMode() != ParamConstants.DispenseMode.MultiAVZDispenseMode) {
            return;
        }

        MultiAVZExParamDO multiAVZExParamDO = avzInfo.getMultiAVZExParamDO();
        if (multiAVZExParamDO == null || CollectionUtils.isEmpty(multiAVZExParamDO.getAvailableZoneInfoList())) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
    }

    /**
     * 白名单校验
     */
    private IPWhiteList getAndCheckIpWhiteList() throws RdsException {
        IPWhiteList ipWhiteList = dependency.getPodParameterHelper().getAndCheckReplicaSetIpWhiteList();
        if (ipWhiteList == null) {
            throw new RdsException(ErrorCode.INVALID_SECURITYIPLIST_FORMAT);
        }
        return ipWhiteList;
    }

    /**
     * 设置参数初始化完成标，用于其他参数依赖检查
     */
    private void setParamInitDone(String paramName) {
        initParams.put(paramName, true);
    }


    /**
     * 判断是否忽略VPC配置
     */
    public boolean isIgnoreVPC() {
        return isPhysical || isTDDL || isK8sService;
    }


    public PodCreateInsParam setGeneralCloudDiskConfig() throws Exception {
        this.checkDepend("dbVersion").checkDepend("instanceLevel").checkDepend("diskType");
        generalCloudDisk = new GeneralCloudDisk();
        String requestId = dependency.getMysqlParamSupport().getParameterValue(params, ParamConstants.REQUEST_ID);

        if (dependency.getMysqlParamSupport().hasParameter(params, ParamConstants.IO_ACCELERATION_ENABLED) &&
                dependency.getPodCommonSupport().transferIoAccelerationEnabledType(dependency.getMysqlParamSupport().getIoAccelerationEnabled(params))) {
            WarmDataDisk warmDataDisk = new WarmDataDisk()
                    .ioAccelerationType(WarmDataDisk.IoAccelerationTypeEnum.BPE)
                    .ioAccelerationEnabled(dependency.getPodCommonSupport().transferIoAccelerationEnabledType(dependency.getMysqlParamSupport().getIoAccelerationEnabled(params)));

            generalCloudDisk.warmDataDisk(warmDataDisk);
        }
        this.checkDepend("coldDataEnabled").checkDepend("avzInfo");
        if(isColdDataEnabled()){
            // init cold Data Disk
            ColdDataDisk coldDataDisk = dependency.getColdDataService().getColdDataDiskByColdDataEnabled(requestId, true ,avzInfo.getRegionId());
            generalCloudDisk.setColdDataDisk(coldDataDisk);
        }

        // Turning on IO acceleration requires relevant verification
        if (isIoAccelerationEnabled()) {
            dependency.getPodCommonSupport().checkIoAccelerationCondition(requestId, dbVersion, instanceLevel, diskType);
        }

        setParamInitDone("generalCloudDisk");
        return this;
    }

    public boolean isIoAccelerationEnabled() {
        return generalCloudDisk != null && generalCloudDisk.getWarmDataDisk() != null
                && Boolean.TRUE.equals(generalCloudDisk.getWarmDataDisk().getIoAccelerationEnabled());
    }

    /**
     *设置活动相关参数
     */
    public PodCreateInsParam setActivities() throws Exception {
        String activitiesJson = dependency.getMysqlParamSupport().getParameterValue(params, "activities");
        activities = JSONObject.parseObject(activitiesJson, Map.class);
        setParamInitDone("activities");
        return this;
    }

    /**
     * 初始化已删除实例备份集恢复
     * 恢复的备份集ID都是以"rbh-"开头的字符串
     */
    public PodCreateInsParam setRebuildDeleteInsConfig() throws Exception {
        String bakId = getParameterValue(params, ParamConstants.BACKUP_SET_ID, null);
        if (!dependency.getMySQLservice().isRebuildBackupSet(bakId)) {
            return this;
        }
        DescribeRestoreBackupSetParam caller = dependency.getDbsGateWayService().describeRestoreBackupSetBuilder(params,bid);
        DescribeRestoreBackupSetResponse restoreBackupResponse = dependency.getDbsGateWayService().describeRestoreBackupSet(caller);
        if (restoreBackupResponse.getBackupSetInfo() == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        String bakInstanceKindCode = restoreBackupResponse.getBackupSetInfo().getInstanceKindCode();
        String bakSnapShotId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getSNAPSHOT_ID();
        if (StringUtils.isBlank(bakSnapShotId)) {
            throw new RdsException(ErrorCode.INVALID_BACKUPSET);
        }
        snapshotId = bakSnapShotId;
        tdeEnabled         = restoreBackupResponse.getBackupSetInfo().getExtraInfo().isTdeEnabled();
        tdeEncryptionKeyId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getTdeEncryptionKeyId();
        clsKeyMode = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getClsKeyMode();
        clsEncryptionKeyId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getClsEncryptionKeyId();
        coldDataSnapshotId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getColdDataSnapshotId();
        coldDataJFSId = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getColdDataFsId();
        coldSourceBakId = bakId;
        this.coldDataEnabled = (coldDataEnabled || StringUtils.isNotBlank(coldSourceBakId) && StringUtils.isNotBlank(coldDataJFSId) && StringUtils.isNotBlank(coldDataSnapshotId));
        coldDataSourceInstanceName = restoreBackupResponse.getBackupSetInfo().getInstanceName();
        dependency.getMySQLservice().compareBakSizeAndDiskSize(restoreBackupResponse,diskSize);
        dependency.getMySQLservice().checkCustinsAndUser(restoreBackupResponse.getBackupSetInfo().getCustinsId(),bid,uid);
        String backupMinorVersion = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getMINOR_VERSION();
        String variablesString = restoreBackupResponse.getBackupSetInfo().getExtraInfo().getVariables();
        if (StringUtils.isNotEmpty(variablesString)) {
            Map<String, String> variables = new Gson().fromJson(variablesString, new TypeReference<Map<String, String>>() {}.getType());
            initOptimizedWritesFromBackUpSetVariables(variables);
        }
        this.isRestoreFromBackupSet = true;
        try {
            String bakServiceSpecTag = dependency.getMinorVersionServiceHelper().getServiceSpecTag(backupMinorVersion, bizType, dbType, dbVersion, dbEngine,
                    KindCodeParser.KIND_CODE_NEW_ARCH, instanceLevel, diskType, isDhg, true);  // rebuild include offline
            params.put("bakServiceSpecTag", bakServiceSpecTag);
        } catch (RdsException re) {
            log.warn("rebuild deleted ins, minor version is not find: {}", backupMinorVersion);
        }
        if (!KindCodeParser.KIND_CODE_NEW_ARCH.equals(Integer.valueOf(bakInstanceKindCode))){
            throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
        }
        return this;
    }

    private void checkKmsKeyInRegion(String regionId, String keyId, String roleArn, String uid) throws Exception {
        log.info("checkKmsKeyInRegion: regionId {} keyId {} uid {}", regionId, keyId, uid);
        DescribeKeyResponse describeKeyResponse = dependency.getKmsApi().describeKeyByRegionId(regionId, keyId, roleArn, uid);
        if (describeKeyResponse == null || describeKeyResponse.getKeyMetadata() == null) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
        String keyState = describeKeyResponse.getKeyMetadata().getKeyState();
        if (StringUtils.equalsIgnoreCase("Disabled", keyState)) {
            throw new RdsException(ErrorCode.INVALID_KMS_KEY);
        }
    }

    public PodCreateInsParam checkTdeAndClsEncryptionKey() throws Exception {
        this.checkDepend("clusterName").checkDepend("uid");
        if (tdeEnabled && !StringUtils.isBlank(tdeEncryptionKeyId)) {
            log.info("checking if tdeEncryptionKey valid:{}", tdeEncryptionKeyId);
            checkKmsKeyInRegion(regionId, tdeEncryptionKeyId,
                    "acs:ram::" + uid + ":role/aliyunrdsinstanceencryptiondefaultrole", uid);
        }
        if (StringUtils.equalsIgnoreCase(PodDefaultConstants.CLS_MODE_KMS_KEY, clsKeyMode) && StringUtils.isNotBlank(clsEncryptionKeyId)) {
            log.info("checking if clsEncryptionKey valid:{}", clsEncryptionKeyId);
            checkKmsKeyInRegion(regionId, clsEncryptionKeyId,
                    "acs:ram::" + uid + ":role/aliyunrdsinstanceencryptiondefaultrole", uid);
        }
        return this;
    }

    public void initOptimizedWritesFromBackUpSetVariables(Map<String, String> variables) {
        boolean isForceTurnOffOptimized = true;
        if (!variables.isEmpty()) {
            String innodbDoublewrite = variables.getOrDefault("innodb_doublewrite", "ON");
            if ("OFF".equalsIgnoreCase(innodbDoublewrite) || "0".equalsIgnoreCase(innodbDoublewrite)) {
                isForceTurnOffOptimized = false;
                log.info("innodb_doublewrite is off, set optimized_writes to true.");
            } else {
                log.info("innodb_doublewrite is on, set optimized_writes to false.");
            }
        } else {
            log.info("Variables string is empty, set optimized_writes to false by default.");
        }
        initOptimizedWrites = !isForceTurnOffOptimized;
        log.info("Optimized writes initialized: {}", initOptimizedWrites);
    }

}
