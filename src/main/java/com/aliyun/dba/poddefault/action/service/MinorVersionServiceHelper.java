package com.aliyun.dba.poddefault.action.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.modules.CrossTagRule;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.UserGrayService;
import com.aliyun.dba.support.common.registry.dataobject.KindCode;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_ENTERPRISE;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.*;


/**
 * MinorVersionService
 *
 * <AUTHOR> 2020-07-27
 * @blame 宇一
 */
@Service
public class MinorVersionServiceHelper {
    private static final LogAgent logger = LogFactory.getLogAgent(MinorVersionServiceHelper.class);
    /**
     * TAG定义来自自升版本管理定义
     */
    public static final String MINOR_VERSION_TAG_ALISQL_RPM = "AliSQL_Rpm";
    public static final String MINOR_VERSION_TAG_XCLUSTER_RPM = "Xcluster_Rpm";
    public static final String MINOR_VERSION_TAG_ALISQL_ECS_IMAGE_SINGLE = "alisql_ecs_image_single";

    public static final String MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE = "alisql_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_AARCH_DOCKER_IMAGE = "alisql_aarch_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_XCHG_DOCKER_IMAGE = "alisql_xchg_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE = "alisql_beta_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_XC_DOCKER_IMAGE = "alisql_xc_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_DHG_DOCKER_IMAGE = "alisql_dhg_docker_image";
    public static final String MINOR_VERSION_TAG_ALISQL_DUCKDB_DOCKER_IMAGE = "alisql_duckdb_docker_image";
    public static final String MINOR_VERSION_TAG_MARIADB_DOCKER_IMAGE = "mariadb_docker_image";
    public static final String MINOR_VERSION_TAG_XCLUSTER_DHG_DOCKER_IMAGE = "xcluster_dhg_docker_image";
    public static final String MINOR_VERSION_TAG_XCLUSTER_DOCKER_MULTI_W = "xcluster_docker_multi_w";
    public static final String MINOR_VERSION_TAG_XCLUSTER_DOCKER_SINGLE_W = "xcluster_docker_single_w";
    public static final String MINOR_VERSION_TAG_XCLUSTER_DOCKER_POLARX = "xcluster_docker_polarx";
    public static final String MINOR_VERSION_TAG_XCLUSTER_DOCKER_ARM_W = "xcluster_docker_arm_w";


    public final static String IMAGE_BIZ_TYPE_DEFAULT = "default";
    public final static String IMAGE_BIZ_TYPE_DHG = "dhg";
    public final static String IMAGE_BIZ_TYPE_ARM = "arm";

    public final static String TAG_SUBTYPE_XC = "xc";
    public final static String TAG_SUBTYPE_DEFAULT = "";
    public final static String TAG_SUBTYPE_DUCKDB = "duckdb";

    public final static Set<String> subTypeSet = new HashSet<String>() {{
        this.add(TAG_SUBTYPE_XC);
        this.add(TAG_SUBTYPE_DEFAULT);
        this.add(TAG_SUBTYPE_DUCKDB);
    }};

    /**
     * 新架构的版本tag的枚举
     */
    public enum ServiceTag {

        TAG_ALISQL_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),             //aliql的默认tag
        TAG_ALISQL_AARCH_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_AARCH_DOCKER_IMAGE, IMAGE_BIZ_TYPE_ARM, TAG_SUBTYPE_DEFAULT),             //aliql for arm<y
        TAG_ALISQL_BETA_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        TAG_ALISQL_XC_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_XC_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_XC),
        TAG_ALISQL_XCHG_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_XCHG_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_XC),
        TAG_ALISQL_DHG_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_DHG_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DHG, TAG_SUBTYPE_DEFAULT),
        TAG_ALISQL_DUCKDB_DOCKER_IMAGE(MINOR_VERSION_TAG_ALISQL_DUCKDB_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DUCKDB),
        TAG_MARIADB_DOCKER_IMAGE(MINOR_VERSION_TAG_MARIADB_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),             //mariadb的默认tag
        TAG_XCLUSTER_DOCKER_SINGLE_W(MINOR_VERSION_TAG_XCLUSTER_DOCKER_SINGLE_W, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),  //xdb的默认tag
        TAG_XCLUSTER_DOCKER_MULTI_W(MINOR_VERSION_TAG_XCLUSTER_DOCKER_MULTI_W, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        TAG_XCLUSTER_DOCKER_POLARX(MINOR_VERSION_TAG_XCLUSTER_DOCKER_POLARX, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        TAG_XCLUSTER_DHG_DOCKER_IMAGE(MINOR_VERSION_TAG_XCLUSTER_DHG_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DHG, TAG_SUBTYPE_DEFAULT),
        TAG_XCLUSTER_DOCKER_ARM_W(MINOR_VERSION_TAG_XCLUSTER_DOCKER_ARM_W, IMAGE_BIZ_TYPE_ARM, TAG_SUBTYPE_DEFAULT);


        private final String tagPrefix;
        private final String bizType;
        private final String subType;

        ServiceTag(String tagPrefix, String bizType, String subType) {
            this.tagPrefix = tagPrefix;
            this.bizType = bizType;
            this.subType = subType;
        }

        public String getTagPrefix() {
            return tagPrefix;
        }

        public String getBizType() {
            return bizType;
        }

        public String getSubType() {
            return subType;
        }

        public static String getSubtypeByServiceSpecTag(String specTag) {
            if (StringUtils.isBlank(specTag)) {
                return null;
            }
            for (ServiceTag tag : ServiceTag.values()) {
                if (specTag.contains(tag.getTagPrefix())) {
                    return tag.getSubType();
                }
            }
            return null; // 如果未找到匹配的 prefix
        }
    }

    private final static String RELEASE_TYPE_LTS = "LTS";
    private final static String RELEASE_TYPE_BETA = "BETA";
    private final static String RELEASE_TYPE_HOTFIX = "HOTFIX";

    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private CustinsParamService custinsParamService;
    @Resource
    private InstanceService instanceService;
    @Resource
    private MysqlDBCustinsService mysqlDBCustinsService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private ClusterService clusterService;
    @Resource
    private UserGrayService userGrayService;
    @Resource
    private ResourceService resourceService;

    private final Cache<String, String> resourceCache = CacheBuilder.newBuilder()
            .maximumSize(64)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build();

    private final static String CROSS_TAG_RULES_KEY = "CROSS_TAG_RULES";

    /**
     * TAG定义来自自升版本管理定义
     */

    public static final String MINOR_VERSION_TAG_XCLUSTER_RPM_POLARX_HATP = "xcluster_rpm_polarx_hatp";

    public static final String MINOR_VERSION_DBENGINE_PREFIX_XDB = "XDB";
    public static final String MINOR_VERSION_DBENGINE_PREFIX_MYSQL = "MySQL";


    public static final String MINOR_VERSION_PREFIX_XCLUSTER = "xcluster";
    public static final String MINOR_VERSION_PREFIX_XCLUSTER80 = "xcluster80";
    public static final String MINOR_VERSION_PREFIX_MYSQL = "mysql";

    public static final String CUSTINS_PARAM_NAME_IS_POLARX_HATP = "is_polarx_hatp";
    public static final String CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES = "1";

    /**
     * pengine任务流中指示当前实例从属于开启dbs服务化的polarx实例
     * */
    public static final String POLARX_USING_DBS_SERVICE_PENGINE = "is_polarx_using_dbs_service";

    /**
     * 指示dbs服务化开启，用于在custins_param表标识
     * */
    public static final String POLARX_USING_DBS_SERVICE_PENGINE_YES = "1";

    /**
     * ext组件间指示当前实例从属于开启dbs服务化的polarx实例，用于ext-polarx到ext-mysql的参数传递
     * */
    public static final String POLARX_USING_DBS_SERVICE_EXT = "IsPolarxUsingDBSService";

    @Resource
    private MinorVersionService minorVersionService;

    /**
     * 根据传入的信息获取目标小版本Tag
     *
     * @param targetMinorVersion 指定的小版本
     * @param bizType            业务类型（aliyun/aligroup）
     * @param dbType             数据库类型
     * @param dbVersion          数据库版本
     * @param dbEngine           引擎（MySQL/XDB）
     * @param kindCode           kindCode
     * @param instanceLevel      规格
     * @param storageType        存储类型
     * @param isDhg              是否大客户
     * @return
     * @throws RdsException
     */
    public String getServiceSpecTag(
            String targetMinorVersion,
            ReplicaSet.BizTypeEnum bizType,
            String dbType,
            String dbVersion,
            String dbEngine,
            Integer kindCode,
            InstanceLevel instanceLevel,
            String storageType,
            boolean isDhg,
            Map<String, String> tagMap) throws RdsException {
        return getServiceSpecTag(null, targetMinorVersion, bizType, dbType, dbVersion, dbEngine, kindCode,
                instanceLevel, storageType, isDhg, false, tagMap);
    }


    /**
     * 根据传入的信息获取目标小版本Tag（可设置是否使用强制使用默认Tag）
     *
     * @param targetMinorVersion 指定的小版本
     * @param bizType            业务类型（aliyun/aligroup）
     * @param dbType             数据库类型
     * @param dbVersion          数据库版本
     * @param dbEngine           引擎（MySQL/XDB）
     * @param kindCode           kindCode
     * @param instanceLevel      规格
     * @param storageType        存储类型
     * @param isDhg              是否大客户
     * @param isContainsOffline  是否包含默认版本
     * @return
     * @throws RdsException
     */
    public String getServiceSpecTag(
            String targetMinorVersion,
            ReplicaSet.BizTypeEnum bizType,
            String dbType,
            String dbVersion,
            String dbEngine,
            Integer kindCode,
            InstanceLevel instanceLevel,
            String storageType,
            boolean isDhg,
            boolean isContainsOffline,
            Map<String, String> tagMap) throws RdsException {
        return getServiceSpecTag(null, targetMinorVersion, bizType, dbType, dbVersion, dbEngine, kindCode,
                instanceLevel, storageType, isDhg, isContainsOffline, tagMap);
    }




    /**
     * 根据原实例获取目标小版本的Tag
     *
     * @param targetMinorVersion 指定的小版本
     * @param bizType            业务类型（aliyun/aligroup）
     * @param dbType             数据库类型
     * @param dbVersion          数据库版本
     * @param dbEngine           引擎（MySQL/XDB）
     * @param kindCode           kindCode
     * @param instanceLevel      规格
     * @param storageType        存储类型
     * @param isDhg              是否大客户
     * @return
     * @throws RdsException
     */
    public String getServiceSpecTag(
            String replicaSetName,
            String targetMinorVersion,
            ReplicaSet.BizTypeEnum bizType,
            String dbType,
            String dbVersion,
            String dbEngine,
            Integer kindCode,
            InstanceLevel instanceLevel,
            String storageType,
            boolean isDhg,
            Map<String, String> tagMap) throws Exception {
        if (StringUtils.isEmpty(replicaSetName)) {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
        String originalSpecTag = getServiceSpecTagByReplicaSetName(UUID.randomUUID().toString(), replicaSetName);
        return getServiceSpecTag(originalSpecTag, targetMinorVersion, bizType, dbType, dbVersion, dbEngine, kindCode,
                instanceLevel, storageType, isDhg, false, tagMap);
    }


    /**
     * 尝试根据传入的信息获取目标小版本Tag，如果找不到尝试最新版本
     *
     * @param targetMinorVersion 指定的小版本
     * @param bizType            业务类型（aliyun/aligroup）
     * @param dbType             数据库类型
     * @param dbVersion          数据库版本
     * @param dbEngine           引擎（MySQL/XDB）
     * @param kindCode           kindCode
     * @param instanceLevel      规格
     * @param storageType        存储类型
     * @param isDhg              是否大客户
     * @return
     * @throws RdsException
     */
    public String tryGetServiceSpecTag(
            String targetMinorVersion,
            ReplicaSet.BizTypeEnum bizType,
            String dbType,
            String dbVersion,
            String dbEngine,
            Integer kindCode,
            InstanceLevel instanceLevel,
            String storageType,
            boolean isDhg,
            Map<String, String> tagMap) throws RdsException {
        String serviceSpecTag = null;
        if (StringUtils.isBlank(targetMinorVersion)) {
            logger.error("targetMinorVersion is empty, please to check");
            throw new RdsException(MINOR_VERSION_TAG_NOT_FOUND_BY_CUSTINS);
        }
        try {
            //使用目标内核版本进行查询，找到相同的版本信息（包含在线和下线状态的）
            serviceSpecTag = this.getServiceSpecTag(targetMinorVersion,
                    bizType,
                    dbType,
                    dbVersion,
                    dbEngine,
                    KIND_CODE_NEW_ARCH,
                    instanceLevel,
                    storageType,
                    isDhg,
                    true,
                    tagMap);
        } catch (RdsException e) {
            // errorCode的格式：{code, summary, desc}
            logger.warn(e.getMessage());
            Object[] errorCode = e.getErrorCode();
            if (errorCode.length != 3) {
                throw e;
            }
            // 相同内核版本找不到，尝试使用最新内核版本
            String summary = errorCode[1].toString();
            if (StringUtils.equalsAnyIgnoreCase(summary, INVALID_MINOR_VERSION_NOT_FOUND.getSummary(), UNSUPPORTED_MINOR_VERSION.getSummary())) {
                logger.info("target minor version not found or unsupported, try to find the latest one");
                serviceSpecTag = this.getServiceSpecTag(null,
                        bizType,
                        dbType,
                        dbVersion,
                        dbEngine,
                        KIND_CODE_NEW_ARCH,
                        instanceLevel,
                        storageType,
                        isDhg,
                        tagMap);
                // 只读实例的内核版本不能低于主实例，否则可能会出现拉不起来的情况
                String currentReleaseDate = this.parseReleaseDate(targetMinorVersion, dbType, dbEngine, MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL);
                String targetReleaseDate = this.parseServiceSpecReleaseDate(serviceSpecTag);
                if (StringUtils.compare(currentReleaseDate, targetReleaseDate) > 0) {
                    logger.error("invalid minor version, expected:{}, current:{}", targetReleaseDate, currentReleaseDate);
                    throw new RdsException(UNSUPPORTED_MINOR_VERSION);
                }
            }
        }
        if (StringUtils.isBlank(serviceSpecTag)) {
            logger.error("service spec not found");
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }
        return serviceSpecTag;
    }



    /**
     * 根据传入的信息获取目标小版本Tag
     *
     * @param originalSpecTag    原始的Tag
     * @param targetMinorVersion 指定的小版本
     * @param bizType            业务类型（aliyun/aligroup）
     * @param dbType             数据库类型
     * @param dbVersion          数据库版本
     * @param dbEngine           引擎（MySQL/XDB）
     * @param kindCode           kindCode
     * @param instanceLevel      规格
     * @param storageType        存储类型
     * @param isDhg              是否大客户
     * @param isContainsOffline  是否包含默认版本
     * @return
     * @throws RdsException
     */
    private String getServiceSpecTag(
            String originalSpecTag,
            String targetMinorVersion,
            ReplicaSet.BizTypeEnum bizType,
            String dbType,
            String dbVersion,
            String dbEngine,
            Integer kindCode,
            InstanceLevel instanceLevel,
            String storageType,
            boolean isDhg,
            boolean isContainsOffline,
            Map<String, String> tagMap) throws RdsException {
        //检查实例指定版本创建，单写/多写的release_date的8位数字一定不同，与感仰沟通，由内核发布保证
        // 默认TAG为多版本
        String specifyReleaseTag;
        boolean isArm = PodCommonSupport.isArm(instanceLevel);
        boolean isX86HG = PodCommonSupport.isX86HG(instanceLevel);
        // 小版本升级：minor_version需要保持与原有的小版本一致，只是升级releaseDate，因此需要从原有的service_spec_tag中获取
        boolean isAnalyticReadOnlyIns = false;
        if (tagMap != null && tagMap.containsKey("isAnalyticReadOnlyIns")) {
            isAnalyticReadOnlyIns = Boolean.parseBoolean(tagMap.get("isAnalyticReadOnlyIns"));
        }

        if (StringUtils.isNotEmpty(originalSpecTag)) {
            specifyReleaseTag = parseOriginalSpecTag(originalSpecTag);
        } else if ("XDB".equalsIgnoreCase(dbEngine)) {
            // 这里不同场景下的是默认TAG逻辑
            if (PodParameterHelper.isAliGroup(bizType)) {
                if (isArm) {
                    specifyReleaseTag = ServiceTag.TAG_XCLUSTER_DOCKER_ARM_W.getTagPrefix();
                } else {
                    specifyReleaseTag = ServiceTag.TAG_XCLUSTER_DOCKER_SINGLE_W.getTagPrefix();
                }
            } else if (!isDhg) {
                specifyReleaseTag = ServiceTag.TAG_XCLUSTER_DOCKER_SINGLE_W.getTagPrefix();
            } else {
                specifyReleaseTag = ServiceTag.TAG_XCLUSTER_DHG_DOCKER_IMAGE.getTagPrefix();
            }
        } else if ("mysql".equalsIgnoreCase(dbEngine)) {
            boolean isXC = targetMinorVersion != null && targetMinorVersion.contains("xc_");
            // 这里不同场景下的是默认TAG逻辑
            if (!isDhg) {
                //信创逻辑
                if (isXC && isArm) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                } else if (isXC) {
                    specifyReleaseTag = ServiceTag.TAG_ALISQL_XC_DOCKER_IMAGE.getTagPrefix();
                } else if (isX86HG) {
                    specifyReleaseTag = ServiceTag.TAG_ALISQL_XCHG_DOCKER_IMAGE.getTagPrefix();
                } else if (isArm) {
                    specifyReleaseTag = ServiceTag.TAG_ALISQL_AARCH_DOCKER_IMAGE.getTagPrefix();
                }  else if (isAnalyticReadOnlyIns) {
                    // 分析型只读实例的内核版本与主实例内核版本解藕
                    // 将targetMinorVersion设置为null 直接去查最新的小版本信息
                    specifyReleaseTag = ServiceTag.TAG_ALISQL_DUCKDB_DOCKER_IMAGE.getTagPrefix();
                    targetMinorVersion = null;
                } else {
                    specifyReleaseTag = ServiceTag.TAG_ALISQL_DOCKER_IMAGE.getTagPrefix();
                }
            } else {
                specifyReleaseTag = ServiceTag.TAG_ALISQL_DHG_DOCKER_IMAGE.getTagPrefix();
            }
        } else if ("mariadb".equalsIgnoreCase(dbEngine)) {
            specifyReleaseTag = ServiceTag.TAG_MARIADB_DOCKER_IMAGE.getTagPrefix();
        } else {
            throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
        }
        //默认Tag目前支持三大类镜像，default，dhg，arm。找到当前默认Tag所属的业务类型
        String imageBizType = findImageBizType(specifyReleaseTag);
        if (StringUtils.isEmpty(imageBizType)) {
            logger.error("Minor Version's bizType is empty");
            throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
        }

        // 此处主要用来检查版本发布的正确性
        String category = instanceLevel.getCategory().toString();
        String classCode = instanceLevel.getClassCode();
        String releaseDate;
        //给出serviceSpecId
        if (targetMinorVersion != null) {
            logger.info("parseReleaseDate targetMinorVersion:{} dbType:{} dbVersion:{} dbEngine:{} isContainsOffline:{} specTag:{}", targetMinorVersion, dbType, dbVersion, dbEngine, isContainsOffline,specifyReleaseTag);
            releaseDate = parseReleaseDate(targetMinorVersion, dbType, dbVersion, dbEngine);
            //查找在指定的Tag下中符合releaseDate的版本信息
            List<MinorVersionReleaseDO> minorVersionReleaseDOS;
            if (isContainsOffline) {
                minorVersionReleaseDOS = minorVersionService.queryAllMinorVersionListByCondition(dbType, dbVersion, kindCode, category, specifyReleaseTag, releaseDate);
            } else {
                minorVersionReleaseDOS = minorVersionService.querySpecifyMinorVersionListByCondition(dbType, dbVersion, kindCode, category, specifyReleaseTag, releaseDate);
            }
            if (CollectionUtils.isEmpty(minorVersionReleaseDOS) && StringUtils.isNotEmpty(originalSpecTag)) {
                //指定的版本必须和原始的Tag保持一致
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            if (CollectionUtils.isEmpty(minorVersionReleaseDOS)) {
                //如果没有找到，则直接根据releaseDate中取最新的内核版本记录，找到后需要做Tag的组校验
                List<MinorVersionReleaseDO> minorVersionsWithOutTag = minorVersionService.querySpecifyMinorVersionListByCondition(dbType, dbVersion, kindCode, category, null, releaseDate);
                if (CollectionUtils.isEmpty(minorVersionsWithOutTag)) {
                    logger.error("specify minor version=" + releaseDate + " not found parsed by dbType=" + dbType + ",dbVersion=" + dbVersion + ",classCode=" + classCode);
                    throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
                }
                // 记录必须唯一
                if (minorVersionsWithOutTag.size() > 1) {
                    logger.error("{} records were found in minor version records, please to check", minorVersionsWithOutTag.size());
                    throw new RdsException(MINOR_VERSION_NOT_SUPPORT);
                }
                specifyReleaseTag = minorVersionsWithOutTag.get(0).getTag();
                //这里要做校验，保证Tag是在相同的bizType中
                if (!StringUtils.equals(imageBizType, findImageBizType(specifyReleaseTag))) {
                    logger.error("Minor Version's bizType is not equal");
                    throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
                }
            } else {
                specifyReleaseTag = minorVersionReleaseDOS.get(0).getTag();
            }
        } else {
            logger.info("getReleaseDateListByTag params, dbType[{}], dbEngine[{}], dbVersion[{}], kindCode[{}], category[{}], tag[{}]",
                    dbType, dbEngine, dbVersion, kindCode, category, specifyReleaseTag);
            releaseDate = fetchReleaseDateBySpecTag(dbType, dbVersion, kindCode, category, specifyReleaseTag);
        }
        logger.info("get minor version releaseDate is " + releaseDate);
        // 无可指定指定小版本走原逻辑
        if (StringUtils.isEmpty(releaseDate)) {
            logger.error("specify minor version=" + releaseDate + "not found parsed by dbType=" + dbType + ",dbVersion=" + dbVersion + ",classCode=" + classCode);
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }

        //单节点xdb的逻辑
        String fileSystem = MySQLParamConstants.FILE_SYSTEM_EXT4;
        boolean isCloudDisk = ReplicaSetService.isStorageTypeCloudDisk(storageType);
        if (PodParameterHelper.isAliGroup(bizType) && isCloudDisk) {
            fileSystem = MySQLParamConstants.FILE_SYSTEM_PFS;
            if (MysqlParamSupport.isSingleNode(instanceLevel)) {
                fileSystem = MySQLParamConstants.FILE_SYSTEM_EXT4;
            }
        }
        return PodCommonSupport.getComposeTag(specifyReleaseTag, storageType, releaseDate, bizType, fileSystem);
    }


    public String getServiceSpecTagByReplicaSetName(String requestId, String replicaSetName) throws ApiException, RdsException {
        Integer specId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetName);
        String specTag = null;
        if (specId != null) {
            ServiceSpec spec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, specId);
            if (spec != null) {
                specTag = spec.getTag();
            }
        }

        if (StringUtils.isEmpty(specTag)) {
            logger.error("spec tag not found from source replicaSetName: " + replicaSetName);
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
        return specTag;
    }


    public String getServiceSpecTagByCustinsId(String requestId, Integer custinsId) throws ApiException, RdsException {
        Integer specId = custinsService.getCustInstanceEngineComposeId(custinsId);
        String specTag = null;
        if (specId != null) {
            ServiceSpec spec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, specId);
            if (spec != null) {
                specTag = spec.getTag();
            }
        }
        logger.info("get ServiceSpec from custinsId [{}], spec is [{}]", custinsId, specTag);
        return specTag;
    }



    public String resetReplicaSetMinorVersion(String requestId, String replicaSetName) throws ApiException {
        return resetReplicaSetMinorVersion(requestId, replicaSetName, null);
    }

    public String resetReplicaSetMinorVersion(String requestId, String replicaSetName, String regionId) throws ApiException {
        String minorVersion = getMinorVersionStr(requestId, replicaSetName, regionId);
        if (StringUtils.isBlank(minorVersion)) {
            return null;
        }

        logger.info("reset minor version: " + minorVersion);
        return minorVersion;
    }

    private String getMinorVersionStr(String requestId, String replicaSetName, String regionId) throws ApiException {
        String minorVersion = null;
        Map<String, String> labels = dBaasMetaService.getMetaDbClient(regionId).listReplicaSetLabels(requestId, replicaSetName);
        Integer specId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetName);
        ServiceSpec spec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, specId);
        if (labels.isEmpty()) {
            logger.warn("no labels found");
            return "";
        }

        for (Map.Entry<String, String> entry : labels.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            if ("minor_version".equalsIgnoreCase(name)) {
                minorVersion = value;
                break;
            }
        }

        if (StringUtils.isBlank(minorVersion) || !minorVersion.startsWith("apsaradb")) {
            return "";
        }

        String[] tmp = minorVersion.split(":");
        String base = tmp[0];
        String version = "";
        if (tmp.length > 1) {
            version = tmp[1];
        }
        if (base.split("_").length > 1) {
            base = base.split("_")[1];
        } else {
            String[] arr = base.split("-");
            base = arr[arr.length - 1];
        }
        version = version.split("-")[0];
        String subtype = ServiceTag.getSubtypeByServiceSpecTag(spec.getTag());
        if (!StringUtils.isEmpty(subtype)) {
            return base + "_" + subtype + "_" + version;
        }
        return base + "_" + version;
    }

    /**
     * e.g.
     * rds_20200229
     * rds_duckdb_20241231
     * rds_xc_20241231
     * xcluster_20200229
     * xcluster80_20200229
     */
    public String parseReleaseDate(String targetMinorVersion,
                                   String dbType,
                                   String dbVersion,
                                   String dbEngine) throws RdsException {
        String[] versions = targetMinorVersion.split("_");
        if (versions.length == 3 && subTypeSet.contains(versions[1])) {
            return versions[2];
        }
        if (versions.length != 2) {
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
        if ("XDB".equalsIgnoreCase(dbEngine) && "5.7".equals(dbVersion) && versions[0].startsWith("xcluster")
                || "XDB".equalsIgnoreCase(dbEngine) && "8.0".equals(dbVersion) && versions[0].startsWith("xcluster80")
                || versions[0].startsWith("rds") || versions[0].startsWith("mysql")) {
            return versions[1];
        } else {
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
    }

    /**
     * e.g.
     * rds_20200229             null
     * rds_xc_20241231          xc
     * xcluster_20200229        null
     * rds_duckdb_20241231      duckdb
     */
    public String parseSubTypeFromMinorVersion(String targetMinorVersion) throws RdsException {
        String[] versions = targetMinorVersion.split("_");
        if (versions.length == 2) {
            return TAG_SUBTYPE_DEFAULT;
        } else if (versions.length == 3) {
            String subType = versions[1];
            if (subTypeSet.contains(subType)) {
                return subType;
            } else {
                throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
            }
        } else {
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }
    }

    /**
     * 解析 serviceSpec中的releaseDate
     *
     * @param targetServiceSpec
     * @return
     * @throws RdsException
     */
    public String parseServiceSpecReleaseDate(String targetServiceSpec) throws RdsException {
        return StringUtils.substringAfterLast(targetServiceSpec, "_");
    }

    /**
     * mysql56_20200229
     * mysql57_20200229
     * mysql80_20200229
     * xcluster_20200229
     * xcluster80_20200229
     */
    public String generateTargetMinorVersion(String dbType, String dbVersion, String releaseDate, String dbEngine)
            throws RdsException {

        if (MINOR_VERSION_DBENGINE_PREFIX_XDB.equalsIgnoreCase(dbEngine) && "5.7".equalsIgnoreCase(dbVersion)) {
            return MINOR_VERSION_PREFIX_XCLUSTER + "_" + releaseDate;
        } else if (MINOR_VERSION_DBENGINE_PREFIX_XDB.equalsIgnoreCase(dbEngine) && "8.0".equalsIgnoreCase(dbVersion)) {
            return MINOR_VERSION_PREFIX_XCLUSTER80 + "_" + releaseDate;
        } else if (MINOR_VERSION_DBENGINE_PREFIX_MYSQL.equalsIgnoreCase(dbEngine)) {
            return MINOR_VERSION_PREFIX_MYSQL + dbVersion.replace(".", "") + "_" + releaseDate;
        }

        throw new RdsException(ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND);
    }

    /**
     * @param dbType
     * @param dbVersion
     * @param classCode
     * @param dbEngine           now XDB or mysql
     * @param targetMinorVersion only three rds_20200229, xcluster_20200229, xcluster80_20200229
     */
    public String checkAndGetTargetMinorVersion(String dbType, String dbVersion, String classCode, String dbEngine, String targetMinorVersion) throws RdsException {

        if (targetMinorVersion != null) {
            String releaseDate = parseReleaseDate(targetMinorVersion, dbType, dbVersion, dbEngine);
            Integer minorVersionReleaseId = minorVersionService.checkAndGetSpecifyMinorVersion(dbType, dbVersion, classCode, null, releaseDate);
            if (minorVersionReleaseId == null) {
                logger.error("target minor version=" + targetMinorVersion + "not found parsed by dbType=" + dbType + ",dbVersion=" + dbVersion + ",classCode=" + classCode);
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            //如果存在，则将对应的targetMinorVersion组装为mysql56_20200229/xcluster80_20200805格式
            return generateTargetMinorVersion(dbType, dbVersion, releaseDate, dbEngine);
        }

        return null;
    }

    /**
     * 从所有小版本中筛选需要的小版本（包括下线的小版本）
     * @param dbType
     * @param dbVersion
     * @param classCode
     * @param kindCode      KIND_CODE_NC, KIND_CODE_ECS_VM, KIND_CODE_DOCKER_ON_ECS, KIND_CODE_NEW_ARCH
     * @param dbEngine      mysql
     * @param targetMinorVersion
     * @return
     * @throws RdsException
     */
    public String checkAndGetAllMinorVersion(String dbType, String dbVersion, String classCode, Integer kindCode, String dbEngine, String targetMinorVersion) throws RdsException {
        if (targetMinorVersion != null) {
            InstanceLevelDO instanceLevelDO = this.instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion, (Character)null, (String)null);
            String category = instanceLevelDO.getCategory().toString();
            String releaseDate = parseReleaseDate(targetMinorVersion, dbType, dbVersion, dbEngine);
            List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.queryAllMinorVersionListByCondition(dbType, dbVersion, kindCode, category, null, releaseDate);
            if (CollectionUtils.isEmpty(minorVersionReleaseDOS)) {
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            Integer minorVersionReleaseId = minorVersionReleaseDOS.get(0).getId();
            if (minorVersionReleaseId == null) {
                logger.error("target minor version=" + targetMinorVersion + "not found parsed by dbType=" + dbType + ",dbVersion=" + dbVersion + ",classCode=" + classCode);
                throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
            }
            //如果存在，则将对应的targetMinorVersion组装为如mysql56_20200229格式
            return generateTargetMinorVersion(dbType, dbVersion, releaseDate, dbEngine);
        }

        return null;
    }


    /**
     * 尝试找到最新的内核版本
     *
     * @param dbType
     * @param dbVersion
     * @return
     * @throws RdsException
     */
    public String tryGetLatestMinorVersion(String dbType, String dbVersion, String category,Integer kindCode,String tag) throws RdsException {
        List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.queryMinorVersionListByCondition(dbType,
                dbVersion,
                kindCode,
                category,
                tag,
                null);
        if (CollectionUtils.isNotEmpty(minorVersionReleaseDOS)) {
            List<MinorVersionReleaseDO> finalMinorVersionReleaseDOS = minorVersionReleaseDOS.stream()
                    .filter(v -> isAvailableMinorVersion(v))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(finalMinorVersionReleaseDOS)) {
                String releaseDate = finalMinorVersionReleaseDOS.get(0).getReleaseDate();
                return generateTargetMinorVersion(dbType, dbVersion, releaseDate, MinorVersionServiceHelper.MINOR_VERSION_DBENGINE_PREFIX_MYSQL);
            }
        }
        return null;
    }


    public String getTagByCondition(String dbType, String dbVersion, String classCode, String dbEngine, String targetMinorVersion)
            throws RdsException {

        String releaseDate = parseReleaseDate(targetMinorVersion, dbType, dbVersion, dbEngine);
        Integer minorVersionReleaseId = minorVersionService.checkAndGetSpecifyMinorVersion(dbType, dbVersion, classCode, null, releaseDate);
        MinorVersionReleaseDO minorVersionReleaseDO = minorVersionService.getMinorVersionReleaseById(minorVersionReleaseId);
        return minorVersionReleaseDO != null ? minorVersionReleaseDO.getTag() : null;
    }

    /**
     * 检查并且返回{@link ServiceSpec}
     *
     * @param requestId
     * @param minorVersion
     * @param dbType
     * @param dbVersion
     * @param serviceSpecTag
     * @return
     * @throws ApiException
     */
    public int checkAndGetServiceSpecId(String requestId, String minorVersion,
                                        String dbType, String dbVersion,
                                        String serviceSpecTag, String category) throws RdsException, ApiException {
        ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpec(requestId, dbType, dbVersion, "pod_arch_v1",
                category, StringUtils.isEmpty(serviceSpecTag) ? "default" : serviceSpecTag, false);
        if (serviceSpec == null) {
            logger.error(MessageFormat.format(
                    "empty_service_spec,serviceSpecTag:{0},minorVersion:{1}", serviceSpecTag, minorVersion));
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }

        return serviceSpec.getId();
    }

    /**
     * 检查并且返回 ServiceSpec中的image
     *
     * @param requestId
     * @param replicaSetName
     * @return
     */
    public JSONObject getSpecImageByReplicaSetName(String requestId, String replicaSetName){
        try{
            Integer specId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetName);
            ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, specId);
            logger.info("get spec for [{}], replicaSetName:[{}]", serviceSpec.getSpec(), replicaSetName);
            JSONArray containersJSONArray = JSON.parseObject(serviceSpec.getSpec()).getJSONArray("containers");
            return containersJSONArray.getJSONObject(0).getJSONObject("image");
        }catch (ApiException apiE){
            logger.warn("getSpecImageByReplicaSetName failed, get ApiException: ", apiE);
        }catch (Exception E){
            logger.warn("getSpecImageByReplicaSetName failed, get Exception: ", E);
        }
        return null;
    }

    /**
     * 检查并返回ReleaseDate<br /> 只能升级，不能版本相同或者降级
     *
     * @param requestId
     * @param replicaSet
     * @param originalSpecTag
     * @return
     * @throws ApiException
     */
    public String checkAndGetReleaseDate(String requestId, ReplicaSet replicaSet, String category,
                                         String originalSpecTag, String minorVersion) throws RdsException, ApiException {
        String expectedReleaseDate;
        if (StringUtils.isNotBlank(minorVersion)) {
            expectedReleaseDate = StringUtils.substringAfterLast(minorVersion,
                    StringUtils.contains(minorVersion, "_") ? "_" : ":");
        } else {
            String specifyReleaseTag = parseOriginalSpecTag(originalSpecTag);
            expectedReleaseDate = fetchReleaseDateBySpecTag(replicaSet.getService(),  replicaSet.getServiceVersion(), KindCodeParser.KIND_CODE_NEW_ARCH,
                    category, specifyReleaseTag);
            if (StringUtils.isEmpty(expectedReleaseDate)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }
        }

        // 只能升级，不能版本相同或者降级
        Map<String, String> labelMap
                = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, replicaSet.getName());
        String actualMinorVersion = labelMap.get("minor_version");
        String actualReleaseDate = StringUtils.substringAfterLast(actualMinorVersion,
                StringUtils.contains(actualMinorVersion, "_") ? "_" : ":");

        //arm x86变配允许版本号相同, 目前逻辑符合要求，如果有改动请兼容架构变配逻辑
        if (StringUtils.compare(actualReleaseDate, expectedReleaseDate) > 0) {
            logger.error(MessageFormat.format("invalid_minor_version,expected:{0},actual:{1}", expectedReleaseDate, actualReleaseDate));
            throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
        }

        return expectedReleaseDate;
    }


    /**
     * 根据各个条件获取对应SpecTag下的ReleaseDate版本
     *
     * @param dbType
     * @param dbVersion
     * @param kindCode
     * @param category
     * @param specifyReleaseTag
     * @return
     * @throws RdsException
     */
    public String fetchReleaseDateBySpecTag(String dbType, String dbVersion, Integer kindCode, String category, String specifyReleaseTag) throws RdsException {
        // 不指定小版本的时候，根据TAG获取最新的小版本
        // 从元数据库中查询出的版本是根据ReleaseDate排序，并且上线状态的版本
        List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.queryMinorVersionListWithOfflineByCondition(dbType, dbVersion, kindCode, category, specifyReleaseTag, null);
        if (CollectionUtils.isEmpty(minorVersionReleaseDOS)) {
            logger.error("The latest minor version cannot found");
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }
        //1.Beta版本过滤掉不满足灰度的版本
        //2.非Beta版本直接保留
        List<MinorVersionReleaseDO> finalMinorVersionReleaseDOS = minorVersionReleaseDOS.stream()
                .filter(v -> isAvailableMinorVersion(v))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalMinorVersionReleaseDOS)) {
            logger.error("After filter version, The latest minor version cannot found");
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }
        //直接取最新的版本
        return finalMinorVersionReleaseDOS.get(0).getReleaseDate();
    }


    /**
     * 判断是否可用内核版本，包含灰度策略
     *
     * @return
     * 1. 非灰度版本：默认直接可用
     * 2. 灰度版本： 根据UID or 百分比来 判断是否需要使用这个版本
     *
     */
    public boolean isAvailableMinorVersion(MinorVersionReleaseDO minorVersionReleaseDO) {
        boolean isOnline = minorVersionReleaseDO.getStatus() == 1;
        String config = minorVersionReleaseDO.getEngineComposeInfo();
        try {
            if (StringUtils.isEmpty(config)) {
                return isOnline;
            }
            JSONObject strategyConfig = JSON.parseObject(config);
            if (strategyConfig.isEmpty() || !strategyConfig.containsKey("grayConfig")) {
                return isOnline;
            }
            // 有配置灰度，则根据灰度策略判断
            JSONObject grayConfig = strategyConfig.getJSONObject("grayConfig");
            return userGrayService.isHit(grayConfig);
        } catch (Exception e) {
            //ignore
        }
        return isOnline;
    }


    /**
     * 小版本升级：minor_version需要保持与原有的小版本一致，只是升级releaseDate，
     * 因此需要从原有的service_spec_tag中获取
     *
     * @param originalSpecTag
     * @return
     * @throws RdsException
     */
    public String parseOriginalSpecTag(String originalSpecTag) throws RdsException {
        logger.info("originalSpecTag is {}", originalSpecTag);
        if ("default".equalsIgnoreCase(originalSpecTag) || "cloud_pfs".equalsIgnoreCase(originalSpecTag)) {
            return originalSpecTag;
        }
        ServiceTag[] serviceTags = ServiceTag.values();
        for (ServiceTag serviceTag : serviceTags) {
            if (StringUtils.startsWith(originalSpecTag, serviceTag.getTagPrefix())) {
                return serviceTag.getTagPrefix();
            }
        }
        throw new RdsException(ErrorCode.UNSUPPORTED_MINOR_VERSION);
    }

    public String getMinorVersionCategory(CustInstanceDO custins) {
        //版本不合法则抛出异常
        String category = instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getCategory();
        //xdb只读使用的是standard的规格，在小版本中，需要转为enterprise，否则招不到指定版本
        if (mysqlParameterHelper.isXDBReadins(custins)) {
            category = CATEGORY_ENTERPRISE;
        }

        //5.6三节点，与高可用使用相同category
        if (mysqlDBCustinsService.isMysqlEnterprise(custins) && CustinsSupport.DB_VERSION_MYSQL_56.equalsIgnoreCase(custins.getDbVersion())) {
            category = CATEGORY_STANDARD;
        }
        return category;
    }


    /**
     * 物理机/OnEcs：mysql{xx}_xxxxxxxx
     * docker: apsaradb-alios7u-mysql5715_20200229-20200320151001
     * xcluster: apsaradb-alios7u-mysql_xdb57:20200702-20200703114607
     */
    public String parseReleaseDateFromMinorVersion(String minorVersion) {

        //控制台格式
        String rdsPattern = "^rds_\\d{8}$";
        String physicalOnEcsPattern = "^mysql\\d{0,2}_\\d{8}$";
        String physicalXclusterPattern = "^xcluster\\d{0,2}_\\d{8}$";
        String dockerPattern = "^apsaradb-alios\\d{1}u-mysql\\d{2,4}_\\d{8}-\\d{14}$";
        String xclusterOnecsPattern = "^apsaradb-alios\\d{1}u-mysql_xdb\\d{2}:\\d{8}-\\d{14}$";
        String mariadbDockerOnEcsPattern = "^mariadb\\d{3,4}_\\d{8}-\\d{14}$";

        List<String> patternList = new ArrayList<>();
        patternList.add(rdsPattern);
        patternList.add(physicalOnEcsPattern);
        patternList.add(physicalXclusterPattern);
        patternList.add(dockerPattern);
        patternList.add(xclusterOnecsPattern);
        patternList.add(mariadbDockerOnEcsPattern);

        for (String pattern : patternList) {
            Matcher matcher = Pattern.compile(pattern).matcher(minorVersion);
            if (matcher.find()) {
                String[] arr = minorVersion.split("_|-|:");
                for (String str : arr) {
                    if (str.length() == 8) {
                        try {
                            int dateDigit = Integer.valueOf(str);
                            return str;
                        } catch (Exception ex) {
                            logger.error("cannot parse release data from minor version:" + minorVersion);
                        }
                    }
                }
            }
        }

        return null;
    }

    public String getAndCheckMinorVersionTag(CustInstanceDO custins) throws RdsException {

        CustinsParamDO minorVersionTagParam = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_TAG_KEY);
        //polarx-hatp会自己打标
        if (minorVersionTagParam != null) {
            return minorVersionTagParam.getValue();
        }

        //只读或者备用只读，都根据主实例查询minorVersionTag
        if (custins.isReadOrBackup()) {
            custins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
        }

        ClustersDO clustersDO = clusterService.getClusterByClusterName(custins.getClusterName());
        boolean isDhg = clustersDO != null && clustersDO.getType() != null && (clustersDO.getType() > 0);

        Integer kindCode = custins.getKindCode();
        String dbVersion = custins.getDbVersion();
        String category = instanceService.getInstanceLevelByLevelId(custins.getLevelId()).getCategory();

        boolean isPolarxHatp = checkCustinsIsPolarxHatp(custins);

        boolean isPhysical = kindCode == KindCode.physical.getCode().intValue();
        boolean isEcsSingle = kindCode == KindCode.onecs.getCode().intValue();
        boolean isDockerOnEcs = (kindCode == KindCode.dockeronecs.getCode().intValue()) || (kindCode == KindCode.dockerlocalssd.getCode().intValue());
        //OnK8s架构，集团上云，新架构，都是18，kindcode=18的，都是OnEcs的，没有物理机
        boolean isPod = kindCode == KindCode.poddefault.getCode().intValue();

        if (isPhysical && InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(category)) {
            return MINOR_VERSION_TAG_ALISQL_RPM;
        } else if (isPhysical && CATEGORY_ENTERPRISE.equalsIgnoreCase(category) && DB_VERSION_MYSQL_56.equalsIgnoreCase(dbVersion)) {
            return MINOR_VERSION_TAG_ALISQL_RPM;
        } else if (isPhysical && CATEGORY_ENTERPRISE.equalsIgnoreCase(category) && !DB_VERSION_MYSQL_56.equalsIgnoreCase(dbVersion) && !isPolarxHatp) {
            return MINOR_VERSION_TAG_XCLUSTER_RPM;
        } else if (isPhysical && CATEGORY_ENTERPRISE.equalsIgnoreCase(category) && !DB_VERSION_MYSQL_56.equalsIgnoreCase(dbVersion) && isPolarxHatp) {
            return MINOR_VERSION_TAG_XCLUSTER_RPM_POLARX_HATP;
        } else if (isEcsSingle && InstanceSupport.CATEGORY_BASIC.equalsIgnoreCase(category)) {
            return MINOR_VERSION_TAG_ALISQL_ECS_IMAGE_SINGLE;
        } else if (isDockerOnEcs && InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(category) && !isDhg) {
            return MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE;
        } else if (isDockerOnEcs && InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(category) && isDhg) {
            return MINOR_VERSION_TAG_ALISQL_DHG_DOCKER_IMAGE;
        } else if (isPod && InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(category) && isDhg) {
            return MINOR_VERSION_TAG_XCLUSTER_DHG_DOCKER_IMAGE;
        }
        //单写/多写，只能根据实例打标或者release_date查询
        else if (isPod
                && (InstanceSupport.CATEGORY_STANDARD.equalsIgnoreCase(category) || InstanceLevel.CategoryEnum.CLUSTER.toString().equalsIgnoreCase(category))
                && !isDhg) {

            CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
            if (custinsParamDO != null) {
                String releaseDate = parseReleaseDateFromMinorVersion(custinsParamDO.getValue());
                if (releaseDate == null) {
                    throw new RdsException(ErrorCode.MINOR_VERSION_TAG_NOT_FOUND_BY_CUSTINS);
                }
                List<MinorVersionReleaseDO> minorVersionReleaseDOS = minorVersionService.querySpecifyMinorVersionListByCondition(custins.getDbType(), dbVersion, kindCode, category, null, releaseDate);
                if (minorVersionReleaseDOS != null && minorVersionReleaseDOS.size() > 0) {
                    return minorVersionReleaseDOS.get(0).getTag();
                }
            }
        }
        throw new RdsException(ErrorCode.MINOR_VERSION_TAG_NOT_FOUND_BY_CUSTINS);
    }


    public boolean checkCustinsIsPolarxHatp(CustInstanceDO custins){

        if (custins.isReadOrBackup()) {
            custins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
        }
        CustinsParamDO isPolarxHatp = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_NAME_IS_POLARX_HATP);
        if(isPolarxHatp != null && "1".equalsIgnoreCase(isPolarxHatp.getValue())){
            return true;
        }
        CustinsParamDO minorVersionTagParam = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_TAG_KEY);
        if(minorVersionTagParam != null && MINOR_VERSION_TAG_XCLUSTER_RPM_POLARX_HATP.equalsIgnoreCase(minorVersionTagParam.getValue())){
            return true;
        }
        return false;
    }



    @Deprecated
    private String chooseAliSQLDefaultSepcTag(String dbVersion) {
        if (StringUtils.equals(DB_VERSION_MYSQL_80, dbVersion)) {
            //alisql8.0默认版本策略，通过开关来控制是否使用beta版本
            try {
                // 如果用户ID在白名单内，或uidRatio小于灰度比例，则返回创新版内核
                if (userGrayService.isSwitchGray("USE_ALISQL_80_BETA")) {
                    return ServiceTag.TAG_ALISQL_BETA_DOCKER_IMAGE.getTagPrefix();
                } else {
                    return ServiceTag.TAG_ALISQL_DOCKER_IMAGE.getTagPrefix();
                }
            } catch (Exception e) {
                logger.error(e);
                return ServiceTag.TAG_ALISQL_DOCKER_IMAGE.getTagPrefix();
            }


        }
        return ServiceTag.TAG_ALISQL_DOCKER_IMAGE.getTagPrefix();
    }


    /**
     * 根据指定的版本的Tag，
     * 找到所属的镜像业务分组
     *
     * @param specTag
     * @return
     */
    private String findImageBizType(String specTag) {
        for (ServiceTag serviceTag : ServiceTag.values()) {
            if (StringUtils.startsWith(specTag, serviceTag.getTagPrefix())) {
                return serviceTag.getBizType();
            }
        }
        return null;
    }

    /**
     * 根据service spec tag找 release date
     *
     * @param tag alisql_cloud_disk_docker_image_20240731
     * @return 20240731
     * @throws RdsException
     */
    public String extractReleaseDate(String tag) throws RdsException {
        int index = Objects.requireNonNull(tag).lastIndexOf("_");
        if (index == -1) {
            logger.warn("Invalid spec tag format: {}", tag);
            throw new RdsException(INVALID_MINOR_VERSION);
        }
        return tag.substring(index + 1);
    }


    public boolean isChangeSubType(ReplicaSet originReplicaSet, String targetMinorVersion) throws RdsException, ApiException {
        if (StringUtils.isBlank(targetMinorVersion)) {
            return false;
        }
        String originalSpecTag = getServiceSpecTagByReplicaSetName(UUID.randomUUID().toString(), originReplicaSet.getName());
        String subtype = ServiceTag.getSubtypeByServiceSpecTag(originalSpecTag);
        String targetSubType = parseSubTypeFromMinorVersion(targetMinorVersion);
        return !StringUtils.equals(subtype, targetSubType);
    }

    public void checkCrossTagRules(ReplicaSet replicaSet, String originTag, String targetTag, String uid) throws ExecutionException, RdsException, ApiException {
        String crossTagRules = resourceCache.get(CROSS_TAG_RULES_KEY, new Callable<String>() {
            @Override
            public String call() throws Exception {
                ResourceDO resource = resourceService.getResourceByResKey(CROSS_TAG_RULES_KEY);
                if (resource == null || StringUtils.isBlank(resource.getRealValue())) {
                    return JSON.toJSONString(Collections.emptyList());
                }
                return resource.getRealValue();
            }
        });
        Gson gson = new Gson();
        Map<String, List<CrossTagRule>> crossTagRuleMap =
                gson.fromJson(crossTagRules, new TypeToken<HashMap<String, List<CrossTagRule>>>() {
                }.getType());
        if (crossTagRuleMap.containsKey(originTag)) {
            List<CrossTagRule> crossTagRuleList = crossTagRuleMap.get(originTag);
            CrossTagRule crossTagRule = crossTagRuleList.stream().filter(c -> StringUtils.equals(c.getLegalTargetTag(), targetTag)).findFirst().orElse(null);
            if (crossTagRule == null) {
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }
            logger.info("crossTagRule:" + JSON.toJSONString(crossTagRule));
            if (!crossTagRule.getUids().contains(uid) && !crossTagRule.getUids().contains("*")) {
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }
            Set<String> legalArch = crossTagRule.getArch();
            Set<String> legalCategory = crossTagRule.getCategory();

            Set<String> archs = new HashSet<>();
            Set<String> categorys = new HashSet<>();
            InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient()
                    .getInstanceLevel(RequestSession.getRequestId(), replicaSet.getService(), replicaSet.getServiceVersion(), replicaSet.getClassCode(), null);
            categorys.add(instanceLevel.getCategory().toString());
            archs.add(PodCommonSupport.isArm(instanceLevel) ? "arm" : "x86");
            boolean isPrimary = ReplicaSet.InsTypeEnum.MAIN.equals(replicaSet.getInsType());
            if (isPrimary) {
                List<CustInstanceDO> readinsList = custinsService
                        .getReadCustInstanceListByPrimaryCustinsId(replicaSet.getId().intValue(), false);
                for (CustInstanceDO readins : readinsList) {
                    InstanceLevelDO readInstanceLevel = instanceService.getInstanceLevelByLevelId(readins.getLevelId());
                    categorys.add(readInstanceLevel.getCategory().toString());
                    archs.add(PodCommonSupport.isArm(readInstanceLevel.getExtraInfo()) ? "arm" : "x86");
                }
            }
            if ((!legalArch.containsAll(archs) && !legalArch.contains("*")) || (!legalCategory.containsAll(categorys) && !legalCategory.contains("*"))) {
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }
        } else {
            throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
        }
    }


}
