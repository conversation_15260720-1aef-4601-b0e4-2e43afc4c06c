package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.ExtendedLogPlanDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.backup.DescribeBackupPolicyParam;
import com.aliyun.dba.base.parameter.backup.DescribeInstanceCrossBackupPolicyParam;
import com.aliyun.dba.base.response.backup.DescribeBackupPolicyResponse;
import com.aliyun.dba.base.response.backup.DescribeInstanceCrossBackupPolicyResponse;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.entity.BlueGreenDeploymentRel;
import com.aliyun.dba.custins.idao.BlueGreenDeploymentRelServiceIDao;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.dockerdefault.service.DockerCommonService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.utils.CheckUtils;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_TDDL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.*;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.*;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.*;
import static com.aliyun.dba.support.property.ParamConstants.*;


/**
 * 变配资源评估接口
 */
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("poddefaultEvaluateModifyRegionResourceImpl")
public class EvaluateModifyRegionResourceImpl implements IAction {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateModifyRegionResourceImpl.class);

    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    protected DBaasMetaService dBaasMetaService;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private PodCommonSupport podCommonSupport;
    @Resource
    private DockerCommonService dockerCommonService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private BakService bakService;
    @Resource
    private DbsGateWayService dbsGateWayService;
    @Resource
    private MysqlEncryptionService mysqlEncryptionService;
    @Resource
    private RundPodSupport rundPodSupport;
    @Resource
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Resource(name = "physicalEvaluateModifyRegionResourceImpl")
    private com.aliyun.dba.physical.action.EvaluateModifyRegionResourceImpl evaluateModifyRegionResource;

    @Autowired
    private BlueGreenDeploymentRelServiceIDao blueGreenDeploymentRelServiceIDao;
    @Resource
    private ResourceScheduleHelper resourceScheduleHelper;

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            ReplicaSet replicaSetMeta = replicaSetService.getAndCheckUserReplicaSet(params);

            BlueGreenDeploymentRel asBlue = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, replicaSetMeta.getId(), null);
            BlueGreenDeploymentRel asGreen = blueGreenDeploymentRelServiceIDao.getBlueGreenDeploymentRel(null, null, null, replicaSetMeta.getId());
            if (asBlue != null || asGreen != null) {
                logger.error("There is a blue-green deployment, and modify spec is not allowed");
                throw new RdsException(UNSUPPORTED_BY_BLUE_GREEN_DEPLOYMENT);
            }

            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String region = mysqlParamSupport.getAndCheckRegion(params);
            String dbType = replicaSetMeta.getService();
            String dbVersion = replicaSetMeta.getServiceVersion();
            String classCode = replicaSetMeta.getClassCode();
            String targetClassCode;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
                targetClassCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            } else {
                targetClassCode = classCode;
            }
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, null);

            Integer diskSize = replicaSetMeta.getDiskSizeMB() / 1024;
            Integer targetDiskSize;
            Integer diskSizeBeforeCompression = null;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.STORAGE)) {
                targetDiskSize = CheckUtils.parseInt(
                        mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE),
                        CustinsSupport.ESSD_MIN_DISK_SIZE, 102400, ErrorCode.INVALID_STORAGE);
                diskSizeBeforeCompression = targetDiskSize;
            } else {
                targetDiskSize = diskSize;
            }
            boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())
                    || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType());
            String targetDiskType = getParameterValue(params, "StorageType");

            // compression mode evaluate and limit
            String srcCompressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), null);
            String paramCompressionMode = mysqlParamSupport.getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_MODE);
            String targetCompressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), paramCompressionMode);
            boolean isCompressionModeChange = !StringUtils.equals(srcCompressionMode, targetCompressionMode);
            if (CloudDiskCompressionHelper.isCompressionModeOn(targetCompressionMode)) {
                cloudDiskCompressionHelper.checkCompressionSupportLimit(requestId, targetInstanceLevel, dbType, uid, regionId, Optional.ofNullable(diskSizeBeforeCompression).map(Integer::longValue).orElse(null), targetDiskType, true);
                // check readIns,  if open master, should open readIns first.
                if (!isReadIns) {
                    cloudDiskCompressionHelper.checkReadInsCompressionModeOn(requestId, replicaSetMeta.getName(), true);
                }
            }
            if (CloudDiskCompressionHelper.isCompressionModeOn(srcCompressionMode)) {
                Double compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, replicaSetMeta.getName(), null, null, null);
                targetDiskSize = CloudDiskCompressionHelper.getLogicalSize(targetDiskSize, compressionRatio);
            }

            if (isCompressionModeChange && CloudDiskCompressionHelper.isCompressionModeOff(targetCompressionMode)) {
                // todo: modify error code
                String errMsg = "cloud compression cannot be closed.";
                logger.warn("{} - {}", requestId, errMsg);
                throw new RdsException(INTERNAL_FAILURE);
            }

            // 云盘缩容限制评估，避免下降配订单后才发现不能缩容
            if (targetDiskSize < diskSize) {
                if (!podCommonSupport.isSupportShrinkV2()) {
                    return evaluateShrinkResource(requestId, replicaSetMeta, diskSize, targetDiskSize, params);
                }
                return evaluateShrinkResourceV2(requestId, replicaSetMeta, diskSize, targetDiskSize, params);
            }

            //节点级资源评估
            String dbNode = mysqlParamSupport.getDbNode(params);
            if (MysqlParamSupport.isCluster(replicaSetMeta.getCategory()) && StringUtils.isNotBlank(dbNode)){
                return evaluateDbNodeResource(replicaSetMeta, params);
            }


            String insTypeDesc = replicaSetMeta.getInsType().toString();

            String clusterName = replicaSetMeta.getResourceGroupName();
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);


            ReplicaSet.BizTypeEnum bizType = replicaSetMeta.getBizType();

            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            Optional<Replica> optReplica = currentReplicas.stream().filter(replica -> Replica.RoleEnum.MASTER.equals(replica.getRole())).findFirst();
            Replica masterReplica = optReplica.isPresent() ? optReplica.get() : currentReplicas.size() > 0 ? currentReplicas.get(0) : null;
            boolean isClassCodeChange = !classCode.equals(targetClassCode);
            boolean isDiskSizeChange = !diskSize.equals(targetDiskSize);
            // 如果规格和磁盘没有变化, 则这个操作为迁移操作
            boolean isTransIns = !isClassCodeChange && !isDiskSizeChange;

            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(),
                    replicaSetMeta.getServiceVersion(), replicaSetMeta.getClassCode(), null);
            if (StringUtils.isBlank(targetDiskType)) {
                targetDiskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE,
                        masterReplica != null ? masterReplica.getStorageType().toString() : podParameterHelper.getDiskType(targetInstanceLevel));
            }

            if (replicaSetService.isMgr(RequestSession.getRequestId(), replicaSetMeta.getName())) {
                replicaSetService.isSupportMgr(listReplicasInReplicaSet.getItems().size(), targetInstanceLevel, null);
            }
            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());

            boolean isInnovationKernel = serviceSpecTag.startsWith(MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE);
            boolean isBasicToHa = InstanceLevel.CategoryEnum.BASIC.equals(srcInstanceLevel.getCategory()) && InstanceLevel.CategoryEnum.STANDARD.equals(targetInstanceLevel.getCategory());
            boolean isHaToCluster = InstanceLevel.CategoryEnum.STANDARD.equals(srcInstanceLevel.getCategory()) && InstanceLevel.CategoryEnum.CLUSTER.equals(targetInstanceLevel.getCategory());
            boolean isCategoryChange = srcInstanceLevel.getCategory() != targetInstanceLevel.getCategory();

            if (isHaToCluster) {
                podCommonSupport.checkHaToClusterCondition(requestId, replicaSetMeta, srcInstanceLevel);
            }

            // TDE开启实例不允许变配到其他系列
            String tdeEnabled = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetMeta.getName(), PodDefaultConstants.TDE_ENABLED);
            if (StringUtils.equalsIgnoreCase(tdeEnabled, "1") && isCategoryChange) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TDE_STATUS);
            }

            // innovation kernel filter
            // forbid upgrading to standard levels
            if (isInnovationKernel && isCategoryChange) {
                return createErrorResponse(ErrorCode.UNSUPPORTED_MINOR_VERSION);
            }

            boolean isFromK8sBasicToPhysicalHA = "5.7".equals(replicaSetMeta.getServiceVersion())
                    && isBasicToHa
                    && targetInstanceLevel.getHostType() == 0;

            if (isFromK8sBasicToPhysicalHA) {
                final Map<String, Object> ret = this.evaluateModifyRegionResource.doActionRequest(custins, params);
                // return object of physical does not contain key 'Engine'
                // put here to prevent unexpected behavior
                ret.put("Engine", replicaSetMeta.getService());
                return ret;
            }

            dockerCommonService.checkReadOnlyDiskSize(custins, String.valueOf(targetDiskSize));

            //目标磁盘
            String targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);

            if (PodCommonSupport.isArchChange(srcInstanceLevel, targetInstanceLevel)) {
                // 如果是架构变更，检查一下目标版本是否存在
                String sourceMinorVersion = dBaasMetaService.getDefaultClient().getReplicaSetLabel(requestId, replicaSetMeta.getName(), "minor_version");
                serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                        sourceMinorVersion,
                        replicaSetMeta.getBizType(),
                        replicaSetMeta.getService(),
                        replicaSetMeta.getServiceVersion(),
                        "MySQL",
                        KIND_CODE_NEW_ARCH,
                        targetInstanceLevel,
                        targetDiskType,
                        isDhg,
                        true);

                if (MysqlParamSupport.isCluster(srcInstanceLevel.getCategory().toString())) {
                    throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE, "cluster is not supported yet!");
                }
            }


            //原磁盘
            String srcDiskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
            String srcPerformanceLevel = replicaSetService.getVolumePerfLevel(requestId, replicaSetMeta.getName(), srcDiskType);
            // set src disk if target is null
            targetDiskType = StringUtils.isBlank(targetDiskType) ? srcDiskType : targetDiskType;
            // set src performance level if target pl is null and targetDiskType is essd
            targetPerformanceLevel = StringUtils.equals(ECS_ClOUD_ESSD, targetDiskType) && StringUtils.isBlank(targetPerformanceLevel) ? srcPerformanceLevel : targetPerformanceLevel;
            boolean isDiskTypeChange = !srcDiskType.equals(targetDiskType) && !StringUtils.equals(srcPerformanceLevel, targetPerformanceLevel);
            boolean isOnlyDiskSizeChange = isDiskSizeChange && !isClassCodeChange && !isDiskTypeChange;
            // add autopl limit for AliYun instance
            boolean isOnlyModifyAutoPLInfo = false;
            boolean isOnlyModifyColdData = false;
            boolean isOptimizedWritesChange = false;

            if (mysqlParamSupport.hasParameter(params, ParamConstants.OPTIMIZED_WRITES)) {
                String optimizedWrites = mysqlParamSupport.getParameterValue(params, ParamConstants.OPTIMIZED_WRITES, null);
                if (StringUtils.isBlank(optimizedWrites) || (!PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites) && !PodDefaultConstants.optimizedWritesTypeEnum.none.name().equals(optimizedWrites))) {
                    return createErrorResponse(ErrorCode.INVALID_PARAMETERS);
                }
                boolean srcOptimizedWrites = podCommonSupport.isOptimizedWrites(dBaasMetaService.getDefaultClient().getReplicaSetLabel(
                        requestId, replicaSetMeta.getName(), OPTIMIZED_WRITES_INFO));
                boolean targetOptimizedWrites = PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites);
                isOptimizedWritesChange = srcOptimizedWrites != targetOptimizedWrites;
                if (isOptimizedWritesChange && (isClassCodeChange || isDiskSizeChange || isDiskTypeChange)) {
                    logger.info(requestId + "modify optimized writes can not change other things");
                    throw new RdsException(INVALID_PARAMETER_VALUE);
                }
                if (PodDefaultConstants.optimizedWritesTypeEnum.optimized.name().equals(optimizedWrites)) {
                    podCommonSupport.checkOptimizedWritesCondition(requestId, dbVersion, srcDiskType);
                }
            }

            if (ECS_ClOUD_AUTO.equalsIgnoreCase(targetDiskType)&&PodParameterHelper.isAliYun(replicaSetMeta.getBizType()))
            {
                boolean isAutoPLConfigChange = isAutoPLConfigChange(requestId, replicaSetMeta, params);
                boolean isColdDataChange = isColdDataChange(requestId, replicaSetMeta, params);
                boolean isIoAccelerationChange = isIoAccelerationChange(requestId, replicaSetMeta, params);
                boolean backupPolicyLimit = checkBackupLimit(requestId, replicaSetMeta, regionId, bid, uid, isColdDataChange, params);
                String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
                if(Boolean.parseBoolean(paramColdDataEnabled)){
                    // check cold data limit, include minor version, class code, cloud disk, and backup policy
                    podCommonSupport.checkColdDataSupportLimitWithBackupPolicy(requestId, targetInstanceLevel, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), ECS_ClOUD_AUTO,serviceSpecTag, null, backupPolicyLimit, uid, regionId);
                }
                isOnlyModifyColdData = checkColdDataEnabledChangeLimit(replicaSetMeta, isColdDataChange, isClassCodeChange, isDiskSizeChange, isDiskTypeChange, isAutoPLConfigChange, backupPolicyLimit, isIoAccelerationChange);
                isOnlyModifyAutoPLInfo = checkAutoPLConfigLimit(srcDiskType,isAutoPLConfigChange, isClassCodeChange, isDiskSizeChange, replicaSetMeta);
            }
            // check disk size min size
            if (Objects.nonNull(targetDiskSize)) {
                PodParameterHelper.checkCloudEssdStorageValidByDiskTypeAndPLEVEL(targetDiskType, targetPerformanceLevel, targetDiskSize);
            }


            /**
             * 1. cloud disk only resize, no need to evaluate resource
             * 2. modify essd to autopl or modify autopl parameters, no need to evaluate resource
             * 3. only modify cold data parameters, no need to evaluate resource
             */
            if ((ReplicaSetService.isStorageTypeCloudDisk(srcDiskType) && isOnlyDiskSizeChange)
                    || isOnlyModifyAutoPLInfo || isOnlyModifyColdData) {

                Map<String, Object> result = new HashMap<>();
                result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                result.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                result.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
                return result;
            }

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setInsType(insTypeDesc);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setBizType(bizType.toString());
            replicaSetResourceRequest.setClassCode(targetClassCode);
            replicaSetResourceRequest.setStorageType(targetDiskType);
            replicaSetResourceRequest.setDiskSize(targetDiskSize);
            replicaSetResourceRequest.setSubDomain(region);
            replicaSetResourceRequest.setRegionId(regionId);
            replicaSetResourceRequest.putLabelsItem(MySQLParamConstants.ACCESS_ID_RESOURCE, getParameterValue(params, MySQLParamConstants.ACCESS_ID));  // 标识请求来源
            // evaluate io acceleration resource
            replicaSetResourceRequest.setGeneralCloudDisk(podCommonSupport.setGeneralCloudDiskConfig(requestId, params, dbVersion, targetInstanceLevel, targetDiskType, null, replicaSetMeta.getName()));

            // FIXME: 配置变配调度方式
            //  ModifyReplicaSetResourceRequest.ModifyModeEnum modifyModeEnum =
            // podParameterHelper.getModifyModeEnum(
            // requestId, replicaSetMeta,
            // srcInstanceLevel, targetInstanceLevel,
            // srcDiskType, targetDiskType, isDhg);
            // replicaSetResourceRequest.setModifyMode(modifyModeEnum);

            if (isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
            }
            replicaSetResourceRequest.setComposeTag(serviceSpecTag);


            boolean isTargetSingleTenant = replicaSetService.isCloudSingleTenant(bizType, targetDiskType, targetInstanceLevel, isDhg);
            boolean isSrcSingleTenant = replicaSetService.isCloudSingleTenant(bizType, srcDiskType, srcInstanceLevel, isDhg);
            boolean isResourceGuaranteeUid = false;
            //单租户 to 单租户白名单用户申请资源算力保障
            if (isSrcSingleTenant && isTargetSingleTenant) {
                replicaSetResourceRequest.setReplicaSetName(replicaSetMeta.getName());
                Pair<String, String> resourceEnsurancePair = resourceScheduleHelper.makeResourceGuaranteeStrategy(uid, replicaSetMeta.getName());
                if (resourceEnsurancePair != null) {
                    isResourceGuaranteeUid = true;
                    Map<String, String> resourceLabel = new HashMap<>();
                    resourceLabel.put(resourceEnsurancePair.getLeft(), resourceEnsurancePair.getRight());
                    if (MapUtils.isNotEmpty(replicaSetResourceRequest.getLabels())) {
                        resourceLabel.putAll(replicaSetResourceRequest.getLabels());
                    }
                    replicaSetResourceRequest.setLabels(resourceLabel);
                    logger.info("use resource ensure.insName:{},requestId:{},uid:{}", replicaSetMeta.getName(), requestId, uid);
                }
            }

            PodType podType = podCommonSupport.getReplicaSetRuntimeType(replicaSetMeta.getName(), targetInstanceLevel);

            //多租户查询实例是否指定调度模板
//            replicaSetResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant));
            // 云上统一取系统模板
            if (PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                replicaSetResourceRequest.setScheduleTemplate(podTemplateHelper.getBizSysScheduleTemplate(
                        podType,
                        replicaSetMeta.getBizType(),
                        replicaSetMeta.getService(),
                        targetInstanceLevel,
                        isTargetSingleTenant,
                        replicaSetMeta.getInsType().getValue(),
                        // 变配评估不要传打散key，否则集群版多节点评估时会不通过
                        null,
                        replicaSetMeta.getPrimaryInsName(), podParameterHelper.getUidByLoginId(replicaSetMeta.getUserId())).getValue());
            } else {
                replicaSetResourceRequest.setScheduleTemplate(podTemplateHelper.getReplicaSetScheduleTemplate(podTemplateHelper.getReplicaSetPodScheduleTemplate(replicaSetMeta)));
            }


            // 多租户场景不允许创建使用用户密钥的云盘加密实例。
            mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId,replicaSetMeta,isTargetSingleTenant);

            // Build replicaResource
            Map<String, String> hostNameMapping = mysqlParamSupport.getHostNameMapping(params);
            List<ReplicaResourceRequest> replicas = new ArrayList<>();
            for (Replica currentReplica : currentReplicas) {
                // 变配场景Logger角色不需要
                if (Replica.RoleEnum.LOGGER.equals(currentReplica.getRole()) &&
                        !isLoggerNeedTrans(isTransIns, targetDiskType, srcInstanceLevel, targetInstanceLevel)) {
                    continue;
                }
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();

                if (Replica.RoleEnum.LOGGER.equals(currentReplica.getRole())) {
                    InstanceLevelListResult instanceLevels = dBaasMetaService
                            .getDefaultClient().listInstanceLevelChildren(requestId, dbType, dbVersion, classCode);
                    InstanceLevel loggerLevel = instanceLevels.getItems().stream().filter(x -> x.getClassCode().contains("logger")).collect(Collectors.toList()).get(0);
                    replicaResourceRequest.setClassCode(loggerLevel.getClassCode());
                    replicaResourceRequest.setDiskSize(loggerLevel.getDiskSizeMB() / 1024);
                } else {
                    replicaResourceRequest.setClassCode(targetClassCode);
                    if (ReplicaSetService.isStorageTypeCloudDisk(targetDiskType)) {
                        //云盘需要有赠送
                        int extendDiskSize = podParameterHelper.getExtendDiskSizeGBForPod(bizType, false, targetDiskSize);
                        replicaResourceRequest.setDiskSize(extendDiskSize);

                        VolumeSpec dataVolumeSpec = new VolumeSpec();
                        dataVolumeSpec.setName("data");
                        dataVolumeSpec.setCategory("data");
                        dataVolumeSpec.setPerformanceLevel(targetPerformanceLevel);
                        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(dataVolumeSpec));
                    } else {
                        replicaResourceRequest.setDiskSize(targetDiskSize);
                    }
                }
                replicaResourceRequest.setRole(currentReplica.getRole().toString());
                replicaResourceRequest.setStorageType(targetDiskType);
                replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
                replicaResourceRequest.setZoneId(currentReplica.getZoneId());
                if (!Objects.isNull(hostNameMapping) && hostNameMapping.containsKey(currentReplica.getId().toString())) {
                    replicaResourceRequest.setHostName(hostNameMapping.get(currentReplica.getId().toString()));
                }
                if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                    rundPodSupport.completeReplicaNetworkConfig(replicaResourceRequest, currentReplica);
                }
                ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, currentReplica.getId(), false);
                if (isSrcSingleTenant && isTargetSingleTenant && isResourceGuaranteeUid) {
                    replicaResourceRequest.setSourcePodId(replicaResource.getVpod().getVpodId());
                }
                replicas.add(replicaResourceRequest);
            }
            replicaSetResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaSetResourceRequest.setDiskSize(diskSize);  //用户可见的磁盘空间
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
            if (PodType.POD_ECS_RUND.getRuntimeType().equals(podType.getRuntimeType())) {
                rundPodSupport.completeReplicaSetNetworkConfig(replicaSetResourceRequest);
            }
            ReplicaSet primaryReplicaSet = podCommonSupport.getPrimaryReplicaSet(requestId,replicaSetMeta).getRight();
            replicaSetResourceRequest.setCatagory(primaryReplicaSet.getCategory());
            if (PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) { // set target category for modifying category
                replicaSetResourceRequest.setCatagory(targetInstanceLevel.getCategory().toString());
            }

            Map<String, Object> result = new HashMap<String, Object>(){{
                this.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                this.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
            }};
            // 评估资源
            try {
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            return result;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /*
    实例节点级新增资源评估
     */
    public Map<String, Object> evaluateDbNodeResource(ReplicaSet replicaSetMeta, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        try {
            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String region = mysqlParamSupport.getAndCheckRegion(params);

            //默认取master节点相关信息，若指定replicaId， 则获取replica的信息
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                    requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
            Optional<Replica> optReplica = currentReplicas.stream().filter(replica -> Replica.RoleEnum.MASTER.equals(replica.getRole())).findFirst();
            Replica curReplica = optReplica.isPresent() ? optReplica.get() : currentReplicas.size() > 0 ? currentReplicas.get(0) : null;
            if (curReplica == null){
                throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
            }

            //实例信息获取
            String dbType = replicaSetMeta.getService();
            String dbVersion = replicaSetMeta.getServiceVersion();
            String insTypeDesc = replicaSetMeta.getInsType().toString();
            ReplicaSet.BizTypeEnum bizType = replicaSetMeta.getBizType();
            Integer srcDiskSize = replicaSetMeta.getDiskSizeMB() / 1024;
            String srcClassCode = replicaSetMeta.getClassCode();
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, srcClassCode, null);
            // 保证传下来的实例维度的规格是目标主节点的规格
            String targetClassCode = mysqlParamSupport.getParameterValue(params, "TargetDbInstanceClass");
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, null);

            String srcDiskType = curReplica.getStorageType().toString();
            String srcPerformanceLevel = replicaSetService.getReplicaVolumePerfLevel(requestId, curReplica);

            // Get request disk info
            String targetDiskType = mysqlParamSupport.getAndCheckStorageType(params);
            if (StringUtils.isBlank(targetDiskType)) {
                targetDiskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE);
            }
            String targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
            // set src disk if target is null
            targetDiskType = StringUtils.isBlank(targetDiskType) ? srcDiskType : targetDiskType;
            // set src performance level if target pl is null and targetDiskType is essd
            targetPerformanceLevel = StringUtils.equals(ECS_ClOUD_ESSD, targetDiskType) && StringUtils.isBlank(targetPerformanceLevel) ? srcPerformanceLevel : targetPerformanceLevel;

            Integer targetDiskSize = mysqlParamSupport.getAndCheckStorage(params);
            String compressionMode = cloudDiskCompressionHelper.getCompressionMode(requestId, replicaSetMeta.getName(), null);
            Double compressionRatio = cloudDiskCompressionHelper.getCompressionRatio(requestId, replicaSetMeta.getName(), null, null, null);
            if (Objects.nonNull(targetDiskSize) && CloudDiskCompressionHelper.isCompressionModeOn(compressionMode)) {
                targetDiskSize = CloudDiskCompressionHelper.getLogicalSize(targetDiskSize, compressionRatio);
            }
            targetDiskSize = Objects.isNull(targetDiskSize) ? srcDiskSize : targetDiskSize;


            String clusterName = replicaSetMeta.getResourceGroupName();
            Boolean isDhg = mysqlParamSupport.isDHGCluster(clusterName);
            Integer serviceSpecId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetMeta.getName());
            ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, serviceSpecId);
            boolean isSrcSingleTenant = replicaSetService.isCloudSingleTenant(bizType, srcDiskType, srcInstanceLevel, isDhg);
            boolean isTargetSingleTenant = replicaSetService.isCloudSingleTenant(bizType, targetDiskType, targetInstanceLevel, isDhg);

            // 检查云盘容量下限约束，避免退单
            PodParameterHelper.checkCloudEssdStorageValidByDiskTypeAndPLEVEL(targetDiskType, targetPerformanceLevel, targetDiskSize);

            List<Map<String, String>> addNodeList = mysqlParamSupport.getNodesInfo(params);
            List<DBNode> addNodeRoleList = buildDBNodeList(requestId,
                    dbType,
                    dbVersion,
                    targetClassCode,
                    isTargetSingleTenant,
                    targetDiskSize,
                    targetDiskType,
                    targetPerformanceLevel,
                    curReplica.getZoneId(),
                    addNodeList,
                    targetInstanceLevel);

            // 租户类型改变时需要所有节点租户类型改变
            if (isSrcSingleTenant != isTargetSingleTenant && addNodeRoleList.size() != currentReplicas.size()) {
                throw new RdsException(INVALID_MULTI_TENANT);
            }

            // Buid replicaSet resource
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(bid);
            replicaSetResourceRequest.setUid(uid);
            replicaSetResourceRequest.setInsType(insTypeDesc);
            replicaSetResourceRequest.setDbType(dbType);
            replicaSetResourceRequest.setDbVersion(dbVersion);
            replicaSetResourceRequest.setBizType(bizType.toString());
            replicaSetResourceRequest.setClassCode(targetClassCode);
            replicaSetResourceRequest.setStorageType(targetDiskType);
            replicaSetResourceRequest.setDiskSize(targetDiskSize);
            replicaSetResourceRequest.setSubDomain(region);
            replicaSetResourceRequest.setRegionId(regionId);

            if (isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(clusterName);
            }
            replicaSetResourceRequest.setComposeTag(serviceSpec.getTag());
            //多租户查询实例是否指定调度模板
            replicaSetResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, srcInstanceLevel, isTargetSingleTenant, null)
            );

            // Build replicaResource
            List<ReplicaResourceRequest> replicas = new ArrayList<>();
            for (DBNode node : addNodeRoleList) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                replicaResourceRequest.setClassCode(node.getClassCode());
                if (ReplicaSetService.isStorageTypeCloudDisk(node.getDiskType())) {
                    //云盘需要有赠送
                    int extendDiskSize = podParameterHelper.getExtendDiskSizeGBForPod(bizType, false, node.getDiskSize());
                    replicaResourceRequest.setDiskSize(extendDiskSize);

                    VolumeSpec dataVolumeSpec = new VolumeSpec();
                    dataVolumeSpec.setName("data");
                    dataVolumeSpec.setCategory("data");
                    dataVolumeSpec.setPerformanceLevel(node.getDiskPerformanceLevel());
                    replicaResourceRequest.setVolumeSpecs(Collections.singletonList(dataVolumeSpec));
                } else {
                    replicaResourceRequest.setDiskSize(node.getDiskSize());
                }

                replicaResourceRequest.setRole(node.getRole());
                replicaResourceRequest.setStorageType(node.getDiskType());
                replicaResourceRequest.setSingleTenant(node.getIsSingleTenant());
                replicaResourceRequest.setZoneId(node.getZoneId());
                replicas.add(replicaResourceRequest);
            }

            replicaSetResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaSetResourceRequest.setDiskSize(targetDiskSize);
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);

            ReplicaSet primaryReplicaSet = podCommonSupport.getPrimaryReplicaSet(requestId,replicaSetMeta).getRight();
            replicaSetResourceRequest.setCatagory(primaryReplicaSet.getCategory());

            Map<String, Object> result = new HashMap<String, Object>(){{
                this.put(ENGINE_VERSION, srcInstanceLevel.getServiceVersion());
                this.put(ParamConstants.ENGINE, CustinsSupport.getEngine(srcInstanceLevel.getService()));
            }};
            // 评估资源
            try {
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            return result;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    /**
     * 判断是否Logger节点需要做跨机
     *
     * @param isTransIns
     * @param diskType
     * @param srcLevel
     * @param targetLevel
     * @return
     */
    private boolean isLoggerNeedTrans(boolean isTransIns,
                                      String diskType,
                                      InstanceLevel srcLevel,
                                      InstanceLevel targetLevel) {
        if (isTransIns) {
            //迁移场景需要做跨机
            return true;
        }
        //云盘版需要判断Logger节点是否做迁移
        //单租户 -> 多租户 or 多租户 -> 单租户
        InstanceLevel.IsolationTypeEnum srcIsolationTypeEnum = srcLevel.getIsolationType();
        InstanceLevel.IsolationTypeEnum destIsolationTypeEnum = targetLevel.getIsolationType();
        if (replicaSetService.isStorageTypeCloudDisk(diskType) && srcIsolationTypeEnum != destIsolationTypeEnum) {
            return true;
        }
        return false;
    }

    /*
    构造节点级的配置参数， (todo) 设置replicaId，配置获取为指定replica的配置
     */
    private List<DBNode> buildDBNodeList(String requestId, String dbType, String dbVersion,
                                         String targetClassCode, Boolean isTargetSingleTenant, Integer targetDiskSize,
                                         String targetDiskType, String targetPerformanceLevel,
                                         String srcZoneId, List<Map<String, String>> nodeInfos,
                                         InstanceLevel targetInstanceLevel) throws Exception{
        Integer nodeNum = nodeInfos.size();
        String category = targetInstanceLevel.getCategory().toString();
        List<Replica.RoleEnum> roles = new ArrayList<Replica.RoleEnum>() {{
            this.add(Replica.RoleEnum.MASTER);
            for (int i = 0; i < nodeNum - 1; i++) {
                this.add(Replica.RoleEnum.SLAVE);
            }
        }};

        List<DBNode> result = new ArrayList<>();
        for(Map<String, String> nodeInfo: nodeInfos){
            DBNode dbNode = new DBNode();

            String nodeClassCode = nodeInfo.getOrDefault("classCode", targetClassCode);
            logger.info("nodeClassCode = " + nodeClassCode + ", targetClassCode = " + targetClassCode);
            if (!StringUtils.equalsIgnoreCase(nodeClassCode, targetClassCode)){
                //限制原规格租户类型需要相同
                checkDbNodeInfo(requestId, dbType, dbVersion, nodeClassCode, isTargetSingleTenant, category);
            }

            dbNode.setClassCode(nodeClassCode);
            dbNode.setIsSingleTenant(isTargetSingleTenant);
            dbNode.setZoneId(nodeInfo.getOrDefault("zoneId", srcZoneId));
            dbNode.setDiskSize(targetDiskSize);
            dbNode.setDiskType(targetDiskType);
            dbNode.setDiskPerformanceLevel(targetPerformanceLevel);
            dbNode.setRole(roles.get(0).getValue());

            result.add(dbNode);
            roles.remove(0);
        }
        return result;
    }

    private void checkDbNodeInfo(String requestId, String dbType, String dbVersion, String targetClassCode, Boolean isSrcSingleTenant, String category) throws Exception{
        InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, targetClassCode, true);
        if (Objects.isNull(targetInstanceLevel)) {
            logger.error("instance level not found, class code: {}", targetClassCode);
            throw new RdsException(UNSUPPORTED_CLASS_CODE);
        }
        if (!category.equalsIgnoreCase(targetInstanceLevel.getCategory().toString())) {
            logger.info("src classCode category : {}, dest classCode {} category: {} not match", category, targetClassCode, targetInstanceLevel.getCategory().toString());
            throw new RdsException(UNSUPPORTED_CLASS_CODE);
        }
        boolean nodeIsSingle = targetInstanceLevel.getIsolationType() != InstanceLevel.IsolationTypeEnum.COMMON;
        if (isSrcSingleTenant != nodeIsSingle){
            logger.error("src classCode isSingle: {},dest classCode {} isSingle: {} not match", isSrcSingleTenant, targetClassCode, nodeIsSingle);
            throw new RdsException(INVALID_MULTI_TENANT);
        }
    }

    /**
     * check autopl parameter change limit
     * 1. class code and disk size can not change
     * 2. only essd can change to autopl
     * 3. check disk size
     * @param srcDiskType
     * @param isAutoPLConfigChange
     * @param isClassCodeChange
     * @param isDiskSizeChange
     * @param replicaSetMeta
     * @throws Exception
     */
    private boolean checkAutoPLConfigLimit(String srcDiskType,  boolean isAutoPLConfigChange, boolean isClassCodeChange, boolean isDiskSizeChange, ReplicaSet replicaSetMeta) throws Exception
    {
        boolean isOnlyAutoPLConfigChange = false;
        boolean isOnlyESSD2AutoPL = false;
        if(!ECS_ClOUD_AUTO.equalsIgnoreCase(srcDiskType) && !srcDiskType.contains(ECS_ClOUD_ESSD))
        {
            // only essd can change to autopl
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyParam", "only allow essd upgrade to general"});
        }
        if(srcDiskType.contains(ECS_ClOUD_ESSD))
        {
            if(isClassCodeChange||isDiskSizeChange) {
                // class code and disk size can not change
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyDiskTypeWithOthers", "disk type can't modify with disk size and class code"});
            }
            //  check disk size
            Integer diskSize = replicaSetMeta.getDiskSizeMB() / 1024;
            PodParameterHelper.checkCloudEssdStorageValid(ECS_ClOUD_AUTO, diskSize);
            isOnlyESSD2AutoPL = true;
        }
        if(isAutoPLConfigChange){
            if (isClassCodeChange || isDiskSizeChange) {
                // class code and disk size can not change
                throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedModifyParam", "burst param must be only modified"});
            }
            isOnlyAutoPLConfigChange = true;
        }
        /**
         * this result is used to evaluate resource
         */
        return isOnlyAutoPLConfigChange||isOnlyESSD2AutoPL;
    }

    /**
     * check cold data change limit
     * 1. class code, disk size  and autopl parameters can not change
     * 2. read instance coldDataEnabled parameter cannot be modified and must be the same as the main instance
     * @param replicaSetMeta
     * @param isColdDataChange
     * @param isClassCodeChange
     * @param isDiskSizeChange
     * @param isDiskTypeChange
     * @param isAutoPLConfigChange
     * @param backupPolicyLimit
     * @return
     * @throws Exception
     */
    private Boolean checkColdDataEnabledChangeLimit(ReplicaSet replicaSetMeta, boolean isColdDataChange, boolean isClassCodeChange, boolean isDiskSizeChange, boolean isDiskTypeChange, boolean isAutoPLConfigChange, boolean backupPolicyLimit, boolean isIoAccelerationChange) throws Exception {
        if (isColdDataChange) {
            if (isClassCodeChange || isDiskSizeChange || isDiskTypeChange || isAutoPLConfigChange || isIoAccelerationChange) {
                // class code, disk size  and autopl parameters can not change
                throw new RdsException(UNSUPPORTED_MODIFY_COLD);
            }
            boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())
                    || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType());
            if (isReadIns) {
                // read instance coldDataEnabled parameter cannot be modified and must be the same as the main instance
                throw new RdsException(UNSUPPORTED_READINS_MODIFY_COLD);
            }
            return true;
        }
        return false;
    }

    private boolean checkBackupLimit(String requestId, ReplicaSet replicaSetMeta, String regionId, String bid, String uid, boolean isColdDataChange, Map<String, String> params) {
        boolean isReadIns = ReplicaSet.InsTypeEnum.READONLY.equals(replicaSetMeta.getInsType())
                || ReplicaSet.InsTypeEnum.READBACKUP.equals(replicaSetMeta.getInsType());
        if (isReadIns) {
            // read instance has null backup policy
            return false;
        }
        boolean isOpenColdData = isColdDataChange && Boolean.parseBoolean(getParameterValue(params, RDS_COLD_DATA_ENABLED));
        if(!isOpenColdData){
            return false;
        }
        return checkBackupStrategy(requestId, replicaSetMeta.getName(), regionId, bid, uid);
    }

    /**
     * check flash backup and cross backup
     */
    private boolean checkBackupStrategy(String requestId, String replicaSetName, String regionId, String bid, String uid) {
        // flash backup
        boolean flashBackup = false;
        DescribeBackupPolicyParam describeBackupPolicyParam = new DescribeBackupPolicyParam(requestId, bid, uid, regionId, replicaSetName);
        try {
            DescribeBackupPolicyResponse describeBackupPolicyResponse = dbsGateWayService.describeBackupPolicy(describeBackupPolicyParam);
            String category = describeBackupPolicyResponse.getCategory();
            if (Objects.nonNull(category) && "Flash".equalsIgnoreCase(category)) {
                flashBackup = true;
            }
        } catch (Exception e) {
            logger.warn("describeBackupPolicy error, skip check backup strategy, error message : {}", e.getMessage());
        }
        // cross backup
        boolean crossBackup = false;
        DescribeInstanceCrossBackupPolicyParam describeInstanceCrossBackupPolicyParam = new DescribeInstanceCrossBackupPolicyParam(requestId, bid, uid, regionId, replicaSetName);
        try {
            DescribeInstanceCrossBackupPolicyResponse describeInstanceCrossBackupPolicyResponse = dbsGateWayService.describeInstanceCrossBackupPolicy(describeInstanceCrossBackupPolicyParam);
            String crossBackupEnabled = describeInstanceCrossBackupPolicyResponse.getBackupEnabled();
            if (Objects.nonNull(crossBackupEnabled) && "enable".equalsIgnoreCase(crossBackupEnabled)) {
                crossBackup = true;
            }
        } catch (Exception e) {
            logger.warn("describeInstanceCrossBackupPolicy error, skip check backup strategy, error message : {}", e.getMessage());
        }
        return flashBackup || crossBackup;
    }

    /**
     * check autopl config change
     */
    private Boolean isAutoPLConfigChange(String requestId, ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception{
        boolean srcBurstingEnabled = replicaSetService.getAutoConfigBurstingEnabled(requestId, replicaSetMeta.getName(), ECS_ClOUD_AUTO, null);
        String paramBurstingEnabled = getParameterValue(params, ParamConstants.AUTOPL_BURSTING_ENABLED);
        boolean targetBurstingEnabled = replicaSetService.getAutoConfigBurstingEnabled(requestId, replicaSetMeta.getName(), ECS_ClOUD_AUTO, paramBurstingEnabled);

        return (srcBurstingEnabled!=targetBurstingEnabled);
    }

    /**
     * check cold data change
     */
    private Boolean isColdDataChange(String requestId, ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception{
        boolean srcColdDataEnabled = replicaSetService.getColdDataEnabled(requestId, replicaSetMeta.getName(),null);
        String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
        boolean targetColdDataEnabled = replicaSetService.getColdDataEnabled(requestId, replicaSetMeta.getName(),paramColdDataEnabled);
        return (srcColdDataEnabled!=targetColdDataEnabled);
    }

    private Boolean isIoAccelerationChange(String requestId, ReplicaSet replicaSetMeta, Map<String, String> params) throws Exception{
        boolean srcIoAccelerationEnabled = replicaSetService.getIoAccelerationEnabled(requestId, replicaSetMeta.getName(),null);
        String paramIoAccelerationEnabled = getParameterValue(params, IO_ACCELERATION_ENABLED);
        boolean targetIoAccelerationEnabled = replicaSetService.getIoAccelerationEnabled(requestId, replicaSetMeta.getName(),paramIoAccelerationEnabled);
        logger.info("srcIoAccelerationEnabled:{}, paramIoAccelerationEnabled:{}",srcIoAccelerationEnabled, targetIoAccelerationEnabled);
        return srcIoAccelerationEnabled != targetIoAccelerationEnabled;
    }

    /**
     * 缩容资源评估
     */
    public Map<String, Object> evaluateShrinkResource(String requestId, ReplicaSet replicaSetMeta, Integer srcDiskSize, Integer targetDiskSize, Map<String, String> params) throws RdsException {
        try {
            logger.warn("Evaluate Shrink Resource with Version 1");
            // 灰度开关
            if(!podParameterHelper.isSupportShrink()){
                throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            // 校验非云上等情况
            if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                logger.error("not support shrink, isAliyun:{}", PodParameterHelper.isAliYun(replicaSetMeta.getBizType()));
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 校验只读缩容大小
            CustInstanceDO custins = mysqlParamSupport.getAndCheckCustInstance(params);
            dockerCommonService.checkReadOnlyDiskSize(custins, String.valueOf(targetDiskSize));

            // 校验缩容次数
            podParameterHelper.checkShrinkLimit(requestId, replicaSetMeta.getName(), user.getAliUid());

            // 校验不能同时迁移
            Boolean migratingAvz = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false"));
            if (migratingAvz) {
                logger.error("not support shrink and migrate at the same time");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }
            // check logPlan and return extendedLogPlan with local retain
            ExtendedLogPlanDO extendedLogPlanDO = mysqlParamSupport.checkAndGetCustinsBinlogForShrink(custins);

            // 校验不允许没达标的集团TDDL实例缩容
            boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, replicaSetMeta);
            boolean isTDDL = CONN_TYPE_TDDL.equalsIgnoreCase(Objects.requireNonNull(replicaSetMeta.getConnType()).toString());
            if (isTDDL && !isTddlTaskMigrate) {
                logger.error("not support TDDL shrink when tddl task migrate label is false");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 源实例信息
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();

            // 变更参数获取
            Boolean isDhg = mysqlParamSupport.isDHGCluster(replicaSetMeta.getResourceGroupName());
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String region = mysqlParamSupport.getAndCheckRegion(params);
            Integer serviceSpecId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetMeta.getName());
            ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, serviceSpecId);
            String targetClassCode;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
                targetClassCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            } else {
                targetClassCode = replicaSetMeta.getClassCode();
            }
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), targetClassCode, null);
            // 原云盘信息
            String srcDiskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
            String targetDiskType = getParameterValue(params, "StorageType");
            if (StringUtils.isEmpty(targetDiskType)) {
                targetDiskType = srcDiskType;
            }
            String targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            // 各PL等级云盘类型归一
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
            srcDiskType = PodParameterHelper.transferDiskTypeParam(srcDiskType);

            /// 检查可缩容云盘类型
            if (!SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(srcDiskType) || !SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(targetDiskType)) {
                throw new RdsException(INVALID_STORAGE);
            }

            // 检查磁盘类型变更
            if (!StringUtils.equals(targetDiskType, srcDiskType)) {
                throw new RdsException(UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }

            // 校验不能同时改变系列，暂不支持serverless、集群版系列
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), replicaSetMeta.getClassCode(), null);
            if (srcInstanceLevel.getCategory() != targetInstanceLevel.getCategory() || !SHRINK_SUPPORT_CATEGORY_LIST.contains(srcInstanceLevel.getCategory().getValue())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许架构迁移
            boolean isArchChange =  PodCommonSupport.isArchChange(srcInstanceLevel, targetInstanceLevel);
            if (isArchChange) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            String classCode = replicaSetMeta.getClassCode();
            boolean isClassCodeChange = (classCode!=null && !classCode.equals(targetClassCode));
            String srcPerformanceLevel = replicaSetService.getVolumePerfLevel(requestId, replicaSetMeta.getName(), srcDiskType);
            boolean isDiskTypeChange = !srcDiskType.equals(targetDiskType) && !StringUtils.equals(srcPerformanceLevel, targetPerformanceLevel);
            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());

            // add autopl limit for AliYun instance
            if (ECS_ClOUD_AUTO.equalsIgnoreCase(targetDiskType)&&PodParameterHelper.isAliYun(replicaSetMeta.getBizType()))
            {
                boolean isAutoPLConfigChange = isAutoPLConfigChange(requestId, replicaSetMeta, params);
                boolean isColdDataChange = isColdDataChange(requestId, replicaSetMeta, params);
                boolean backupPolicyLimit = checkBackupLimit(requestId, replicaSetMeta, regionId, bid, uid, isColdDataChange, params);
                String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
                if(Boolean.parseBoolean(paramColdDataEnabled)){
                    // check cold data limit, include minor version, class code, cloud disk, and backup policy
                    podCommonSupport.checkColdDataSupportLimitWithBackupPolicy(requestId, targetInstanceLevel, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), ECS_ClOUD_AUTO,serviceSpecTag, null, backupPolicyLimit, uid, regionId);
                }
                checkColdDataEnabledChangeLimit(replicaSetMeta,isColdDataChange,isClassCodeChange,true, isDiskTypeChange, isAutoPLConfigChange, backupPolicyLimit, false);
                checkAutoPLConfigLimit(srcDiskType,isAutoPLConfigChange, isClassCodeChange, true, replicaSetMeta);
            }

            // check usage and local log retain plan
            podParameterHelper.checkShrinkCloudESSDValid(requestId, replicaSetMeta.getId().intValue(), user.getAliUid(), srcDiskSize, targetDiskSize, extendedLogPlanDO);

            // 构建资源请求
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(user.getBid());
            replicaSetResourceRequest.setUid(user.getAliUid());
            replicaSetResourceRequest.setInsType(replicaSetMeta.getInsType().toString());
            replicaSetResourceRequest.setDbType(replicaSetMeta.getService());
            replicaSetResourceRequest.setDbVersion(replicaSetMeta.getServiceVersion());
            replicaSetResourceRequest.setBizType(replicaSetMeta.getBizType().toString());
            replicaSetResourceRequest.setCatagory(targetInstanceLevel.getCategory().toString());
            replicaSetResourceRequest.setClassCode(targetClassCode);
            replicaSetResourceRequest.setStorageType(targetDiskType);
            replicaSetResourceRequest.setDiskSize(targetDiskSize);
            replicaSetResourceRequest.setSubDomain(region);
            replicaSetResourceRequest.setRegionId(regionId);
            replicaSetResourceRequest.setComposeTag(serviceSpec.getTag());
            if (isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(replicaSetMeta.getResourceGroupName());
            }
            boolean isTargetSingleTenant = replicaSetService.isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, isDhg);
            //多租户查询实例是否指定调度模板
            replicaSetResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant, null)
            );
            // 多租户场景不允许创建使用用户密钥的云盘加密实例。
            mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId,replicaSetMeta,isTargetSingleTenant);
            replicaSetResourceRequest.setSingleTenant(isTargetSingleTenant);

            // 构建pod资源请求
            List<ReplicaResourceRequest> replicas = new ArrayList<>();
            for (Replica currentReplica : currentReplicas) {
                ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
                replicaResourceRequest.setRole(currentReplica.getRole().toString());
                replicaResourceRequest.setStorageType(targetDiskType);
                replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
                replicaResourceRequest.setZoneId(currentReplica.getZoneId());
                replicaResourceRequest.setClassCode(targetClassCode);
                // 磁盘信息
                int extendDiskSize = podParameterHelper.getExtendDiskSizeGBForPod(replicaSetMeta.getBizType(), false, targetDiskSize);
                replicaResourceRequest.setDiskSize(extendDiskSize);
                VolumeSpec dataVolumeSpec = new VolumeSpec();
                dataVolumeSpec.setName("data");
                dataVolumeSpec.setCategory("data");
                dataVolumeSpec.setPerformanceLevel(targetPerformanceLevel);
                replicaResourceRequest.setVolumeSpecs(Collections.singletonList(dataVolumeSpec));
                replicas.add(replicaResourceRequest);
            }
            replicaSetResourceRequest.setReplicaResourceRequestList(replicas);
            Map<String, Object> result = new HashMap<String, Object>() {{
                this.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                this.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
            }};
            // 评估资源
            try {
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            return result;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }

    public Map<String, Object> evaluateShrinkResourceV2(String requestId, ReplicaSet replicaSetMeta, Integer srcDiskSize, Integer targetDiskSize, Map<String, String> params) throws RdsException {
        try {
            // 灰度开关
            if(!podParameterHelper.isSupportShrink()){
                throw new RdsException(UNSUPPORTED_REDUCE_DISK_SIZE);
            }

            Integer evaluateNum = mysqlParamSupport.getAndCheckEvaluateNum(params);
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSetMeta.getUserId(), false);
            String bid = user.getBid();
            String uid = user.getAliUid();
            // 校验非云上等情况
            if (!PodParameterHelper.isAliYun(replicaSetMeta.getBizType())) {
                logger.error("not support shrink, isAliyun:{}", PodParameterHelper.isAliYun(replicaSetMeta.getBizType()));
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 校验只读缩容大小
            CustInstanceDO custins = mysqlParamSupport.getAndCheckCustInstance(params);
            dockerCommonService.checkReadOnlyDiskSize(custins, String.valueOf(targetDiskSize));

            // 校验缩容次数
            podParameterHelper.checkShrinkLimit(requestId, replicaSetMeta.getName(), user.getAliUid());

            // 校验不能同时迁移
            Boolean migratingAvz = Boolean.parseBoolean(mysqlParamSupport.getParameterValue(params, PodDefaultConstants.MIGRATING_AVZ, "false"));
            if (migratingAvz) {
                logger.error("not support shrink and migrate at the same time");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }
            // check logPlan and return extendedLogPlan with local retain
            ExtendedLogPlanDO extendedLogPlanDO = mysqlParamSupport.checkAndGetCustinsBinlogForShrink(custins);

            // 校验不允许没达标的集团TDDL实例缩容
            boolean isTddlTaskMigrate = replicaSetService.isTddlTaskMigrate(requestId, replicaSetMeta);
            boolean isTDDL = CONN_TYPE_TDDL.equalsIgnoreCase(Objects.requireNonNull(replicaSetMeta.getConnType()).toString());
            if (isTDDL && !isTddlTaskMigrate) {
                logger.error("not support TDDL shrink when tddl task migrate label is false");
                throw new RdsException(UNSUPPORTED_THIS_DB_TYPE_REDUCE_DISK_SIZE);
            }

            // 源实例信息
            ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(requestId, replicaSetMeta.getName(), null, null, null, null);
            List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();

            // 变更参数获取
            Boolean isDhg = mysqlParamSupport.isDHGCluster(replicaSetMeta.getResourceGroupName());
            String regionId = mysqlParamSupport.getAndCheckRegionID(params);
            String region = mysqlParamSupport.getAndCheckRegion(params);
            Integer serviceSpecId = dBaasMetaService.getDefaultClient().getReplicaSetServiceSpecId(requestId, replicaSetMeta.getName());
            ServiceSpec serviceSpec = dBaasMetaService.getDefaultClient().getServiceSpecById(requestId, serviceSpecId);
            String targetClassCode;
            if (mysqlParamSupport.hasParameter(params, ParamConstants.TARGET_DB_INSTANCE_CLASS)) {
                targetClassCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
            } else {
                targetClassCode = replicaSetMeta.getClassCode();
            }
            InstanceLevel targetInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), targetClassCode, null);
            // src disk type and performance level
            String srcDiskType = replicaSetService.getReplicaSetStorageType(replicaSetMeta.getName(), requestId);
            String srcPerformanceLevel = replicaSetService.getVolumePerfLevel(requestId, replicaSetMeta.getName(), srcDiskType);
            // get target disk type and performance level
            String targetDiskType = mysqlParamSupport.getAndCheckStorageType(params);
            if (StringUtils.isBlank(targetDiskType)) {
                targetDiskType = getParameterValue(params, DB_INSTANCE_STORAGE_TYPE);
            }
            String targetPerformanceLevel = PodParameterHelper.transferCloudDiskPerfLevel(targetDiskType);
            targetDiskType = PodParameterHelper.transferDiskTypeParam(targetDiskType);
            // set src disk if target is null
            targetDiskType = StringUtils.isBlank(targetDiskType) ? srcDiskType : targetDiskType;
            // set src performance level if target pl is null and targetDiskType is essd
            targetPerformanceLevel = StringUtils.equals(ECS_ClOUD_ESSD, targetDiskType) && StringUtils.isBlank(targetPerformanceLevel) ? srcPerformanceLevel : targetPerformanceLevel;

            /// 检查可缩容云盘类型
            if (!SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(srcDiskType) || !SHRINK_SUPPORT_STORAGE_TYPE_LIST.contains(targetDiskType)) {
                throw new RdsException(INVALID_STORAGE);
            }

            // 检查磁盘类型变更
            if (!StringUtils.equals(targetDiskType, srcDiskType)) {
                throw new RdsException(UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }
            // 检查云盘容量下限约束，避免退单
            if (Objects.nonNull(targetDiskSize)) {
                PodParameterHelper.checkCloudEssdStorageValidByDiskTypeAndPLEVEL(targetDiskType, targetPerformanceLevel, targetDiskSize);
            }

            // 校验不能同时改变系列，暂不支持serverless
            InstanceLevel srcInstanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), replicaSetMeta.getClassCode(), null);
            if (srcInstanceLevel.getCategory() != targetInstanceLevel.getCategory() || !SHRINK_SUPPORT_CATEGORY_LIST_V2.contains(srcInstanceLevel.getCategory())) {
                throw new RdsException(ErrorCode.UNSUPPORTED_TARGET_CLASS_CODE);
            }

            // 不允许架构迁移
            boolean isArchChange =  PodCommonSupport.isArchChange(srcInstanceLevel, targetInstanceLevel);
            if (isArchChange) {
                throw new RdsException(UNSUPPORTED_TARGET_CLASS_CODE);
            }

            String classCode = replicaSetMeta.getClassCode();
            boolean isClassCodeChange = (classCode!=null && !classCode.equals(targetClassCode));
            boolean isDiskTypeChange = !srcDiskType.equals(targetDiskType) && !StringUtils.equals(srcPerformanceLevel, targetPerformanceLevel);
            String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTagByCustinsId(requestId, custins.getId());

            // add autopl limit for AliYun instance
            if (ECS_ClOUD_AUTO.equalsIgnoreCase(targetDiskType)&&PodParameterHelper.isAliYun(replicaSetMeta.getBizType()))
            {
                boolean isAutoPLConfigChange = isAutoPLConfigChange(requestId, replicaSetMeta, params);
                boolean isColdDataChange = isColdDataChange(requestId, replicaSetMeta, params);
                boolean backupPolicyLimit = checkBackupLimit(requestId, replicaSetMeta, regionId, bid, uid, isColdDataChange, params);
                String paramColdDataEnabled = getParameterValue(params, RDS_COLD_DATA_ENABLED);
                if(Boolean.parseBoolean(paramColdDataEnabled)){
                    // check cold data limit, include minor version, class code, cloud disk, and backup policy
                    podCommonSupport.checkColdDataSupportLimitWithBackupPolicy(requestId, targetInstanceLevel, replicaSetMeta.getService(), replicaSetMeta.getServiceVersion(), ECS_ClOUD_AUTO,serviceSpecTag, null, backupPolicyLimit, uid, regionId);
                }
                checkColdDataEnabledChangeLimit(replicaSetMeta,isColdDataChange,isClassCodeChange,true, isDiskTypeChange, isAutoPLConfigChange, backupPolicyLimit, false);
                checkAutoPLConfigLimit(srcDiskType,isAutoPLConfigChange, isClassCodeChange, true, replicaSetMeta);
            }

            // check usage and local log retain plan
            podParameterHelper.checkShrinkCloudESSDValid(requestId, replicaSetMeta.getId().intValue(), user.getAliUid(), srcDiskSize, targetDiskSize, extendedLogPlanDO);

            // 构建资源请求
            ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
            replicaSetResourceRequest.setUserId(user.getBid());
            replicaSetResourceRequest.setUid(user.getAliUid());
            replicaSetResourceRequest.setInsType(replicaSetMeta.getInsType().toString());
            replicaSetResourceRequest.setDbType(replicaSetMeta.getService());
            replicaSetResourceRequest.setDbVersion(replicaSetMeta.getServiceVersion());
            replicaSetResourceRequest.setBizType(replicaSetMeta.getBizType().toString());
            replicaSetResourceRequest.setCatagory(targetInstanceLevel.getCategory().toString());
            replicaSetResourceRequest.setClassCode(targetClassCode);
            replicaSetResourceRequest.setStorageType(targetDiskType);
            replicaSetResourceRequest.setDiskSize(targetDiskSize);
            replicaSetResourceRequest.setSubDomain(region);
            replicaSetResourceRequest.setRegionId(regionId);
            replicaSetResourceRequest.setComposeTag(serviceSpec.getTag());
            if (isDhg) {
                replicaSetResourceRequest.setDedicatedHostGroupId(replicaSetMeta.getResourceGroupName());
            }
            boolean isTargetSingleTenant = replicaSetService.isCloudSingleTenant(replicaSetMeta.getBizType(), targetDiskType, targetInstanceLevel, isDhg);
            //多租户查询实例是否指定调度模板
            replicaSetResourceRequest.setScheduleTemplate(
                    podTemplateHelper.getReplicaSetScheduleTemp(replicaSetMeta, targetInstanceLevel, isTargetSingleTenant, null)
            );
            // 多租户场景不允许创建使用用户密钥的云盘加密实例。
            mysqlEncryptionService.checkEncryptionKeyByReplicaSet(requestId,replicaSetMeta,isTargetSingleTenant);
            replicaSetResourceRequest.setSingleTenant(isTargetSingleTenant);

            // 构建pod资源请求,请求两,否则无法模拟预占2快盘的情况,预占标签是replicaSet级别,即使申请两个volume,在common也会因为serviceSpec过滤掉
            List<ReplicaResourceRequest> destReplica = new ArrayList<>();
            Replica replica =  currentReplicas.stream().filter(r -> r.getRole() == Replica.RoleEnum.SLAVE).findFirst().orElse(null);
            replica = null == replica ? currentReplicas.get(0) : replica;
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setRole(replica.getRole().toString());
            replicaResourceRequest.setStorageType(targetDiskType);
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            replicaResourceRequest.setZoneId(replica.getZoneId());
            replicaResourceRequest.setClassCode(targetClassCode);
            // 磁盘信息
            int extendDiskSize = podParameterHelper.getExtendDiskSizeGBForPod(replicaSetMeta.getBizType(), false, targetDiskSize);
            replicaResourceRequest.setDiskSize(extendDiskSize);
            destReplica.add(replicaResourceRequest);
            replicaSetResourceRequest.setReplicaResourceRequestList(destReplica);
            // 预占2块盘
            replicaSetResourceRequest.setReserveDiskCount(2);
            //========================== 第1次评估资源 ==========================
            Map<String, Object> result = new HashMap<String, Object>() {{
                this.put(ENGINE_VERSION, targetInstanceLevel.getServiceVersion());
                this.put(ParamConstants.ENGINE, CustinsSupport.getEngine(targetInstanceLevel.getService()));
            }};
            try {
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    logger.warn("dest ReplicaSet for shrink allocate resource failed.");
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            //========================== 第2次评估资源 ==========================
            List<ReplicaResourceRequest> slaveDestReplicas = new ArrayList<>();
            for (Replica currentReplica : currentReplicas) {
                if (Objects.equals(replica.getId(), currentReplica.getId())){
                    continue;
                }
                ReplicaResourceRequest replicaRequest = new ReplicaResourceRequest();
                replicaRequest.setRole(currentReplica.getRole().toString());
                replicaRequest.setStorageType(targetDiskType);
                replicaRequest.setSingleTenant(isTargetSingleTenant);
                replicaRequest.setZoneId(currentReplica.getZoneId());
                replicaRequest.setClassCode(targetClassCode);
                // 磁盘信息
                replicaRequest.setDiskSize(extendDiskSize);
                VolumeSpec dataVolumeSpec = new VolumeSpec();
                dataVolumeSpec.setName("data");
                dataVolumeSpec.setCategory("data");
                dataVolumeSpec.setPerformanceLevel(targetPerformanceLevel);
                replicaRequest.setVolumeSpecs(Collections.singletonList(dataVolumeSpec));
                slaveDestReplicas.add(replicaRequest);
            }
            if (CollectionUtils.isEmpty(slaveDestReplicas)) {
                return result;
            }
            replicaSetResourceRequest.setReplicaResourceRequestList(slaveDestReplicas);
            replicaSetResourceRequest.setReserveDiskCount(null);
            // 评估资源
            try {
                boolean isResourceSufficient = commonProviderService.getDefaultApi().evaluateReplicaSetResource(requestId, "no_need", evaluateNum, replicaSetResourceRequest);
                if (isResourceSufficient) {
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 1);
                } else {
                    logger.warn("dest slave ReplicaSet for shrink allocate resource failed.");
                    result.put(ParamConstants.DB_INSTANCE_AVAILABLE, 0);
                }
            } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            }
            return result;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        }
    }
}