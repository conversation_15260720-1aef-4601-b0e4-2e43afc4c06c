package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.support.TaskSupport;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.aliyun.dba.support.common.action.IAction;

import javax.annotation.Resource;
import java.util.*;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.TASK_MYSQL_MODIFY_VIP_AVAILABLE;

/**
 * 修改实例vip可用性
 *
 * <AUTHOR>
 */
@Component("physicalModifyDBInstanceVIPAvailableImpl")
public class ModifyDBInstanceVIPAvailableImpl implements IAction {
    private static final LogAgent logger = LogFactory.getLogAgent(ModifyDBInstanceVIPAvailableImpl.class);
    public static Set<String> OPERATION_TYPE_SET = new HashSet<>(2);

    static {
        OPERATION_TYPE_SET.add("enable");
        OPERATION_TYPE_SET.add("disable");
    }

    @Autowired
    private ResourceService resourceService;
    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private TaskService taskService;
    @Autowired
    protected CustinsParamService custinsParamService;

    /**
     * 处理操作请求，用于修改数据库实例的VIP可用性
     * <p>
     * 此方法首先检查数据库实例的状态、类型和锁定模式是否支持该操作
     * 然后获取任务所需的参数，并验证参数的有效性
     * 接着根据参数查找目标VIP和连接字符串，如果找不到则抛出异常
     * 最后创建任务队列对象，更新数据库实例状态，并将任务添加到队列中
     *
     * @param custins 数据库实例对象，包含数据库实例的相关信息
     * @return 包含任务和数据库实例信息的映射，包括实例ID、实例名称、连接字符串、负载均衡ID、网络类型和任务ID
     * @throws RdsException 如果数据库实例不满足操作条件或参数无效
     */
    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        logger.info("doActionRequest: custins={}", JSON.toJSONString(custins));
        logger.info("doActionRequest: actionParams={}", JSON.toJSONString(actionParams));
        // 检查数据库实例是否处于活动状态
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        logger.info("实例状态检查成功");
        // 检查数据库实例是否为共享类型
        if (custins.isShare()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
        // 检查数据库实例是否被锁定
        if (custins.isLock()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
        logger.info("实例锁定状态检查成功");
        // 定义任务的关键字
        String taskKey = TASK_MYSQL_MODIFY_VIP_AVAILABLE;
        // 检查数据库实例是否有正在进行的相同任务
        checkCustinsTaskForModifyVipAvailable(custins, taskKey);
        logger.info("数据库实例是否有正在进行的相同任务:无");
        // 获取目标负载均衡ID
        String targetLbId = mysqlParamSupport.getParameterValue(actionParams, "LbId");
        logger.info("获取目标负载均衡ID：{}", targetLbId);
        // 验证负载均衡ID是否为空
        if (StringUtils.isBlank(targetLbId)) {
            throw new RdsException(MysqlErrorCode.MISSING_PARAM_LBID.toArray());
        }
        // 获取操作类型
        String opType = mysqlParamSupport.getParameterValue(actionParams, "Type");
        logger.info("获取操作类型：{}", opType);
        // 验证操作类型是否为空
        if (StringUtils.isBlank(opType)) {
            throw new RdsException(MysqlErrorCode.MISSING_PARAM_TYPE.toArray());
        }
        if (!OPERATION_TYPE_SET.contains(opType)) {
            throw new RdsException(MysqlErrorCode.INVALID_OPERATION_TYPE.toArray());
        }
        // 初始化目标VIP、连接字符串和网络类型
        String targetVip = "";
        String targetConnectionString = "";
        Integer netType = null;
        // 获取数据库实例的连接地址列表
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);
        logger.info("获取数据库实例的连接地址列表：{}", JSON.toJSONString(custinsConnAddrList));
        // 遍历连接地址列表，查找匹配的负载均衡ID和私网类型
        for (CustinsConnAddrDO connAddr : custinsConnAddrList) {
            String connAddrLbId = resourceService.getLbIdByVip(connAddr.getVpcId(), connAddr.getVip());
            if (targetLbId.equals(connAddrLbId) && connAddr.isPrivateNetType()) {
                targetVip = connAddr.getVip();
                targetConnectionString = connAddr.getConnAddrCust();
                netType = connAddr.getNetType();
            }
        }
        logger.info("初始化目标VIP、连接字符串和网络类型：{},{},{}", targetVip, targetConnectionString, netType);
        // 验证是否找到了目标VIP
        if (StringUtils.isBlank(targetVip)) {
            throw new RdsException(MysqlErrorCode.INVALID_LBID_PARAM.toArray());
        }
        // 创建任务参数映射
        Map<String, Object> taskParamMap = new HashMap(4);
        taskParamMap.put("targetLbId", targetLbId);
        taskParamMap.put("targetConnectionString", targetConnectionString);
        taskParamMap.put("opType", opType);
        // 创建任务队列对象
        TaskQueueDO taskQueue = new TaskQueueDO(mysqlParamSupport.getAction(actionParams), mysqlParamSupport.getOperatorId(actionParams),
                custins.getId(), TaskSupport.TASK_TYPE_CUSTINS, taskKey, JSON.toJSONString(taskParamMap));
        logger.info("创建任务队列对象：{}", JSON.toJSONString(taskQueue));
        // 数据库实例打标
        custinsParamService.setCustinsParam(custins.getId(), "classic_net", opType);
        // 更新数据库实例状态为网络修改中
        custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsState.STATE_NET_MODIFYING.getState(),
                CustinsState.STATE_NET_MODIFYING.getComment());
        // 创建任务队列
        taskService.createTaskQueue(taskQueue);
        // 获取任务ID
        Integer taskId = taskQueue.getId();
        logger.info("创建任务队列：{}", taskQueue.getId());
        // 更新任务的Pengine策略ID
        taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(actionParams));
        // 创建返回的数据映射
        Map<String, Object> data = new HashMap<>(10);
        data.put("DBInstanceID", custins.getId());
        data.put("DBInstanceName", custins.getInsName());
        data.put("ConnectionString", targetConnectionString);
        data.put("LbId", targetLbId);
        data.put("DBInstanceNetType", netType);
        data.put("TaskId", taskId);
        logger.info("创建返回的数据映射：{}", JSON.toJSONString(data));
        // 返回包含任务和数据库实例信息的映射
        return data;
    }

    /**
     * 修改vip可用性并发拦截
     * 该方法用于检查在修改VIP可用区时是否存在冲突的任务，以避免并发问题
     * 如果存在状态为0、1或8的任务，则表示有冲突，将抛出异常阻止操作
     *
     * @param custInstanceDO 客户实例对象，包含实例的相关信息
     * @param taskKey        任务键，用于标识特定的任务
     * @throws RdsException 如果存在冲突的任务，抛出此异常
     */
    public void checkCustinsTaskForModifyVipAvailable(CustInstanceDO custInstanceDO, String taskKey) throws RdsException {
        // 定义任务状态数组，包含需要检查的任务状态
        String[] taskStatusArray = new String[]{"0", "1", "8"};
        // 创建条件映射，用于存储查询任务队列的条件
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("taskKey", taskKey);
        condition.put("status", taskStatusArray);
        // 判断客户实例是否为逻辑实例
        if (custInstanceDO.isLogic()) {
            // 如果是逻辑实例，获取其下所有的DB实例
            List<CustInstanceDO> dbCustins = custinsService.getCustInstanceUnitByParentIdAndCharacterType(custInstanceDO.getId(), "db");
            // 遍历DB实例，检查每个实例的任务队列
            for (CustInstanceDO dbCustin : dbCustins) {
                condition.put("custinsId", dbCustin.getId());
                Integer count = taskService.countTaskQueueByCondition(condition);
                // 如果任务队列非空，表示有冲突，抛出异常
                if (count > 0) {
                    throw new RdsException(ErrorCode.TASK_HAS_EXIST);
                }
            }
        } else {
            // 如果不是逻辑实例，直接检查该实例的任务队列
            condition.put("custinsId", custInstanceDO.getId());
            Integer count = taskService.countTaskQueueByCondition(condition);
            // 如果任务队列非空，表示有冲突，抛出异常
            if (count > 0) {
                throw new RdsException(ErrorCode.TASK_HAS_EXIST);
            }
        }
    }
}
