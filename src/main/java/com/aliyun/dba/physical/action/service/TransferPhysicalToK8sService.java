package com.aliyun.dba.physical.action.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.idao.ZoneIDao;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParamGroupHelper;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.CustinsServiceDO;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.service.*;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dbs.service.DbsService;
import com.aliyun.dba.ecs.service.EcsImageService;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.ClustersQuery;
import com.aliyun.dba.host.dataobject.HostInfo;
import com.aliyun.dba.host.idao.ClusterIDao;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstancePerfDO;
import com.aliyun.dba.instance.idao.InstanceIDao;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.onecs.service.MysqlOnEcsDBCenterService;
import com.aliyun.dba.physical.action.support.PysicalToPodModifyInsParam;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.TransferK8sToPhysicalService;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.poddefault.action.support.exception.CommonProviderExceptionUtils;
import com.aliyun.dba.poddefault.action.support.modules.RundGrayInfoModules.RundRegionalGrayInfo;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.dataobject.RegionAVZonDO;
import com.aliyun.dba.resource.dataobject.RegionAVZoneQuery;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.IResApi;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.utils.RequestSession;
import com.aliyun.dba.support.utils.Validator;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.task.service.TaskService;
import com.aliyun.dba.task.support.TaskSupport;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.aliyun.dba.base.support.MySQLParamConstants.CONN_TYPE_PHYSICAL;
import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.custins.support.CustinsSupport.CUSTINS_PARAM_MINOR_VERSION_KEY;
import static com.aliyun.dba.custins.support.CustinsSupport.getAndCheckBizType;
import static com.aliyun.dba.instance.support.InstanceSupport.*;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;
import static com.aliyun.dba.task.support.TaskSupport.TASK_TYPE_CUSTINS;

@Slf4j
@Service
public class TransferPhysicalToK8sService {
    private static final LogAgent logger = LogFactory.getLogAgent(TransferK8sToPhysicalService.class);

    @Autowired
    protected MysqlParamSupport mysqlParamSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected EcsService ecsService;
    @Autowired
    protected EcsImageService ecsImageService;
    @Autowired
    protected HostService ecsHostService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected ClusterService clusterService;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    protected InstanceService instanceService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected IResApi resApi;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected MysqlOnEcsDBCenterService mysqlOnEcsDBCenterService;
//    @Autowired
//    protected PodAvzSupport avzSupport;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected MysqlParamGroupHelper mysqlParamGroupHelper;
    @Autowired
    private DbsService dbsService;
    @Autowired
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Autowired
    private MinorVersionService minorVersionService;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    private InstanceIDao instanceIDao;
    @Resource
    protected ReplicaSetService replicaSetService;
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Autowired
    private DTZSupport dtzSupport;
    @Autowired
    private AliyunInstanceDependency dependency;
    @Autowired
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Autowired
    private CommonProviderService commonProviderService;
    @Autowired
    protected PodParameterHelper podParameterHelper;
    @Autowired
    private ClusterIDao clusterIDao;
    @Resource
    protected HostIDao hostIDao;
    @Resource
    private ResourceService resourceService;
    @Autowired
    private ZoneIDao zoneIDao;
    @Resource
    private HostService hostService;

    // 新老架构迁移新架构CLUSTER集群DBTYPE
    String GLOBAL_DB_TYPE = "global";

    public Map<String, Object> doActionRequest(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        String requestId = null;
        boolean isSuccess = false;
        boolean isAllocated = false;
        // 所有申请资源成功的临时实例名
        Map<String, AllocateTmpResourceResult> allocatedInsNameMap = new ConcurrentHashMap<>();

        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();
        TransListDO translist = new TransListDO();
        try {
            ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
            PysicalToPodModifyInsParam podModifyInsParam = initPodModifyInsParam(params);
            requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
            custins = mysqlParamSupport.getAndCheckCustInstance(params);

            validatePhysicalToK8s(custins, params);
            // 获取目标实例Region
            // 指定DispenseMode默认为MultiAVZDispenseMode
            String dispenseModeStr = CustinsParamSupport.getParameterValue(params, ParamConstants.DISPENSE_MODE);
            if (com.alibaba.cobar.util.StringUtil.isEmpty(dispenseModeStr)) {
                params.put("DispenseMode".toLowerCase(), "1");
            }
            AVZInfo avzInfo = generateAvzInfoByLBAndHostInfo(custins, podModifyInsParam.getRegionId(), requestId);

            String strEcsTransType = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_TRANS_TYPE,
                    CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
            CustinsSupport.checkTransTypeValid(strEcsTransType);

            //获取版本
            String dbVersion = mysqlParamSupport.getDBVersion(params, custins.getDbType());
            if (dbVersion == null) {
                dbVersion = custins.getDbVersion();
            }

            // 获取level
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InstanceLevelDO newLevel = this.getNewLevel(custins, params, dbVersion);

            //获取磁盘大小
            Long diskSize = this.getDiskSize(custins, params);

            // 获取状态描述
            String inputDesc = mysqlParamSupport.getParameterValue(params, "DBInstanceStatusDesc");

            List<CustInstanceDO> readinsList = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), false);
            Integer readOnlyCount = readinsList.size();

            if (readOnlyCount > 0) {
                if (!params.containsKey("RoTargetDBInstanceClass".toLowerCase())) {
                    logger.error("RoTargetDBInstanceClass cannot be empty");
                    throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
                }
                List<TempInsCreateParam> insParamList = new ArrayList<>();
                TempInsCreateParam insParam = new TempInsCreateParam();
                insParam.setCustins(custins);
                insParam.setAvzInfo(avzInfo);
                insParam.setPodModifyInsParam(podModifyInsParam);
                insParam.setAllocateDisk(true);
                insParamList.add(insParam);

                // 并发申请临时主实例+只读实例资源
                allocateTmpResourceForHaRoIns(allocatedInsNameMap, insParamList, readinsList, params, requestId);
                isAllocated = !allocatedInsNameMap.isEmpty();
                // 还原全局参数为主实例的参数
                ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
                // 申请的临时实例数量 = 只读 + 主实例
                if (allocatedInsNameMap.size() != readOnlyCount + 1) {
                    throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "AllocateResourcesFailed", "Allocate resources for ha and readonly replicaSet failed."});
                }
                translist = allocatedInsNameMap.get(custins.getInsName()).getTransList();
            }
            else {
                // 创建临时主实例（实例名不以tmp开头，以订单号开头）
                AllocateTmpResourceResult allocateTmpResourceResult = createTempCustins(requestId, custins, podModifyInsParam, avzInfo, true);
                isAllocated = allocateTmpResourceResult.isAllocated();

                String destDBInstanceName = allocateTmpResourceResult.getResourceRequest().getReplicaSetName();
                CustInstanceDO tempCustins = custinsService.getCustInstanceByInsName(null, destDBInstanceName, 1);
                if (tempCustins == null) {
                    logger.error("tempCustins {} info is null, may allocate resource faild", destDBInstanceName);
                    throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
                }

                translist = allocateTmpResourceResult.getTransList();
                allocatedInsNameMap.put(custins.getInsName(), allocateTmpResourceResult);

                custinsParamService.syncCustinsCipherChain(custins.getId(), tempCustins.getId(), custins.getDbType(), custins.getInsName());

                // sync db && accounts
                dbsService.syncAllDbsAndAccounts(custins, tempCustins);

                // sync all ip white list group
                ipWhiteListService.syncCustinsIpWhiteList(custins.getId(), tempCustins.getId());
            }

            //校验UTC时间，返回带时区的日期
            Date utcDate = mysqlParamSupport.parseCheckSwitchTimeTimeZoneSafe(params);
            String switchMode = mysqlParamSupport.getAndCheckSwitchTimeMode(params,
                    ParamConstants.SWITCH_TIME_MODE, utcDate, true);
            //计算元数据库时区时间
            //utcDate可能为null，即不是按照时间点切换
            Date metadbSwitchTime = null;
            if(utcDate != null){
                String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
                metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
            }
            translist.setSwitchTime(metadbSwitchTime);

            logger.info("TransferPhysicalToK8sService.doActionRequest.params: " + JSONObject.toJSONString(params));

            // 创建任务流: 主实例的任务流
            Integer taskId = this.transMysqlDBTask(
                    mysqlParamSupport.getAction(params), mysqlParamSupport.getOperatorId(params), custins, params,
                    translist, switchMode, utcDate, readOnlyCount);

            isSuccess = true;
            custinsService.updateCustInstanceStatusByCustinsId(custins.getId(), CustinsSupport.CUSTINS_STATUS_TRANS, CustinsState.STATE_CLASS_CHANGING.getComment());
            taskService.updateTaskPenginePolicy(taskId, mysqlParamSupport.getPenginePolicyID(params));

            Map<String, Object> data = new HashMap<>(8);
            data.put("MigrationID", 0);
            data.put("DBInstanceID", custins.getId());
            data.put("DBInstanceName", custins.getInsName());
            data.put("SourceDBInstanceClass", oldLevel.getClassCode());
            data.put("TargetDBInstanceClass", newLevel.getClassCode());
            data.put("TaskId", taskId);
            data.put("SwitchMode", switchMode);
            return data;
        } catch (RdsException re) {
            logger.error(requestId + " RdsException: ", re);
            return createErrorResponse(re.getErrorCode());
        } catch (com.aliyun.apsaradb.dbaasmetaapi.ApiException e) {
            logger.error(requestId + " MetaApi error: ", e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", "Write replicaSet meta failed."});
        } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
            Map<String, Object> responseMap = CommonProviderExceptionUtils.resourceWrapper(requestId, e);
            return CommonProviderExceptionUtils.resourceWrapper(requestId, e);
        } catch (Exception ex) {
            logger.error(requestId + " Exception: ", ex);
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            // 变配失败释放资源
            if (!allocatedInsNameMap.isEmpty() && !isSuccess && isAllocated) {
                try {
                    for (AllocateTmpResourceResult tmpIns : allocatedInsNameMap.values()) {
                        String insName = tmpIns.getResourceRequest().getReplicaSetName();
                        logger.warn("TransferPhysicalToK8sService is deleting tmp instance: " + insName);
                        commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, insName);
                    }
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    //ignore
                    logger.error(String.format("allocate for rebuild failed: %s", e.getResponseBody()));
                }
            }
        }
    }

    public Integer transMysqlDBTask(
            String action, Integer operatorId, CustInstanceDO custins, Map<String, String> params,
            TransListDO translist, String switchMode, Date utcDate, Integer readOnlyCount) throws RdsException {

        String inputDesc = mysqlParamSupport.getParameterValue(params, "DBInstanceStatusDesc");
        String statusDesc = StringUtils.isNotBlank(inputDesc) ? inputDesc : CustinsState.STATE_CLASS_CHANGING.getComment();
        // 通过desc判断该迁移任务是否是运维后台下发
        Integer lockMigrate = 0;
        if (isActiveOperation(statusDesc)) {
            lockMigrate = 1;
        }

        String taskparamString = taskService.getTransTaskParameter(translist.getId(), switchMode, utcDate);
        Map<String, Object> taskparam = new HashMap<String, Object>();
        taskparam = JSON.parseObject(taskparamString);
        // #13054224 mysql实例迁移任务下发,增加一个参数锁定迁移flag,如果为true表示运维后台下发,在任务流中解锁>迁移>锁定.
        taskparam.put(CustinsSupport.LOCK_MIGRATE, lockMigrate);

        // 只读实例数量
        taskparam.put("readOnlyCount", readOnlyCount);
        taskparam.put("RdsExtMysql_TransferPhysicalToK8sService", "PengineMysql_MysqlTransferPhysicalToK8s");
        taskparam.put(CustinsSupport.TRANS_ID, translist.getId());

        String taskKey = TaskSupport.TASK_TRANSFER;
        TaskQueueDO taskQueue = new TaskQueueDO(action, operatorId, custins.getId(),
                TASK_TYPE_CUSTINS, taskKey);
        taskQueue.setParameter(JSONObject.toJSONString(taskparam));
        logger.warn("TransferPhysicalToK8sService.transMysqlDBTask.taskQueue: " + JSONObject.toJSONString(taskQueue));
        taskService.createTaskQueue(taskQueue);
        return taskQueue.getId();
    }

    /**
     * 创建临时实例
     * */
    public AllocateTmpResourceResult createTempCustins(String requestId, CustInstanceDO custins, PysicalToPodModifyInsParam modifyInsParam, AVZInfo avzInfo, Boolean allocateDisk) throws Exception {

        // 检查临时实例是否被重复分配
        String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName(custins.getBizType());
        CustInstanceDO tempCustins = custinsService.getCustInstanceByInsName(null, tmpReplicaSetName, 1);
        if (tempCustins != null) {
            throw new RdsException(ErrorCode.ALLOCATE_TMPINS_DUPLICATED);
        }

        // 申请临时实例
        AllocateTmpResourceResult result = this.allocateTmpResource(requestId, custins, modifyInsParam,
                CATEGORY_STANDARD, Arrays.asList(Replica.RoleEnum.MASTER, Replica.RoleEnum.SLAVE), avzInfo, allocateDisk);

        String masterLocation = avzInfo.getRegion(); // Master 子域
        AvailableZoneInfoDO masterDO = new AvailableZoneInfoDO(masterLocation, "master");

        Replica tmpMasterReplica = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(
                        modifyInsParam.getRequestId(),
                        result.getReplicaSet().getName(),
                        null, null, null, null
                ).getItems()
                .stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.MASTER))
                .findFirst()
                .get();

        Replica tmpSlaveReplica = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(
                        modifyInsParam.getRequestId(),
                        result.getReplicaSet().getName(),
                        null, null, null, null
                ).getItems()
                .stream()
                .filter(it -> Objects.equals(it.getRole(), Replica.RoleEnum.SLAVE))
                .findFirst()
                .get();
        String slaveLocation = tmpSlaveReplica.getSubDomain();
        AvailableZoneInfoDO slaveDO = new AvailableZoneInfoDO(slaveLocation, "slave");
        masterDO.setZoneID(tmpMasterReplica.getZoneId());
        slaveDO.setZoneID(tmpSlaveReplica.getZoneId());
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> availableZoneInfoList = multiAVZExParamDO.getAvailableZoneInfoList();
        availableZoneInfoList.add(masterDO);
        availableZoneInfoList.add(slaveDO);

        AVZInfo tmpAvzInfo = new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, avzInfo.getRegion(), avzInfo.getRegionId(), avzInfo.getRegionCategory(), multiAVZExParamDO);

        Integer tmpCustinsId = Objects.requireNonNull(result.getReplicaSet().getId()).intValue();
        List<CustinsParamDO> custinsParams = custinsParamService.getCustinsParams(custins.getId());
        if (!CollectionUtils.isEmpty(custinsParams)) {
            List<CustinsParamDO> tmpCustinsParams = custinsParamService.getCustinsParams(tmpCustinsId);
            logger.info("requestId={}, custinsParams={}", requestId, JSONObject.toJSONString(custinsParams));
            logger.info("requestId={}, tmpCustinsParams={}", requestId, JSONObject.toJSONString(tmpCustinsParams));
            Set<String> tmpCustinsParamNameSet = tmpCustinsParams.stream().filter(Objects::nonNull).map(CustinsParamDO::getName).collect(Collectors.toSet());
            for (CustinsParamDO custinsParam : custinsParams) {
                // 临时实例已有的参数不需要覆盖, SECURITY_IP_MODE字段不复制(本地盘高安全模式，云盘不支持)
                if (tmpCustinsParamNameSet.contains(custinsParam.getName()) || custinsParam.getName().equalsIgnoreCase(CustinsParamSupport.SECURITY_IP_MODE)) {
                    continue;
                }
                custinsParamService.setCustinsParam(tmpCustinsId, custinsParam.getName(), custinsParam.getValue());
            }
        }

        logger.info("[createTempCustins] " + tmpCustinsId +  " tmpAvzInfo: " + JSON.toJSONString(tmpAvzInfo));

        // 主可用区需要更新
        custinsParamService.updateAVZInfo(tmpCustinsId, tmpAvzInfo);
        custinsParamService.setCustinsParam(tmpCustinsId, CustinsParamSupport.CUSTINS_PARAM_NAME_SLAVE_LOCATION, slaveLocation);
        if (PodCommonSupport.isArchChange(modifyInsParam.getSrcInstanceLevel(), modifyInsParam.getTargetInstanceLevel())) {
            custinsParamService.setCustinsParam(tmpCustinsId, PodDefaultConstants.PARAM_ARCH_CHANGED, "1");
        }
        // 审计日志管控参数补全
        // 集团业务不会使用此 Service，直接设置
        custinsParamService.setCustinsParam(custins.getId(), CustinsParamSupport.CUSTINS_PARAM_NAME_SQL_LOG_VERSION, CustinsParamSupport.CUSTINS_PARAM_VALUE_SQL_LOG_VERSION_NEW);
        return result;
    }


    private AvailableZoneInfoDO getTargetZoneInfo(AVZInfo avzInfo, Replica.RoleEnum role) {
        MultiAVZExParamDO multiAvzDo = avzInfo.getMultiAVZExParamDO();
        AvailableZoneInfoDO targetZoneInfo = null;
        if (null != multiAvzDo) {
            for (AvailableZoneInfoDO zoneInfoDO : multiAvzDo.getAvailableZoneInfoList()) {
                if (role.getValue().equals(zoneInfoDO.getRole())) {
                    targetZoneInfo = zoneInfoDO;
                    break;
                }
            }
        }
        return targetZoneInfo;
    }

    /**
     * 临时实例资源申请（不含链路）
     */
    public AllocateTmpResourceResult allocateTmpResource(
            String requestId,
            CustInstanceDO custins,
            PysicalToPodModifyInsParam modifyInsParam,
            String category,
            List<Replica.RoleEnum> roles,
            AVZInfo avzInfo,
            Boolean allocateDisk
    ) throws Exception {
        ReplicaSetResourceRequest resourceRequest = new ReplicaSetResourceRequest();
        String tmpReplicaSetName = modifyInsParam.getTmpReplicaSetName(custins.getBizType());
        String masterZoneId = avzInfo.getMasterZoneId();
        String masterSubDomain = avzInfo.getMainLocation();

        String slaveZoneId = null;
        String slaveSubDomain = null;
        if (avzInfo.getDispenseMode().equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)
                && !avzInfo.getMultiAVZExParamDO().getSlaveAvailableZoneInfo().isEmpty()) {
            slaveZoneId = avzInfo.getMultiAVZExParamDO().getSlaveZoneIds()[0];
            slaveSubDomain = avzInfo.getMultiAVZExParamDO().getSlaveLocations()[0];
        } else {
            // 当使用ClassicDispenseMode的时候，使用主可用区ZoneId, 及SubDomain
            // 当备可用区为空的时候，使用主可用区ZoneId, 及SubDomain
            slaveZoneId = avzInfo.getMasterZoneId();
            slaveSubDomain = avzInfo.getMainLocation();
        }

        // 检查实例名是否已存在新架构实例
        List<Replica> currentReplicas = dBaasMetaService.getDefaultClient()
                .listReplicasInReplicaSet(
                        modifyInsParam.getRequestId(),
                        modifyInsParam.getDbInstanceName(),
                        null, null, null, null
                ).getItems();

        // Why assert??
        assert currentReplicas != null;
        PysicalToPodModifyInsParam.replicaNameValidate(currentReplicas, tmpReplicaSetName);

        String serviceSpecTag = null;
        Boolean isArm = PodCommonSupport.isArm(modifyInsParam.getTargetInstanceLevel());
        serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                modifyInsParam.getCurrentMinorVersion(),
                modifyInsParam.getBizType(),
                modifyInsParam.getDbType(),
                modifyInsParam.getDbVersion(),
                modifyInsParam.getDbType(),
                KindCodeParser.KIND_CODE_NEW_ARCH,
                modifyInsParam.getTargetInstanceLevel(),
                modifyInsParam.getTargetDiskType(),
                modifyInsParam.isDHG(),
                true,
                null); //需要支持下线版本

        if (StringUtils.isEmpty(serviceSpecTag)) {
            throw new RdsException(INVALID_MINOR_VERSION_NOT_FOUND);
        }


        if (StringUtils.isNotBlank(modifyInsParam.getClusterName()) && modifyInsParam.isDHG()) {
            resourceRequest.setDedicatedHostGroupId(modifyInsParam.getClusterName());
        }

        resourceRequest.userId(modifyInsParam.getBid())
                .uid(modifyInsParam.getUid())
                .replicaSetName(tmpReplicaSetName)
                .bizType(podParameterHelper.getBizType(requestId, modifyInsParam.getRegionId()).toString())
                .dbType(modifyInsParam.getDbType())
                .dbVersion(modifyInsParam.getDbVersion())
                .classCode(modifyInsParam.getTargetClassCode())
                .diskSize(modifyInsParam.getTargetDiskSizeGB())
                .storageType(modifyInsParam.getTargetDiskType())
                .catagory(category)
                .allocateDisk(allocateDisk)
                // ServiceTag 由本地盘的转换为k8s新架构支持的ServiceTag
                .composeTag(serviceSpecTag)
                // 不申请 VIP（默认 lvs）
                .connType(CONN_TYPE_PHYSICAL)
                // 不申请反向 VPC
                .ignoreCreateVpcMapping(true);

        if("aligroup".equals(custins.getBizType())){
            resourceRequest.bizType(custins.getBizType());
        }

        boolean isTargetSingleTenant = modifyInsParam.isTargetSingleTenant();
        if (isTargetSingleTenant) {
            // 单租户场景
            resourceRequest.setSingleTenant(true);
        }
        resourceRequest.setEniDirectLink(false);

        resourceRequest.setScheduleTemplate(modifyInsParam.getScheduleTemplate());

        // 强制走单可用区，控制台如果支持再做修改
        resourceRequest.setSubDomain(avzInfo.getMainLocation());
        resourceRequest.setRegionId(avzInfo.getRegionId());

        Integer diskSizeGB = modifyInsParam.getTargetDiskSizeGB();
        int extendedDiskSizeGB = podParameterHelper
                .getExtendDiskSizeGBForPod(
                        modifyInsParam.getReplicaSetMeta().getBizType(),
                        false, // 云上无影响
                        diskSizeGB
                );

        List<ReplicaResourceRequest>  replicas = new ArrayList<ReplicaResourceRequest>();

        String performanceLevel = null;
        performanceLevel = modifyInsParam.getTargetPerformanceLevel();

        for (Replica.RoleEnum role : roles) {
            ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
            replicaResourceRequest.setStorageType(modifyInsParam.getTargetDiskType());
            replicaResourceRequest.setClassCode(modifyInsParam.getTargetClassCode());
            replicaResourceRequest.setSingleTenant(isTargetSingleTenant);
            // 云盘赠送
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);

            replicaResourceRequest.setRole(role.toString());
            if (role == Replica.RoleEnum.MASTER) {
                replicaResourceRequest.setZoneId(masterZoneId);
                replicaResourceRequest.setSubDomain(masterSubDomain);
            } else {
                replicaResourceRequest.setZoneId(slaveZoneId);
                replicaResourceRequest.setSubDomain(slaveSubDomain);
            }
            replicaResourceRequest.setDiskSize(extendedDiskSizeGB);
            VolumeSpec volumeSpec = new VolumeSpec();
            volumeSpec.setName("data");
            volumeSpec.setPerformanceLevel(performanceLevel);
            replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));
            replicas.add(replicaResourceRequest);
        }
        resourceRequest.setReplicaResourceRequestList(replicas);

        InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(
                modifyInsParam.getTargetInstanceLevel().getClassCode(),
                custins.getDbType(),
                custins.getDbVersion(),
                custins.getTypeChar(),
                modifyInsParam.getTargetInstanceLevel().getCharacterType()
        );

        podReplicaSetResourceHelper.mockReplicaSetResource(resourceRequest);

        // 设置autopl配置
        resourceRequest.setProvisionedIops(modifyInsParam.getProvisionedIops());
        resourceRequest.setBurstingEnabled(modifyInsParam.isBurstingEnabled());

        // 使用 Common 创建实例 API
        Boolean isAllocated = commonProviderService.getDefaultApi()
                .allocateReplicaSetResourceV1(modifyInsParam.getRequestId(), tmpReplicaSetName, resourceRequest);
        DefaultApi client = dBaasMetaService.getDefaultClient();
        ReplicaSet replicaSet = client.getReplicaSet(requestId, tmpReplicaSetName, null);

        // 补齐 translist
        List<Integer> sInsIds = custinsService.getInstanceIdsByCustinsId(custins.getId());
        CustInstanceDO tempCustins  = custinsService.getCustInstanceByInsName(null, tmpReplicaSetName, 0);
        List<Integer> dInsIds = custinsService.getInstanceIdsByCustinsId(tempCustins.getId());
        TransListDO transList = new TransListDO(custins, TRANS_STATUS_REMOVE_NB, TRANS_TYPE_REMOVE);
        transList.setsCinsid(custins.getId());
        transList.setdCinsid(Objects.requireNonNull(replicaSet.getId()).intValue());
        transList.setsLevelid(custins.getLevelId());
        transList.setdLevelid(newLevel.getId());
        transList.setsDisksize(custins.getDiskSize());
        transList.setdDisksize(diskSizeGB * 1024L);
        transList.setsHinsid1(sInsIds.get(0));
        transList.setsHinsid2((sInsIds.size() > 1) ? sInsIds.get(1) : 0);
        transList.setdHinsid1(dInsIds.get(0));
        transList.setdHinsid2((dInsIds.size() > 1) ? dInsIds.get(1) : 0);

        this.instanceIDao.createTransList(transList);

        // 订正元数据，设置为临时实例
        replicaSet.setPrimaryInsName(custins.getInsName());
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        client.updateReplicaSet(requestId, tmpReplicaSetName, replicaSet);

        AllocateTmpResourceResult allocateTmpResourceResult = new AllocateTmpResourceResult();
        allocateTmpResourceResult.setAllocated(isAllocated);
        allocateTmpResourceResult.setTransList(transList);
        allocateTmpResourceResult.setResourceRequest(resourceRequest);
        allocateTmpResourceResult.setReplicaSet(replicaSet);

        return allocateTmpResourceResult;
    }


    /**
     * 入参初始化
     */
    public PysicalToPodModifyInsParam initPodModifyInsParam(Map<String, String> params) throws Exception {
        // TODO(wenfeng):
        // 1. 链路上：检查vpc链路只有一条、没有ipv6、不是经典网络
        // 2. 检查不是TDE加密实例
        PysicalToPodModifyInsParam modifyInsParam = new PysicalToPodModifyInsParam(dependency, params);

        // 初始化实例基础信息
        modifyInsParam
                .initCustins()
                .initReplicaSetMeta()
                .multiWriteEngineValidate();

        // 获取用户与实例属性信息
        modifyInsParam
                .initUser()
                .initInstanceName()
                .initDBType()
                .initDBVersion()
                .initClusterName()
                .initOrderId();

        // 设置业务属性标
        modifyInsParam
                .setIsDHG()
                .setClusterNameToParamsForDHG()
                .setIsReadIns()
                .initRoleHostNameMapping();

        // 初始化迁移变配资源基本信息
        modifyInsParam
                .initDiskSizeGB()               // 获取原实例磁盘大小
                .initTargetDiskSizeGB()         // 获取目标磁盘大小
                .initClassCode()                // 获取原实例规格码
                .initTargetClassCode()          // 获取目标规格码
                .initSrcInstanceLevel()         // 获取原规格对象
                .initTargetInstanceLevel()      // 获取目标规格对象
                .initSrcDiskTypeForPhysical()              // 获取原磁盘类型
                .initTargetDiskType()           // 获取目标磁盘类型
                .initAutoPLConfig()              // 获取AutoPL配置
                .setIsDiskSizeChange()
                .setIsDiskTypeChange()
                .diskSizeValidation();


        // 初始化AZ相关信息
        modifyInsParam.initOldAVZInfo();

        modifyInsParam
                .initAVZInfo()
                .initRegionId()
                .setIsModifyAvz();

        modifyInsParam.fixReplicaSetMeta();

        // 初始化切换信息
        modifyInsParam.initSwitchInfo();

        // 初始化调度信息
        modifyInsParam
                .initIsTargetSingleTenant()
                .initRsTemplate();

        // 检查云盘加密
        modifyInsParam.isEncryptionKeyAvailable();

        modifyInsParam.setBizType();

        // 版本信息MinorVersion
        modifyInsParam.setCurrentMinorVersion();

        return modifyInsParam;
    }

    /**
     * 判断是否是本地盘变k8s高可用，如果满足一下条件，则进行本地盘变k8s云盘
     */
    public boolean isPhysicalToK8s(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String levelCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
//        String newStorageType = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
        if (levelCode != null) {
            String dbVersion = custins.getDbVersion();

            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(
                    levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);

            if (newLevel != null) {
                validatePhysicalToK8sV2(custins, params, oldLevel, newLevel);
                if (oldLevel.getHostType().equals(0) && newLevel.getHostType().equals(2)
                        && ("5.7".equals(dbVersion) || "8.0".equals(dbVersion))
                        && InstanceLevel.CategoryEnum.STANDARD.toString().equals(oldLevel.getCategory())
                        && InstanceLevel.CategoryEnum.STANDARD.toString().equals(newLevel.getCategory())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否是可以发起本地盘变k8s高可用
     */
    public void validatePhysicalToK8sV2(CustInstanceDO custins, Map<String, String> params, InstanceLevelDO oldLevel, InstanceLevelDO newLevel) throws RdsException {
        validateTargetStorageType(custins, params, oldLevel, newLevel);
    }

    public void validateTargetStorageType(CustInstanceDO custins, Map<String, String> params, InstanceLevelDO oldLevel, InstanceLevelDO newLevel) throws RdsException {
        // 如果目标实例规格为本地盘，但传入essd类型，直接报错不支持
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        String storageType = mysqlParamSupport.getParameterValue(params, ParamConstants.DB_INSTANCE_STORAGE_TYPE);
        if (!StringUtils.isEmpty(storageType)) {
            if (0 == oldLevel.getHostType() && 0 == newLevel.getHostType() && !StringUtils.equalsIgnoreCase(CustinsSupport.STORAGE_TYPE_LOCAL_SSD, storageType)) {
                logger.error("requestId: {}, newLevel is {} used for local_ssd, storage type is {}. Physical transfer to k8s is not supported", requestId, newLevel.getClassCode(), storageType);
                throw new RdsException(ErrorCode.UNSUPPORTED_CHANGE_STORAGE_TYPE);
            }
        }
    }

    /**
     * 判断是否是可以发起本地盘变k8s高可用
     */
    public void  validatePhysicalToK8s(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        validateTargetStorageSize(custins, params);
        validateInsType(custins, params);
        validateDBVersion(custins, params);
        validateMinorVersion(custins, params);
        validateConnType(custins, params);
        validateNetType(custins, params);
        validateMultiVpc(custins, params);
        validateNetProtocol(custins, params);
        validateInsStatus(custins, params);
        validateMaxscale(custins, params);
        validateTDE(custins, params);
        validateSSL(custins, params);
        validateFlexibleResourceCpu(custins, params);
        validateLocationSupportK8s(custins, params);
        validateCrossRegion(custins, params);
    }

    /**
     * 判断只读实例是否是可以发起本地盘变k8s高可用
     */
    public void  validatePhysicalToK8sForRo(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        validateTargetStorageSize(custins, params);
        validateRoInsType(custins, params);
        validateDBVersion(custins, params);
        validateMinorVersion(custins, params);
        validateConnType(custins, params);
        validateNetType(custins, params);
        validateMultiVpc(custins, params);
        validateNetProtocol(custins, params);
        validateInsStatus(custins, params);
        validateMaxscale(custins, params);
        validateTDE(custins, params);
        validateSSL(custins, params);
        validateFlexibleResourceCpu(custins, params);
        validateLocationSupportK8s(custins, params);
        validateCrossRegion(custins, params);
    }

    public void validateInsType(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        CustInstanceDO primaryins = null;
        CustInstanceDO guardins = null;
        // 如果当前实例为逻辑主实例，则获取其逻辑灾备实例，便于后面判断含有灾备实例情况进行版本升级的合法性。
        if (custins.isLogicPrimary()) {
            guardins = custinsService.getGuardInstanceByPrimaryCustinsId(custins.getId());
        } else {
            logger.error("custins {} is not parimary or guardins type, not support migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }
        // 如果当前实例为逻辑灾备实例、只读实例或只读实例备节点，获取其primary_custins_id指向的实例
        if (custins.getPrimaryCustinsId() != null && custins.getPrimaryCustinsId() > 0) {
            primaryins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
        }
        // 不是主实例，不支持
        if (primaryins != null) {
            logger.error("custins {} is not primary instance , custIns's PrimaryCustinsId {}", custins.getInsName(), custins.getPrimaryCustinsId());
            throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }
        // 如果实例为灾备实例或存在灾备实例，
        if (guardins != null) {
            logger.error("custins {} exists guard instance , not permit migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }

        // 不支持带子实例的主实例发起本地盘变k8s高可用
        List<CustInstanceDO> subCustInstances = custinsService.getCustInstanceByParentId(custins.getId());
        if (!subCustInstances.isEmpty()) {
            logger.error("custins {} exists sub instance , not permit migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
    }

    public void validateRoInsType(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        if (!custins.isRead()) {
            logger.error("custins {} is not read-only instance , not permit migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.INVALID_INS_TYPE);
        }
        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if (!InstanceLevel.CategoryEnum.STANDARD.getValue().equalsIgnoreCase(oldLevel.getCategory()) || oldLevel.getHostType() != 0) {
            logger.error("custins {} is not standard physical instance , not permit migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.INVALID_SOURCE_CATEGORY);
        }
        if (!"5.7".equals(custins.getDbVersion()) && !"8.0".equals(custins.getDbVersion())) {
            logger.error("custins {} engine version is not 5.7 or 8.0, not permit migrate to k8s", custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
        }
    }

    public void validateInsStatus(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 不支持非运行状态实例发起本地盘变k8s高可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        if (!custins.isActive()) {
            logger.error(String.format("requestId: %s, Instance status is not Active. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }
        // 不支持锁定的实例发起本地盘变k8s高可用
        if (!custins.isNoLock()) {
            logger.error(String.format("requestId: %s, Instance is locked. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }
    }

    public void validateMinorVersion(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        String releaseDate = null;
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), CUSTINS_PARAM_MINOR_VERSION_KEY);
        if (custinsParamDO != null) {
            releaseDate = minorVersionServiceHelper.parseReleaseDateFromMinorVersion(custinsParamDO.getValue());
        }
        if (releaseDate == null) {
            logger.error("custins {}, not found minor version release in custins params", custins.getInsName());
            throw new RdsException(ErrorCode.MINOR_VERSION_TAG_NOT_FOUND_BY_CUSTINS);
        }

        List<String> releaseDateList = minorVersionService.getReleaseDateListByTag(
                custins.getDbType(),
                custins.getDbVersion(),
                KindCodeParser.KIND_CODE_NEW_ARCH,
                "standard",
                MinorVersionServiceHelper.MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE
        );

        if (releaseDateList != null) {
            Boolean expectedReleaseDateExsit = false;
            for (String podMinorVersion: releaseDateList) {
                if (StringUtils.equals(podMinorVersion, releaseDate)) {
                    expectedReleaseDateExsit = true;
                    break;
                }
            }
            if (!expectedReleaseDateExsit) {
                logger.error("requestId: {}. minor version {} {} not found in k8s, please upgrade minor version first", requestId, custins.getDbVersion(), releaseDate);
                throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
            }
        } else {
            logger.error("requestId: {}. minor version list not found in k8s, may not support this major dbVersion {} in k8s", requestId, custins.getDbVersion());
            throw new RdsException(ErrorCode.MINOR_VERSION_NOT_SUPPORT);
        }
    }

    public void validateConnType(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 目前本地盘迁移云盘只支持lvs链路
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        if (!CustinsSupport.isLvs(custins.getConnType())) {
            logger.error(String.format("requestId: %s. conn type is not lvs. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CONNTYPE);
        }
    }

    public void validateNetType(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 只支持VPC网络实例发起本地盘变k8s高可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_VPC, CustinsSupport.RW_TYPE_NORMAL);
        if (custinsConnAddrList.isEmpty()) {
            logger.error("requestId: {} . custIns {} vpc type conn address is empty, not permit migrate to k8s", requestId, custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }
        List<CustinsConnAddrDO> privateConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_PRIVATE, CustinsSupport.RW_TYPE_NORMAL);
        if (privateConnAddrList != null && !privateConnAddrList.isEmpty()) {
            logger.error("contains private type conn address is empty, not permit migrate to k8s", requestId, custins.getInsName());
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_NETTYPE);
        }
    }

    public void validateMultiVpc(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 不支持多个vpc的实例发起本地盘变k8s高可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), CustinsSupport.NET_TYPE_VPC, null);
        if (custinsConnAddrList.size() >= 2) {
            logger.error(String.format("requestId: %s . DBInstance has multiple VPC ConnAddr. Physical transfer to k8s is not supported", requestId));
//            throw new RdsException(ErrorCode.UNSUPPORTED_MULTI_VPC);
        }
    }

    public void validateNetProtocol(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 不支持使用ipv6的实例发起本地盘变k8s高可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
                .getCustinsConnAddrByCustinsId(custins.getId(), null, null);
        if (!custinsConnAddrList.isEmpty()) {
            for (CustinsConnAddrDO custinsConnAddr : custinsConnAddrList) {
                String ipVersion = custinsService.getIpVersionByVipAndVpcId(custinsConnAddr.getVip(), custinsConnAddr.getVpcId());
                if (ipVersion.equals("ipv6")) {
                    logger.error(String.format("requestId: %s .custinsConnAddr %s ipVersion is ipv6 . " +
                            "Physical transfer to k8s is not supported", JSONObject.toJSONString(custinsConnAddr), requestId));
                    throw new RdsException(ErrorCode.UNSUPPORTED_NETPROTOCOL_IPV6);
                }
            }
        }

    }

    public void validateMaxscale(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        boolean isMaxscale = false;
        // 获取是否使用了Maxscale
        if (custins != null) {
            List<CustinsServiceDO> maxscale = custinsService.getCustinsServicesByCustinsIdAndServiceRole(custins.getId(), "maxscale");
            isMaxscale = !CollectionUtils.isEmpty(maxscale);
        }
        // 不支持 maxscale
        if (isMaxscale) {
            logger.error(String.format("requestId: %s . DBInstance with Maxscale. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.MAXSCALE_NOT_SUPPORT);
        }
    }

    public void validateDBVersion(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        //不支持版本升降级
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        String dbVersion = mysqlParamSupport.getDBVersion(params, custins.getDbType());

        if (dbVersion == null) {
            dbVersion = custins.getDbVersion();
        }
        //不支持版本升降级
        if (0 != dbVersion.compareTo(custins.getDbVersion())) {
            logger.error("request {}, custIns {}, not permit modify dbVersion from {} to {}", requestId, custins.getInsName(), custins.getDbVersion(), dbVersion);
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE_VERSION);
        }
    }

    public void validateTDE(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        //不支持TDE加密实例发起本地盘变k8s高可用
        // select * from custins_param where  name="tde_enabled" and value="1" and `custins_id`= #id
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "tde_enabled");
        if (custinsParamDO != null && "1".equalsIgnoreCase(custinsParamDO.getValue())) {
            logger.error(String.format("requestId: %s . TDE is enabled. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TDE_STATUS);
        }
    }

    public void validateSSL(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 不支持开通ssl实例发起本地盘变k8s高可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        List<String> sslParams = new ArrayList<String>(1);
        List<CustinsParamDO> sslResultList;
        sslParams.add(CustinsParamSupport.CUSTINS_PARAM_NAME_ENABLE_SSL);
        sslResultList = custinsParamService.getCustinsParams(custins.getId(), sslParams);
        String sslStatus = CustinsParamSupport.CUSTINS_PARAM_VALUE_ENABLE_SSL;
        if (!sslResultList.isEmpty() && sslResultList.get(0).getValue().equals(sslStatus)){
            logger.error(String.format("requestId: %s . SSL is enabled. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_SSL_STATUS);
        }
    }

    public void validateFlexibleResourceCpu(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        //不支持带弹性升级包的实例发起本地盘变k8s高可用，
        // select * from custins_param where name="flexible_resource_cpu" and value="1" and `custins_id`= #id
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), "flexible_resource_cpu");
        if (custinsParamDO != null && "1".equalsIgnoreCase(custinsParamDO.getValue())) {
            logger.error(String.format("requestId: %s . flexible_resource_cpu is enabled. Physical transfer to k8s is not supported", requestId));
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }
    }


    public void validateTargetStorageSize(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        // 传进来的参数里storage单位是GB，直接通过custins.getDiskSize()获取的storage单位是MB
        String storage = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
        if (StringUtils.isEmpty(storage)) {
            storage = String.valueOf(custins.getDiskSize());
        } else {
            storage = Long.toString(Long.parseLong(storage) * 1024L); // MB
        }

        if (storage != null) {
            InstancePerfDO instancePerf = this.instanceService.getInstancePerfByCustinsId(custins.getId(), 0);
            String diskCurr;
            if (instancePerf != null && instancePerf.getDiskCurr() != null) {
                diskCurr = instancePerf.getDiskCurr();
            } else {
                diskCurr = custins.getDiskSize().toString();
            }

            Long diskUsed = (new BigDecimal(diskCurr)).longValue();

            // 判断是否mysql高可用带只读 存储空间限制 > min{使用量*1.2, 使用量+400}
            List<CustInstanceDO> readCustInstances = custinsService.getReadCustInstanceListByPrimaryCustinsId(custins.getId(), false);
            if (custins.isRead() || !readCustInstances.isEmpty()) {
                if (Double.parseDouble(storage) <= Math.min((double)diskUsed * CustinsSupport.DISK_SIZE_REDUCE_RULE, diskUsed + 400 * 1024)) {
                    logger.error("requestId: {}, custins {}, request diskSize {} is less than current used size {} * 1.2: ", requestId, custins.getInsName(), storage, diskCurr);
                    String errMsg = String.format("Request disksize %s is less than current used size %s * %s", storage, diskCurr, CustinsSupport.DISK_SIZE_REDUCE_RULE);
                    throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "InvalidParam.DiskSize", errMsg});
                }
            } else {
                if (Double.parseDouble(storage) <= (double) diskUsed * CustinsSupport.DISK_SIZE_REDUCE_RULE) {
                    logger.error("requestId: {}, custins {}, request diskSize {} is less than current used size {} * 1.2: ", requestId, custins.getInsName(), storage, diskCurr);
                    String errMsg = String.format("Request disksize %s is less than current used size %s * %s", storage, diskCurr, CustinsSupport.DISK_SIZE_REDUCE_RULE);
                    throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "InvalidParam.DiskSize", errMsg});
                }
            }
        }
    }


    public void validateLocationSupportK8s(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 检查源实例所在subdomain是否支持k8s, k8s开区后会有对应集群可用
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);

        // 元数据cluster check新架构是否开区成功
        if (!checkNewArmClusterSupport(custins)) {
            ClustersDO clustersDO = clusterService.getClusterByCustinsId(custins.getId().longValue());
            logger.error("requestId: {}, custins {} location is {}, but k8s cluster not found, may not support modify to k8s or instance must migrateZone to other Zone first",
                    requestId, custins.getInsName(), clustersDO.getLocation());
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }
    }

    public void validateCrossRegion(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // 这里的region实际上是subdomain
        String requestId = getParameterValue(params, ParamConstants.REQUEST_ID);
        // 获得region以及旧region
        String region = avzSupport.getMainLocation(params);
        String oldRegion = avzSupport.getCustInstanceMainLocation(custins.getId());

        //跨region迁移
        if (!StringUtils.isBlank(region) && !oldRegion.equals(region)) {
            logger.error("requestId: {}. custins {}, from region {} to {}, Cross region is not support", requestId, custins.getInsName(), oldRegion, region);
            throw new RdsException(ErrorCode.CROSS_REGION_TRANS_NOT_ALLOWED);
        }
    }

    public Map<String, Object> validateEngineVersion(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        // todo
        return createErrorResponse(ErrorCode.INVALID_REGION);
    }

    /**
     * 获取新的level信息
     */
    public InstanceLevelDO getNewLevel(CustInstanceDO custins, Map<String, String> params, String dbVersion) {
        String levelCode = mysqlParamSupport.getParameterValue(params, ParamConstants.TARGET_DB_INSTANCE_CLASS);
        InstanceLevelDO newLevel;
        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if (Validator.isNull(levelCode)) {
            newLevel = oldLevel;
        } else {
            newLevel = instanceService.getInstanceLevelByClassCode(
                    levelCode, custins.getDbType(), dbVersion, custins.getTypeChar(), null);
        }
        return newLevel;
    }

    /**
     * 获取新的磁盘大小
     */
    public Long getDiskSize(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        Long diskSize;
        String storage = mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE);
        diskSize = custinsService.getAndCheckDiskSize(custins, storage, getAndCheckBizType(params));
        return diskSize;
    }

    public boolean isActiveOperation(String statusDesc) {
        return CustinsState.STATE_MAINTAINING.getComment().equals(statusDesc);
    }

    /**
     * 检查是否开区cluster是否完成
     */
    public Boolean checkNewArmClusterSupport(CustInstanceDO custins) {

        ClustersQuery clustersQuery = new ClustersQuery();
        clustersQuery.setDbType(GLOBAL_DB_TYPE);
        clustersQuery.setIsAvail(1);
        List<ClustersDO> clustersDOList = clusterIDao.getClusters(clustersQuery);
        List<ClustersDO> filteredClusters;
        if (checkIsClassicDispenseMode(custins)) {
            String masterSiteNameByLB = getMasterSiteNameByLB(custins);
            filteredClusters = clustersDOList.stream().filter(clustersDO -> clustersDO.getSiteName() != null && clustersDO.getSiteName().contains(masterSiteNameByLB)).collect(Collectors.toList());
        } else {
            ClustersDO clusterDO = clusterService.getClusterByCustinsId(custins.getId().longValue());
            filteredClusters = clustersDOList.stream().filter(clustersDO -> clustersDO.getLocation() != null && clustersDO.getLocation().equals(clusterDO.getLocation())).collect(Collectors.toList());
        }
        return !filteredClusters.isEmpty();
    }

    // 获取本地盘主备节点所在cluster当前最新的location信息
    public AVZInfo getCustinsCurrentAVZInfo(CustInstanceDO custins)  throws RdsException {
        AvailableZoneInfoDO  masterAvailableZoneInfo = null;
        AvailableZoneInfoDO  slaveAvailableZoneInfo = null;
        String regionID = null;
        String regionCategory = null;
        String subDomain = null;
        String zoneID = null;

        ParamConstants.DispenseMode dispenseMode = custinsParamService.getDispenseMode(custins.getId());
        MultiAVZExParamDO multiAVZExParamDO = null;
        RegionAVZoneQuery query;
        List regionAVZoneList;
        RegionAVZonDO regionAVZonDO;
        if (dispenseMode.equals(ParamConstants.DispenseMode.MultiAVZDispenseMode)) {
            List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
            Map<String, Object> HostRoleMap = new HashMap<String, Object>();
            for (InstanceDO instanceDO: instanceList) {
                String hostId = String.valueOf(instanceDO.getHostId());
                Integer role = instanceDO.getRole();
                HostRoleMap.put(hostId, role);
            }

            List<Map<String, Object>> hostInfoList = hostIDao.getHostInfoByCustinsId(custins.getId());
            for (Map<String, Object> hostInfoObject: hostInfoList) {
                String hostId = hostInfoObject.get("HostId").toString();
                String clusterName = hostInfoObject.get("ClusterName").toString();
                ClustersDO cluster = clusterService.getClusterByClusterName(clusterName);
                subDomain = cluster != null ? cluster.getLocation() : null;
                // rds_region_avzone表
                regionAVZonDO = resourceService.getRegionAVZoneBySubDomain(subDomain);

                if (HostRoleMap.get(hostId).equals(0)) {
                    masterAvailableZoneInfo = new AvailableZoneInfoDO();
                    masterAvailableZoneInfo.setRole("master");
                    masterAvailableZoneInfo.setRegion(regionAVZonDO.getSubDomain());
                    masterAvailableZoneInfo.setZoneID(regionAVZonDO.getAvz());
                    masterAvailableZoneInfo.setUserSpecified(true);
                    regionID = regionAVZonDO.getRegion();
                    zoneID = regionAVZonDO.getAvz();
                    regionCategory = regionAVZonDO.getRegionCategory();
                } else {
                    slaveAvailableZoneInfo = new AvailableZoneInfoDO();
                    slaveAvailableZoneInfo.setRole("slave");
                    slaveAvailableZoneInfo.setRegion(regionAVZonDO.getSubDomain());
                    slaveAvailableZoneInfo.setZoneID(regionAVZonDO.getAvz());
                    slaveAvailableZoneInfo.setUserSpecified(true);
                }
            }

            multiAVZExParamDO = new MultiAVZExParamDO();
            List<AvailableZoneInfoDO>  availableZoneInfos = new ArrayList<>();
            availableZoneInfos.add(masterAvailableZoneInfo);
            availableZoneInfos.add(slaveAvailableZoneInfo);
            multiAVZExParamDO.setAvailableZoneInfoList(availableZoneInfos);
        } else {
            subDomain = clusterService.getRegionByCluster(custins.getClusterName());
            query = new RegionAVZoneQuery();
            query.setSubDomain(subDomain);
            regionAVZoneList = resourceService.getRegionAVZoneList(query);
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(regionAVZoneList)) {
                regionAVZonDO = (RegionAVZonDO)regionAVZoneList.get(0);
                regionID = regionAVZonDO.getRegion();
                zoneID = regionAVZonDO.getAvz();
            }
        }

        return new AVZInfo(dispenseMode, subDomain, zoneID, (String)null, regionID, regionCategory, multiAVZExParamDO);

    }

    public AvailableZoneInfoDO getMultiAVZExParamFromRegion(String region, String role) throws RdsException {
        AvailableZoneInfoDO availableZoneInfo = new AvailableZoneInfoDO();
        RegionAVZoneQuery query = new RegionAVZoneQuery();
        query.setSubDomain(region);
        List<RegionAVZonDO> regionAVZoneList = this.resourceService.getRegionAVZoneList(query);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(regionAVZoneList)) {
            throw new RdsException(ErrorCode.INVALID_REGION);
        }

        RegionAVZonDO regionAVZonDO = regionAVZoneList.get(0);
        availableZoneInfo.setRole(role);
        availableZoneInfo.setRegion(regionAVZonDO.getSubDomain());
        availableZoneInfo.setZoneID(regionAVZonDO.getAvz());
        availableZoneInfo.setUserSpecified(true);
        return availableZoneInfo;
    }

    public AvailableZoneInfoDO getMultiAVZExParamFromInsCluster(CustInstanceDO custins, String role) throws RdsException {
        String region = this.clusterService.getRegionByCluster(custins.getClusterName());
        RegionAVZoneQuery query = new RegionAVZoneQuery();
        query.setSubDomain(region);
        List<RegionAVZonDO> regionAVZoneList = this.resourceService.getRegionAVZoneList(query);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(regionAVZoneList)) {
            throw new RdsException(ErrorCode.INVALID_REGION);
        }

        RegionAVZonDO regionAVZonDO = regionAVZoneList.get(0);

        AvailableZoneInfoDO availableZoneInfo = new AvailableZoneInfoDO();
        availableZoneInfo.setRole(role);
        availableZoneInfo.setRegion(regionAVZonDO.getSubDomain());
        availableZoneInfo.setZoneID(regionAVZonDO.getAvz());
        availableZoneInfo.setUserSpecified(true);
        return availableZoneInfo;
    }


    // 直接从参数或者主机的zone获取主备节点可用区信息
    public AVZInfo getAvzInfoFromParamAndCluster(CustInstanceDO custins) throws RdsException {
        String regionID = null;
        String regionCategory = null;
        String zoneID = null;
        String masterRegion = null;
        RegionAVZonDO regionAVZonDO = null;

        AvailableZoneInfoDO masterAvailableZoneInfo = null;
        AvailableZoneInfoDO slaveAvailableZoneInfo = null;

        // 获取主备instance的host_id
        List<InstanceDO> instanceList = instanceService.getInstanceByCustinsId(custins.getId());
        Map<Integer, Integer> HostRoleMap = new HashMap<Integer, Integer>();
        for (InstanceDO instanceDO: instanceList) {
            Integer hostId = instanceDO.getHostId();
            Integer role = instanceDO.getRole();
            HostRoleMap.put(role, hostId);
        }

        // 先从实例参数拿avz，如果拿不到再去实例集群查：备节点的信息会用主节点补充
        masterRegion = this.custinsParamService.getMasterLocation(custins.getId());
        if (StringUtils.isNotBlank(masterRegion)) {
            masterAvailableZoneInfo = getMultiAVZExParamFromRegion(masterRegion, "master");
        } else {
            masterAvailableZoneInfo = getMultiAVZExParamFromInsCluster(custins, "master");
            masterRegion = masterAvailableZoneInfo.getRegion();
        }

        String[] slaveRegions = this.custinsParamService.getSlaveLocations(custins.getId());
        if (slaveRegions.length > 0 && StringUtils.isNotBlank(slaveRegions[0])) {
            slaveAvailableZoneInfo = getMultiAVZExParamFromRegion(slaveRegions[0], "slave");
        } else {
            slaveAvailableZoneInfo = getMultiAVZExParamFromInsCluster(custins, "slave");
        }

        RegionAVZoneQuery query = new RegionAVZoneQuery();
        query.setSubDomain(masterRegion);
        List<RegionAVZonDO> regionAVZoneList = this.resourceService.getRegionAVZoneList(query);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(regionAVZoneList)) {
            throw new RdsException(ErrorCode.INVALID_REGION);
        }
        regionAVZonDO = regionAVZoneList.get(0);
        regionID = regionAVZonDO.getRegion();
        regionCategory = regionAVZonDO.getRegionCategory();
        zoneID = masterAvailableZoneInfo.getZoneID();

        MultiAVZExParamDO multiAVZParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> availableZoneInfos = new ArrayList<>();
        availableZoneInfos.add(masterAvailableZoneInfo);
        availableZoneInfos.add(slaveAvailableZoneInfo);
        multiAVZParamDO.setAvailableZoneInfoList(availableZoneInfos);

        return new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, masterRegion, zoneID, (String) null, regionID, regionCategory, multiAVZParamDO);
    }

    @Setter
    @Getter
    static class TempInsCreateParam {
        CustInstanceDO custins;
        PysicalToPodModifyInsParam podModifyInsParam;
        AVZInfo avzInfo;
        Boolean allocateDisk=false;
    }

    /**
     * 为临时实例分配实例资源：只读不包括链路和磁盘，主实例不包括链路，但得有磁盘
     * */
    public void allocateTmpResourceForHaRoIns(Map<String, AllocateTmpResourceResult> allocatedInsNameMap, List<TempInsCreateParam> insParamList, List<CustInstanceDO> readInsList, Map<String, String> primaryParams, String requestId) throws Exception {
        // 每个实例先依次获取自己的参数后再并发申请，避免全局参数被多线程更改  ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        for (CustInstanceDO readIns: readInsList) {
            Map<String, String> readInsParam = primaryParams
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            readInsParam.put("dbinstancename", readIns.getInsName());
            // 修改全局参数为只读实例的参数
            ActionParamsProvider.ACTION_PARAMS_MAP.set(readInsParam);
            // 创建只读实例时，订单参数依旧使用主实例order_id，(主实例orderId_只读实例名)作为临时只读实例名，用以判断临时实例是否重复分配
            // readInsParam.put("orderid", );
            long diskSizeGB = readIns.getDiskSize()/1024;
            readInsParam.put("storage", Long.toString(diskSizeGB));
            readInsParam.put("engineversion", readIns.getDbVersion());
            readInsParam.put("targetdbinstanceclass", primaryParams.get("ROTargetDBInstanceClass".toLowerCase()));
            readInsParam.put("multiavzexparam", "");

            // 忽略参数里的avz信息，重新获取只读当前最新的location信息
            AVZInfo roAvzInfo = getAvzInfoFromParamAndCluster(readIns);
            logger.info("[allocateTmpResourceForHaRoIns] roAvzInfo: " + JSON.toJSONString(roAvzInfo));
            readInsParam.put("multiavzexparam", JSON.toJSONString(roAvzInfo.getMultiAVZExParamDO()));
            readInsParam.put("region", roAvzInfo.getMainLocation());

            logger.info("[allocateTmpResourceForHaRoIns] readInsParam: " + JSON.toJSONString(readInsParam));

            validatePhysicalToK8sForRo(readIns, readInsParam);

            PysicalToPodModifyInsParam roPodModifyInsParam = initPodModifyInsParam(readInsParam);

            TempInsCreateParam insParam = new TempInsCreateParam();
            insParam.setCustins(readIns);
            insParam.setAvzInfo(roAvzInfo);
            insParam.setPodModifyInsParam(roPodModifyInsParam);
            insParam.setAllocateDisk(false);
            insParamList.add(insParam);
        }

        Function<TempInsCreateParam, String> allocateRes = createParam -> {
            try {
                // 创建临时实例
                AllocateTmpResourceResult allocateTmpResourceResult = createTempCustins(requestId, createParam.getCustins(), createParam.getPodModifyInsParam(), createParam.getAvzInfo(), createParam.getAllocateDisk());
                if (!allocateTmpResourceResult.isAllocated()) {
                    logger.error("[allocateTmpResourceForHaRoIns] tempCustins {} allocate resource failed",  createParam.getCustins().getInsName());
                    throw new RdsException(ErrorCode.CUSTINSSEARCH_CREATE_ERROR);
                }
                String destDBInstanceName = allocateTmpResourceResult.getResourceRequest().getReplicaSetName();
                CustInstanceDO tempCustins = custinsService.getCustInstanceByInsName(null, destDBInstanceName, 1);
                if (tempCustins == null) {
                    logger.error("[allocateTmpResourceForHaRoIns] tempCustins {} info is null, may allocate resource failed",  destDBInstanceName);
                    throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
                }
                allocatedInsNameMap.put(createParam.getCustins().getInsName(), allocateTmpResourceResult);

                // 临时实例同步
                custinsParamService.syncCustinsCipherChain(createParam.getCustins().getId(), tempCustins.getId(), createParam.getCustins().getDbType(), createParam.getCustins().getInsName());

                // sync db && accounts
                dbsService.syncAllDbsAndAccounts(createParam.getCustins(), tempCustins);

                // sync all ip white list group
                ipWhiteListService.syncCustinsIpWhiteList(createParam.getCustins().getId(), tempCustins.getId());

                return destDBInstanceName;
            } catch (Exception e) {
                logger.error(requestId + " [allocateTmpResourceForHaRoIns] error", e);
                throw new RuntimeException(e);
            }
        };

        List<CompletableFuture<String>> futures = insParamList.stream()
                .map(it -> CompletableFuture.completedFuture(it).thenApplyAsync(allocateRes))
                .collect(Collectors.toList());

        // get阻塞当前线程，直到所有CompletableFuture任务完成或者任务超时
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .exceptionally(e -> {
                        if (e instanceof InterruptedException){
                            // 重新设置中断标志
                            Thread.currentThread().interrupt();
                            String msg = " [allocateTmpResourceForHaRoIns] task thread interrupted";
                            logger.error(requestId + msg, e);
                            throw new RuntimeException(e);
                        }
                        return null;
                    }).get(300, TimeUnit.SECONDS);
        }
        catch (TimeoutException e) {
            String msg = " [allocateTmpResourceForHaRoIns] task timed out";
            logger.error(requestId + msg, e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", msg});
        }
        catch (ExecutionException e) {
            String msg = " [allocateTmpResourceForHaRoIns] execution task fail";
            logger.error(requestId + msg, e);
            throw new RdsException(new Object[]{ResultCode.CODE_ERROR, "MetaDataAllocateFailed", msg});
        }
    }

    public boolean checkIsClassicDispenseMode(CustInstanceDO custins) {
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custins.getId(), MysqlParamSupport.DISPENSE_MODE);
        if (custinsParamDO == null) {
            return false;
        }
        log.info("ClassicDispenseMode:{},param:{}", ParamConstants.DispenseMode.ClassicDispenseMode, custinsParamDO.getValue());
        return Objects.equals(ParamConstants.DispenseMode.ClassicDispenseMode.toString(), custinsParamDO.getValue());
    }

    public AVZInfo generateAvzInfoByLBAndHostInfo(CustInstanceDO custins, String regionId, String requestId) throws RdsException {
        //通过获取lb上的sitename来确定主可用区
        CustinsConnAddrDO custinsConnAddrDO = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(),
                CustinsSupport.NET_TYPE_VPC,
                CustinsSupport.RW_TYPE_NORMAL).get(0);
        IpResourceDO ipr = resourceService.getIpResourceByIpAndVpc(
                custinsConnAddrDO.getVip(),
                custinsConnAddrDO.getVpcId());

        ResourceDO resourceDO = resourceService.getResourceByResKey(PodDefaultConstants.GLOBAL_CLUSTER_SITENAME_TO_SUBDOMAIN_MAP_KEY);
        Gson gson = new Gson();
        Map<String, String> sitenametoSubdomainMap = gson.fromJson(resourceDO.getRealValue(), Map.class);
        logger.info("sitenametoSubdomainMap:{}", sitenametoSubdomainMap);
        //组合可用区，拼接出对应的单可用区avzInfo
        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO> availableZoneInfoDOList = new ArrayList<>();
        List<InstanceDO> instanceDOList = instanceService.getInstanceByCustinsId(custins.getId());
        boolean haveMaster = false;
        ClustersQuery clustersQuery = new ClustersQuery();
        clustersQuery.setDbType(GLOBAL_DB_TYPE);
        clustersQuery.setIsAvail(1);
        List<ClustersDO> clustersDOList = clusterIDao.getClusters(clustersQuery);
        for (InstanceDO instanceDO : instanceDOList) {
            HostInfo hostInfoDO = hostService.getHostInfo(instanceDO.getHostId(), null, null);
            String siteName = hostInfoDO.getSiteName();
            boolean isMaster = Objects.equals(siteName, ipr.getSiteName());
            AvailableZoneInfoDO zoneInfo = new AvailableZoneInfoDO();
            List<ClustersDO> clustersList = clustersDOList.stream().filter(it -> it.getSiteName().contains(siteName)).collect(Collectors.toList());

            if (clustersList.isEmpty()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
            }
            ClustersDO targetCluster = null;
            List<RegionAVZonDO> regionAVZonDOList = null;
            for (ClustersDO clusterDO : clustersList) {
                if (clusterDO.getLocation() == null) {
                    continue;
                }
                if (sitenametoSubdomainMap.containsKey(siteName) && !clusterDO.getLocation().equals(sitenametoSubdomainMap.get(siteName))) {
                    continue;
                }
                RegionAVZoneQuery query = new RegionAVZoneQuery();
                query.setSubDomain(clusterDO.getLocation());
                query.setBizType(podParameterHelper.getBizType(requestId, regionId).toString());
                regionAVZonDOList = resourceService.getRegionAVZoneList(query);
                if (!regionAVZonDOList.isEmpty()) {
                    targetCluster = clusterDO;
                    break;
                }
            }
            if (targetCluster == null) {
                throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
            }
            zoneInfo.setIsMaster(isMaster);
            zoneInfo.setVSwitchID(ipr.getvSwitchId());
            zoneInfo.setUserSpecified(true);
            zoneInfo.setRole(isMaster && !haveMaster ? "master" : "slave");
            zoneInfo.setRegion(targetCluster.getLocation());
            zoneInfo.setZoneID(regionAVZonDOList.get(0).getAvz());
            availableZoneInfoDOList.add(zoneInfo);
            haveMaster = isMaster;
        }
        multiAVZExParamDO.setAvailableZoneInfoList(availableZoneInfoDOList);
        return new AVZInfo(ParamConstants.DispenseMode.MultiAVZDispenseMode, multiAVZExParamDO.getMasterLocation(), regionId, null, multiAVZExParamDO);
    }

    public String getMasterZoneIdByLB(CustInstanceDO custins, String regionId) {
        CustinsConnAddrDO custinsConnAddrDO = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(),
                CustinsSupport.NET_TYPE_VPC,
                CustinsSupport.RW_TYPE_NORMAL).get(0);
        IpResourceDO ipr = resourceService.getIpResourceByIpAndVpc(
                custinsConnAddrDO.getVip(),
                custinsConnAddrDO.getVpcId());
        return zoneIDao.getZoneIdBySiteAndRegion(ipr.getSiteName(), regionId);
    }

    public String getMasterSiteNameByLB(CustInstanceDO custins) {
        CustinsConnAddrDO custinsConnAddrDO = connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(),
                CustinsSupport.NET_TYPE_VPC,
                CustinsSupport.RW_TYPE_NORMAL).get(0);
        IpResourceDO ipr = resourceService.getIpResourceByIpAndVpc(
                custinsConnAddrDO.getVip(),
                custinsConnAddrDO.getVpcId());
        return ipr.getSiteName();
    }
}
