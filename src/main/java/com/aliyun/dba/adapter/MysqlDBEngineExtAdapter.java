package com.aliyun.dba.adapter;


import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.annocation.ActionAnnotation;
import com.aliyun.dba.support.common.annocation.DubboProviderAnnotation;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Map;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;

@DubboProviderAnnotation(engine = "mysql", kindCode = "physical")
@Service("mysqlDBEngineExtAdapter")
public class MysqlDBEngineExtAdapter extends BaseDBEngineExtAdapter {

    @Override
    @ActionAnnotation(action = "CreateDBInstance")
    public Map<String, Object> create(CustInstanceDO custins, Map<String, String> params) {
        return this.getActionResult(custins, params);
    }



    @Override
    @ActionAnnotation(action = "CreateReadDBInstance")
    public Map<String, Object> createReadInstance(CustInstanceDO custins, Map<String, String> params) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "ExchangeDBInstance")
    public Map<String, Object> exchange(CustInstanceDO custins, Map<String, String> params) {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "CreateDdrDBInstance")
    public Map<String, Object> createDdr(CustInstanceDO custins, Map<String, String> params) {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "CheckCreateDdrDBInstance")
    public Map<String, Object> checkCreateDdr(CustInstanceDO custins, Map<String, String> params) {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceClass")
    public Map<String, Object> modify(CustInstanceDO custins, Map<String, String> params) throws RdsException {

        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "MigrateDBInstance")
    public Map<String, Object> modifyForMigrate(CustInstanceDO custins, Map<String, String> params) throws RdsException {
        return this.getActionResult(custins, params);
    }

    @Override
    @ActionAnnotation(action = "RestoreDBInstance")
    public Map<String, Object> restore(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * 恢复表到原实例
     */
    @Override
    @ActionAnnotation(action = "RestoreTable")
    public Map<String, Object> restoreTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RestoreDdrTable")
    public Map<String, Object> restoreDdrTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "EvaluateRegionResource")
    public Map<String, Object> evaluate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "UpgradeDBVersion")
    public Map<String, Object> upgradeDbVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }



    @Override
    @ActionAnnotation(action = "SwitchCustinsIsolateMode")
    public Map<String, Object> switchCustinsIsolateMode(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "EvaluateModifyRegionResource")
    public Map<String, Object> evaluateUpgrade(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceSSL")
    public Map<String, Object> modifySSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceSSL")
    public Map<String, Object> describeSSL(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CloneDBInstance")
    public Map<String, Object> clone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "EvaluateBakSet")
    public Map<String, Object> evaluateBakSet(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CheckCanUpgrade")
    public Map<String, Object> checkCanUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CloneDBInstanceForSecurity")
    public Map<String, Object> cloneForSecurity(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * mysql物理机版本使用该接口,暂不支持mysql5.1, ecs
     */
    @Override
    @ActionAnnotation(action = "DescribeKernelReleaseNotes")
    public Map<String, Object> describeKernelReleaseNote(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifySQLDelay")
    public Map<String, Object> modifySQLdelay(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RebuildSlaveInstance")
    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstance")
    public Map<String, Object> delete(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DestroyDBInstance")
    public Map<String, Object> destroy(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceUnavailableRecords")
    public Map<String, Object> failuredetail(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RestartDBInstance")
    public Map<String, Object> restart(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceRWType")
    public Map<String, Object> modifyDBInstanceRWType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "FlushAuroraProxyAccount")
    public Map<String, Object> flushAPAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CheckDBInstanceAcrossRegion")
    public Map<String, Object> checkDBInstanceAcrossRegion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * 静默迁移，要求必须是mysql实例,且不能是CustinsOnEcs实例
     */
    @Override
    @ActionAnnotation(action = "SilentMigrate")
    public Map<String, Object> silentMigrate(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ResetRootPassword")
    public Map<String, Object> resetRootPassword(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * 只支持mysql物理机类型
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnType")
    public Map<String, Object> switchConnType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceSlaveLag")
    public Map<String, Object> getSlaveLag(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    /**
     * mysql物理机类型接口
     */
    @Override
    @ActionAnnotation(action = "RebuildSlaveInstance")
    public Map<String, Object> rebuildSlaveInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ExcuteDBInstanceCommand")
    public Map<String, Object> execCommand(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    //未迁移，不加注解
    @Override
    public Map<String, Object> attachNetworkInterface(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    //未迁移，不加注解
    @Override
    public Map<String, Object> createAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "CreateAccount")
    public Map<String, Object> createAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> deleteAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "DescribeAccountList")
    public Map<String, Object> describeAccountList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    //物理机 不支持停机实例
    @Override
    public Map<String, Object> stopDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }
    @Override
    public Map<String, Object> startDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> describeDBInstanceAllParameterList(CustInstanceDO custins,
                                                                  Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterChangeList(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeDBInstanceParameterList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeParameterTemplateList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> flushSysParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceNoTemplateParameter(CustInstanceDO custins,
                                                                   Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    public Map<String, Object> describeTaskIdByRequestID(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    @ActionAnnotation(action = "DescribeAnalyticdbByPrimaryDBInstance")
    public Map<String, Object> describeAnalyticdbByPrimaryDBInstance(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateDBInstanceNetType")
    public Map<String, Object> createDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {

        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBInstanceNetType")
    public Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "InspectDBInstance")
    public Map<String, Object> inspect(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyTaskRecoverTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    public Map<String, Object> describeDBInstanceTopoInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> migrateConnectionToOtherZone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> changeDiskPerfLevel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @ActionAnnotation(action = "RestartDBSlaveIns")
    @Override
    public Map<String, Object> restartDBSlaveIns(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> modifyDBInstanceVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeInstanceLevelList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceAutoUpgradeMinorVersion(CustInstanceDO custins,
                                                                       Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeTaskProgressList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeTaskProgressInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @ActionAnnotation(action = "MigratePengineToK8SInstance")
    public Map<String, Object> migratePengineToK8SInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {

        return null;
    }

    public Map<String, Object> migrateFailover(CustInstanceDO custins, Map<String, String> actionParams) {
        return null;
    }

    @ActionAnnotation(action = "TransferResourceRequest")
    public Map<String, Object> transferResourceRequest(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "UpgradeDBMajorVersion")
    public Map<String, Object> upgradeDbMajorVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyReverseNetForMigrateAcrossRegionViaVip")
    public Map<String, Object> modifyReverseNetForMigrateAcrossRegionViaVip(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "TransferToDBInstanceCrossNetViaVip")
    public Map<String, Object> transferToDBInstanceCrossNetViaVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "IsolateDBInstanceTmpfs")
    public Map<String, Object> isolateDBInstanceTmpfs(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CreateDBNodes")
    public Map<String, Object> createDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteDBNodes")
    public Map<String, Object> deleteDBNodes(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> createAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceAttribute(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceNetInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceClass(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceDataSource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccounts(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> resetAnalyticAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> suspendAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> startAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    /**
     * 创建蓝绿部署
     */
    @Override
    @ActionAnnotation(action = "CreateBlueGreenDeployment")
    public Map<String, Object> createBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 蓝绿实例切换
     */
    @Override
    @ActionAnnotation(action = "SwitchBlueGreenInstance")
    public Map<String, Object> switchBlueGreenInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除蓝绿部署
     */
    @Override
    @ActionAnnotation(action = "DeleteBlueGreenDeployment")
    public Map<String, Object> deleteBlueGreenDeployment(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询蓝绿实例同步信息
     */
    @Override
    @ActionAnnotation(action = "DescribeBlueGreenSyncInfo")
    public Map<String, Object> describeBlueGreenSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 蓝绿实例切换预检查
     */
    @Override
    @ActionAnnotation(action = "SwitchBlueGreenInstancePreCheck")
    public Map<String, Object> switchBlueGreenInstancePreCheck(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "SwitchHostinsPerfMeta")
    public Map<String, Object> switchHostinsPerfMeta(CustInstanceDO custins, Map<String, String> actionParams) {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> evaluateAnalyticRegionResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "RefreshAllocateResource")
    public Map<String, Object> refreshAllocateResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 开启防篡改表功能
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceTrustTable")
    public Map<String, Object> modifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 创建防篡改表
     */
    @Override
    @ActionAnnotation(action = "CreateDBInstanceTrustTable")
    public Map<String, Object> createDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除防篡改表
     */
    @Override
    @ActionAnnotation(action = "DeleteDBInstanceTrustTable")
    public Map<String, Object> deleteDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 显示数据库中已有的防篡改表
     */
    @Override
    @ActionAnnotation(action = "ListDBInstanceTrustTable")
    public Map<String, Object> listDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 防篡改表定时签名
     */
    @Override
    @ActionAnnotation(action = "SignDBInstanceTrustTable")
    public Map<String, Object> signDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 校验防篡改表
     */
    @Override
    @ActionAnnotation(action = "VerifyDBInstanceTrustTable")
    public Map<String, Object> verifyDBInstanceTrustTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 判断是否支持在线扩容
     */
    @Override
    @ActionAnnotation(action = "DescribeSupportOnlineResizeDisk")
    public Map<String, Object> describeSupportOnlineResizeDisk(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 创建全密态规则
     */
    @Override
    @ActionAnnotation(action = "CreateMaskingRules")
    public Map<String, Object> createMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 修改全密态规则
     */
    @Override
    @ActionAnnotation(action = "ModifyMaskingRules")
    public Map<String, Object> modifyMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 删除全密态规则
     */
    @Override
    @ActionAnnotation(action = "DeleteMaskingRules")
    public Map<String, Object> deleteMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询全密态规则
     */
    @Override
    @ActionAnnotation(action = "DescribeMaskingRules")
    public Map<String, Object> describeMaskingRules(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 修改用户全密态授权信息
     */
    @Override
    @ActionAnnotation(action = "ModifyAccountMaskingPrivilege")
    public Map<String, Object> modifyAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 查询用户全密态授权信息
     */
    @Override
    @ActionAnnotation(action = "DescribeAccountMaskingPrivilege")
    public Map<String, Object> describeAccountMaskingPrivilege(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 列加密全局配置API：算法，黑白名单模式，密钥模式等
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceCLS")
    public Map<String, Object> modifyDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    /**
     * 列加密全局配置查询API：算法，黑白名单模式，密钥模式等
     */
    @Override
    @ActionAnnotation(action = "DescribeDBInstanceCLS")
    public Map<String, Object> describeDBInstanceCLS(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 大版本升级预检查
     */
    @Override
    @ActionAnnotation(action = "UpgradeMajorVersionPreCheck")
    public Map<String, Object> upgradeMajorVersionPreCheck(CustInstanceDO custins, Map<String, String> actionParms) throws RdsException {
        return this.getActionResult(custins, actionParms);
    }

    /**
     * 大版本升级预检查结果
     */
    @Override
    @ActionAnnotation(action = "DescribeUpgradeMajorVersionResult")
    public Map<String, Object> describeUpgradeMajorVersionResult(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 内部接口小版本升级且刷参
     */
    @Override
    @ActionAnnotation(action = "UpgradeMinorVersionAndFlushParam")
    public Map<String, Object> upgradeMinorVersionAndFlushParam(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnectionDraining")
    public Map<String, Object> modifyDBInstanceConnectionDraining(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RepairDBInstanceParameter")
    public Map<String, Object> repairDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeDBInstanceStorageCompression")
    public Map<String, Object> describeDBInstanceStorageCompression(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "ExchangeReadOnlyInstanceToPrimary")
    public Map<String, Object> exchangeReadOnlyInstanceToPrimary(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /*
    本地盘实例恢复
     */
    @Override
    @ActionAnnotation(action = "RecoverDBInstance")
    public Map<String, Object> recoverDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    /**
     * 修改实例vip可用性
     * @param custins
     * @param actionParams
     * @return
     */
    @Override
    @ActionAnnotation(action = "ModifyDBInstanceVIPAvailable")
    public Map<String, Object> modifyDBInstanceVIPAvailable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
}
