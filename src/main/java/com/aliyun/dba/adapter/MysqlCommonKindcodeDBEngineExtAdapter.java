package com.aliyun.dba.adapter;

import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.support.common.annocation.ActionAnnotation;
import com.aliyun.dba.support.common.annocation.DubboProviderAnnotation;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Map;


@DubboProviderAnnotation(engine = "mysql", kindCode = "commonkindcode")
@Service("mysqlCommonKindcodeDBEngineExtAdapter")
public class MysqlCommonKindcodeDBEngineExtAdapter extends BaseDBEngineExtAdapter {

    @Override
    public Map<String, Object> create(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    public Map<String, Object> createReadInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    public Map<String, Object> exchange(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    public Map<String, Object> createDdr(CustInstanceDO custins, Map<String, String> params)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    public Map<String, Object> checkCreateDdr(CustInstanceDO custins, Map<String, String> params)
            throws RdsException, UnsupportedEncodingException {
        return null;
    }

    @Override
    public Map<String, Object> modify(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyForMigrate(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restore(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restoreTable(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restoreDdrTable(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> evaluate(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> upgradeDbVersion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> switchCustinsIsolateMode(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
    }

    @Override
    public Map<String, Object> evaluateUpgrade(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifySSL(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeSSL(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> clone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> evaluateBakSet(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> checkCanUpgrade(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> cloneForSecurity(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeKernelReleaseNote(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifySQLdelay(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> rebuild(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> delete(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> destroy(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> failuredetail(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restart(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceRWType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> flushAPAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> checkDBInstanceAcrossRegion(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> silentMigrate(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> resetRootPassword(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> switchConnType(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> getSlaveLag(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> rebuildSlaveInstance(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> execCommand(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> attachNetworkInterface(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAdminAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAccount(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAccountList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    //物理机 不支持停机实例
    @Override
    public Map<String, Object> stopDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> startDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }


    @ActionAnnotation(action = "DescribeDBInstanceAllParameterList")
    @Override
    public Map<String, Object> describeDBInstanceAllParameterList(CustInstanceDO custins,
                                                                  Map<String, String> actionParams)
        throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeDBInstanceParameterChangeList")
    @Override
    public Map<String, Object> describeDBInstanceParameterChangeList(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeDBInstanceParameterList")
    @Override
    public Map<String, Object> describeDBInstanceParameterList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "DescribeParameterTemplateList")
    @Override
    public Map<String, Object> describeParameterTemplateList(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "FlushSysParameter")
    @Override
    public Map<String, Object> flushSysParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyDBInstanceNoTemplateParameter")
    @Override
    public Map<String, Object> modifyDBInstanceNoTemplateParameter(CustInstanceDO custins,
                                                                   Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyDBInstanceParameter")
    @Override
    public Map<String, Object> modifyDBInstanceParameter(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }


    public Map<String, Object> describeTaskIdByRequestID(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeAnalyticdbByPrimaryDBInstance(CustInstanceDO custins,
                                                                     Map<String, String> actionParams)
            throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> createDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    @ActionAnnotation(action = "InspectDBInstance")
    public Map<String, Object> inspect(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
    }

    @Override
    public Map<String, Object> modifyTaskRecoverTime(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @ActionAnnotation(action = "DescribeDBInstanceTopoInfo")
    @Override
    public Map<String, Object> describeDBInstanceTopoInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "MigrateConnectionToOtherZone")
    public Map<String, Object> migrateConnectionToOtherZone(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> changeDiskPerfLevel(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> restartDBSlaveIns(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyDBInstanceVip(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
//    @ActionAnnotation(action = "DescribeInstanceLevelList")
    public Map<String, Object> describeInstanceLevelList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "ModifyDBInstanceAutoUpgradeMinorVersion")
    @Override
    public Map<String, Object> modifyDBInstanceAutoUpgradeMinorVersion(CustInstanceDO custins,
                                                                       Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> describeTaskProgressList(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeTaskProgressInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    public Map<String, Object> migratePengineToK8SInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> checkServiceLinkedRoleForDeleting(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException, UnsupportedEncodingException {
        return this.getActionResult(custins, actionParams);
    }

    public Map<String, Object> migrateFailover(CustInstanceDO custins, Map<String, String> actionParams) {
        return null;
    }

    @Override
    @ActionAnnotation(action = "ModifyDBInstanceConnectionString")
    public Map<String, Object> modifyConnStr(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CustomerApplyResource")
    public Map<String, Object> customerApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeApplyResource")
    public Map<String, Object> describeApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeZoneInfo")
    public Map<String, Object> describeZoneInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CancelApplyResource")
    public Map<String, Object> cancelApplyResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "GetLatestEngineImage")
    public Map<String, Object> getLatestEngineImage(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    //for data api
    @Override
    @ActionAnnotation(action = "CreateSecret")
    public Map<String, Object> createSecret(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DeleteSecret")
    public Map<String, Object> deleteSecret(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "DescribeSecrets")
    public Map<String, Object> describeSecrets(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "RetrieveSecretValue")
    public Map<String, Object> retrieveSecretValue(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    @ActionAnnotation(action = "CustinsConfigure")
    public Map<String, Object> custinsConfigure(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }
    @Override
    @ActionAnnotation(action = "DescribeDetachedInsBackupInfo")
    public Map<String, Object> describeDetachedInsBackupInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @Override
    public Map<String, Object> createAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceAttribute(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceNetInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceClass(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstance(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticDBInstanceNetType(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceConnectionString(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticDBInstanceDataSource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> createAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> deleteAnalyticAccount(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeAnalyticAccounts(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> describeSyncInfo(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountDescription(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticAccountPermission(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> resetAnalyticAccountPassword(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> suspendAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> startAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> modifyAnalyticDBInstanceSyncJob(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @Override
    public Map<String, Object> evaluateAnalyticRegionResource(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
        return null;
    }

    @ActionAnnotation(action = "SwitchDBInstanceHA")
    @Override
    public Map<String, Object> switchDBInstanceHA(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

    @ActionAnnotation(action = "SwitchOverHostInstances")
    public Map<String, Object> switchOverHostInstances(CustInstanceDO custins, Map<String, String> actionParams)
            throws RdsException {
        return this.getActionResult(custins, actionParams);
    }

}
