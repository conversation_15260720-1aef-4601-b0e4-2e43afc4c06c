package com.aliyun.dba.dockerdefault.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.apsaradb.activityprovider.ApiException;
import com.aliyun.apsaradb.activityprovider.model.ReplicaResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.activityprovider.model.VolumeSpec;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.User;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.common.exception.BaseServiceException;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.ActionParamsProvider;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.parameter.backup.BackupSetParam;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.*;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.dataobject.EcsDiskDO;
import com.aliyun.dba.ecs.service.EcsDiskService;
import com.aliyun.dba.ecs.support.EcsConstants;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.support.common.action.IAction;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.utils.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.dba.common.ResponseSupport.createErrorResponse;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_BASIC;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_STANDARD;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.*;

@Slf4j
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
@Component("dockerdefaultDispatchReplicaChecksumImpl")
public class DispatchReplicaChecksumImpl implements IAction {
    @Resource
    private DBaasMetaService dBaasMetaService;
    @Resource
    private MysqlParamSupport mysqlParamSupport;
    @Resource
    private AVZSupport avzSupport;
    @Resource
    private CommonProviderService commonProviderService;
    @Resource
    private CustinsService custinsService;
    @Resource
    private MySQLAvzService mySQLAvzService;
    @Resource
    private BakService bakService;
    @Resource
    private ReplicaSetService replicaSetService;
    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Resource
    private MysqlParameterHelper mysqlParameterHelper;
    @Resource
    private PodParameterHelper podParameterHelper;
    @Resource
    private InstanceService instanceService;
    @Resource
    private BackupService backupService;
    @Resource
    private ClusterService clusterService;
    @Resource
    protected ResourceService resourceService;
    @Resource
    private PodTemplateHelper podTemplateHelper;
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private EcsDiskService ecsDiskService;
    @Resource
    private CustinsParamService custinsParamService;

    Set<String> supportDBVersion = new HashSet<String>() {
        {
            add("8.0");
            add("5.7");
        }
    };

    Set<String> supportCloudEssdPL = new HashSet<String>() {
        {
            add("PL1");
            add("PL2");
            add("PL3");
        }
    };

    public final String SUBDOMAIN_LOCATION_MAP = "SUBDOMAIN_LOCATION_MAP";

    @Override
    public Map<String, Object> doActionRequest(CustInstanceDO logicCustins, Map<String, String> params) throws RdsException {
        ActionParamsProvider.ACTION_PARAMS_MAP.set(params);
        String requestId = mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID);
        String destReplicaSetName = null;
        boolean isSuccess = false;
        boolean isAllocated = false;
        try {
            logicCustins = mysqlParameterHelper.getAndCheckCustInstance();
            // 校验
            //  logicCustins
            if (logicCustins.getParentId() != 0) {  // 包含父实例，拿到logic
                logicCustins = custinsService.getCustInstanceByCustinsId(logicCustins.getParentId());
            }
            if (!logicCustins.isPrimary()) {  // 不支持主实例以外的实例
                log.error("DispatchReplicaChecksum, unsupported ins type: {}", logicCustins.getInsType());
                throw new RdsException(ErrorCode.INVALID_INS_TYPE);
            }
            String dbInstanceName = logicCustins.getInsName();
            destReplicaSetName = String.format("checksum-%s", dbInstanceName);
            ReplicaSet destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, destReplicaSetName, true);
            if (destReplicaSet != null) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }

            //  校验 bizType cluster
            if (!PodParameterHelper.isAliYun(logicCustins.getBizType()) || mysqlParamSupport.isDHGCluster(logicCustins.getClusterName())) {
                log.error("Only support biz type = aliyun And not is dhg.");
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE);
            }
            ClustersDO clustersDO = clusterService.getClusterByClusterName(logicCustins.getClusterName());
            ReplicaSet.BizTypeEnum bizType = podParameterHelper.getBizType(requestId, clustersDO.getRegion());
            String uid = mysqlParamSupport.getUID(params);
            String bid = mysqlParamSupport.getBID(params);

            // 校验版本是否支持，新架构只支持 5.7 8.0
            String dbVersion = logicCustins.getDbVersion();
            if (!supportDBVersion.contains(dbVersion)) {
                log.error("Unsupported the db version");
                throw new RdsException(ErrorCode.INVALID_PARAM_DB_VERSION);
            }

            // 校验实例系列
            InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(logicCustins.getLevelId());
            if (!(oldLevel.getCategory().equalsIgnoreCase(CATEGORY_BASIC) || oldLevel.getCategory().equalsIgnoreCase(CATEGORY_STANDARD))) {
                log.info("Unsupported the category");
                throw new RdsException(ErrorCode.INVALID_PARAM_CATEGORY);
            }

            // 获取 physical 信息
            CustInstanceDO physicalCustins;
            boolean isDockerOnEcs = logicCustins.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS);
            boolean isEcs = logicCustins.getKindCode().equals(KIND_CODE_ECS_VM);
            if (isDockerOnEcs) {
                // 当前Pengine实例的physical实例
                if (StringUtils.equalsIgnoreCase(logicCustins.getCharacterType(), CustinsSupport.CHARACTER_TYPE_PHYSICAL)) {
                    physicalCustins = logicCustins;
                } else {
                    List<CustInstanceDO> childCustinsList = custinsService.getCustInstanceUnitByParentIdAndCharacterType(
                            logicCustins.getId(), CustinsSupport.CHARACTER_TYPE_PHYSICAL);
                    if (childCustinsList.size() == 0) {
                        throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_CHARACTER_TYPE);
                    }
                    physicalCustins = childCustinsList.get(0);
                }
            } else if (isEcs) {
                physicalCustins = logicCustins;
            } else {
                log.error("only support kind code in (1, 3)");
                throw new RdsException(ErrorCode.UNSUPPORTED_KIND_CODE);
            }

            // 校验磁盘类型
            String diskType = getPrimaryCustinsStorageType(physicalCustins.getId());  // 从原physical来获取磁盘类型

            // user 信息
            String userId = bid + "_" + uid;
            User user = dBaasMetaService.getDefaultClient().getUser(requestId, userId, false);

            // 备份
            String backupSetId = mysqlParamSupport.getParameterValue(params, ParamConstants.BACKUP_SET_ID);  // 指定备份集
            BakhistoryDO bakHistory;
            if (StringUtils.isNotEmpty(backupSetId)) {
                bakHistory = bakService.getBakhistoryByBackupSetId(logicCustins.getId(), Long.valueOf(backupSetId));
                if (bakHistory == null) {
                    log.error("logic custins backup_set {} not found.", backupSetId);
                    throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
                } else if (!"ok".equalsIgnoreCase(bakHistory.getStatus()) || bakHistory.getIsAvail() != 1) {
                    log.error("logic custins backup_set {} status {} isAvail {}.", backupSetId, bakHistory.getStatus(), bakHistory.getIsAvail());
                    throw new RdsException(ErrorCode.INVALID_BAKSET);
                }
            } else {  // 不指定备份集
                Date latestTime = new Date();
                bakHistory = bakService.getBakhistoryByRecoverTime(logicCustins.getId(), latestTime, null, BakSupport.BAKTYPE_FULL);
            }
            if (bakHistory == null) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            String snapshot = getTargetSnapshotId(logicCustins, user, bakHistory);
            boolean isPengineBackupSet = !bakHistory.getKindCode().equals(KIND_CODE_NEW_ARCH);

            String printLogsString = mysqlParamSupport.getParameterValue(params, "printLogs");
            Integer printLogs = 0;
            if (printLogsString != null) {
                printLogs = CheckUtils.parseInt(printLogsString, null, null, ErrorCode.INVALID_PARAM);
            }

            ReplicaSetResourceRequest replicaSetResourceRequest = buildReplicaSetResourceRequest(
                    requestId,
                    params,
                    logicCustins,
                    dbVersion,
                    oldLevel,
                    destReplicaSetName,
                    bizType,
                    diskType,
                    snapshot
            );


            log.info(" dockerOnEcs instance start allocate k8s resource: {}", JSON.toJSONString(replicaSetResourceRequest));
            isAllocated = commonProviderService.getDefaultApi().allocateReplicaSetResourceV1(requestId, destReplicaSetName, replicaSetResourceRequest);

            // 修改元数据，改成临时实例
            destReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(requestId, destReplicaSetName, false);
            destReplicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
            dBaasMetaService.getDefaultClient().updateReplicaSet(requestId, destReplicaSetName, destReplicaSet);
            Map<String, String> labels = destReplicaSet.getLabels();
            if (labels == null) {
                labels = new HashMap<>();
            }
            labels.put(MySQLParamConstants.IS_INTERNAL, "1");
            dBaasMetaService.getDefaultClient().updateReplicaSetLabels(requestId, destReplicaSetName, labels);

            String taskKey = PodDefaultConstants.CHECK_REPLICA_IBD;
            String domain = PodDefaultConstants.DOMAIN_MYSQL;
            JSONObject taskParam = new JSONObject();
            taskParam.put("srcReplicaSetName", dbInstanceName);
            taskParam.put("isPengineBackupSet", isPengineBackupSet);
            taskParam.put("destReplicaSetName", destReplicaSetName);
            taskParam.put("printLogs", printLogs);
            taskParam.put("bakHisID", bakHistory.getHisId());

            Object taskId = workFlowService.dispatchTask("custins", destReplicaSetName, domain, taskKey, taskParam.toJSONString(), 0);

            Map<String, Object> ret = new HashMap<>();
            ret.put("TaskId", taskId);
            ret.put(ParamConstants.DB_INSTANCE_NAME, destReplicaSetName);
            ret.put(ParamConstants.DB_INSTANCE_ID, destReplicaSet.getId());
            ret.put(ParamConstants.BACKUP_SET_ID, bakHistory.getHisId());
            isSuccess = true;
            return ret;
        } catch (RdsException e) {
            log.error("execute failed with exception: {}", JSON.toJSONString(e));
            throw e;
        } catch (ApiException e) {
            log.error("common api calling, allocate resource failed: {}", JSON.toJSONString(e));
            return createErrorResponse(ErrorCode.RESOURCE_NOT_ENOUGH);
        } catch (Exception e) {
            log.error("dispatch checksum task failed: {}", JSON.toJSONString(e));
            return createErrorResponse(ErrorCode.INTERNAL_FAILURE);
        } finally {
            if (!isSuccess && isAllocated) {
                try {
                    commonProviderService.getDefaultApi().releaseReplicaSetResourceV1(requestId, destReplicaSetName);
                } catch (com.aliyun.apsaradb.activityprovider.ApiException e) {
                    log.error("release replicaSet failed, {}", JSON.toJSONString(e));
                }
            }
        }
    }

    private ReplicaSetResourceRequest buildReplicaSetResourceRequest(String requestId,
                                                                     Map<String, String> params,
                                                                     CustInstanceDO logicCustins,
                                                                     String dbVersion,
                                                                     InstanceLevelDO oldLevel,
                                                                     String destReplicaSetName,
                                                                     ReplicaSet.BizTypeEnum bizType,
                                                                     String diskType,
                                                                     String snapshot) throws Exception {
        String uid = mysqlParamSupport.getUID(params);
        String bid = mysqlParamSupport.getBID(params);
        String dbType = logicCustins.getDbType();

        ResourceDO resourceDO = resourceService.getResourceByResKey(PodDefaultConstants.CHECK_IBD_INSTANCE_LEVEL_KEY);
        String classCode;
        if (resourceDO != null) {
            String instanceLevelMapper = resourceDO.getRealValue();
            if (StringUtils.isBlank(instanceLevelMapper)) {
                throw new RdsException(ErrorCode.INVALID_KEY);
            }
            JSONObject mapper = (JSONObject) JSON.parse(instanceLevelMapper);
            classCode = mapper.getString("ClassCode");
        }
        else {
            classCode = oldLevel.getClassCode();
        }
        InstanceLevel instanceLevel = dBaasMetaService.getDefaultClient().getInstanceLevel(requestId, dbType, dbVersion, classCode, false);

        ReplicaSet.BizTypeEnum bizTypeEnum = ReplicaSet.BizTypeEnum.valueOf(logicCustins.getBizType().toUpperCase());
        Boolean isSingleTenant = replicaSetService.isCloudSingleTenant(bizTypeEnum, diskType, instanceLevel, false);

        AVZInfo avzInfo = avzSupport.getAVZInfoFromCustInstance(logicCustins);
        String subDomain = avzInfo.getRegion();
        String regionId = avzInfo.getRegionId();
        String zoneId = mySQLAvzService.getRoleZoneId(avzInfo, Replica.RoleEnum.MASTER.toString()).getZoneID();
        Integer diskSize = CheckUtils.parseInt(String.valueOf((logicCustins.getDiskSize() / 1024)), CustinsSupport.ECS_MIN_DISK_SIZE,
                102400, ErrorCode.INVALID_STORAGE);
        String serviceSpecTag = minorVersionServiceHelper.getServiceSpecTag(
                null,
                bizType,
                dbType,
                dbVersion,
                "MySQL",
                KindCodeParser.KIND_CODE_NEW_ARCH,
                instanceLevel,
                diskType,
                false,
                false
        );

        // build replicaSet resource
        ReplicaSetResourceRequest replicaSetResourceRequest = new ReplicaSetResourceRequest();
        replicaSetResourceRequest
                .userId(bid)
                .uid(uid)
                .insType(ReplicaSet.InsTypeEnum.MAIN.toString())
                .replicaSetName(destReplicaSetName)
                .connType(MySQLParamConstants.CONN_TYPE_PHYSICAL)
                .dbType(logicCustins.getDbType())
                .dbVersion(logicCustins.getDbVersion())
                .bizType(bizType.toString())
                .classCode(classCode)
                .subDomain(subDomain)
                .regionId(regionId)
                .catagory(instanceLevel.getCategory().toString())
                .storageType(diskType)
                .diskSize(diskSize)
                .composeTag(serviceSpecTag)
                .ignoreCreateVpcMapping(true)
                .eniDirectLink(false)
                .singleTenant(isSingleTenant);

        Pair<String, ScheduleTemplate> scheduleTemplate = podTemplateHelper.getBizSysScheduleTemplate(PodType.POD_RUNC,
                bizType, logicCustins.getDbType(), instanceLevel, isSingleTenant,
                ReplicaSet.InsTypeEnum.MAIN.toString(), logicCustins.getInsName(), null, uid);
        replicaSetResourceRequest.setScheduleTemplate(scheduleTemplate.getValue());

        // 初始化 replica 信息
        ReplicaResourceRequest replicaResourceRequest = new ReplicaResourceRequest();
        replicaResourceRequest
                .hostName(podParameterHelper.getRoleHostNameMapping().get(Replica.RoleEnum.MASTER))
                .role(Replica.RoleEnum.MASTER.toString())
                .storageType(diskType)
                .singleTenant(isSingleTenant)
                .zoneId(zoneId)
                .diskSize(diskSize)
                .classCode(classCode);
        // volume
        VolumeSpec volumeSpec = new VolumeSpec();
        volumeSpec.setSnapshotId(snapshot);
        volumeSpec.setName("data");
        if (diskType.equalsIgnoreCase(DockerOnEcsConstants.ECS_ClOUD_ESSD)) {
            volumeSpec.setPerformanceLevel("PL1");  // 指定PL1
        }
        replicaResourceRequest.setVolumeSpecs(Collections.singletonList(volumeSpec));

        // 赋值
        List<ReplicaResourceRequest> replicaResourceRequestList = new ArrayList<>();
        replicaResourceRequestList.add(replicaResourceRequest);
        replicaSetResourceRequest.setReplicaResourceRequestList(replicaResourceRequestList);

        return replicaSetResourceRequest;
    }

    /**
     * 获取 primary physical 实例 storageType
     */
    public String getPrimaryCustinsStorageType(Integer primaryCustinsId) throws Exception {

        String dataDiskCategory = null;
        if (primaryCustinsId != null) {
            List<EcsDiskDO> ecsDiskList = ecsDiskService.getEcsDiskByCustinsId(primaryCustinsId);
            if (ecsDiskList.size() == 0) {
                throw new RdsException(ErrorCode.INVALID_ECS_DATA_DISK_CATEGORY);
            }
            for (EcsDiskDO ecsDiskDO : ecsDiskList) {
                if (EcsConstants.DISK_TYPE_DATA.equals(ecsDiskDO.getType())) {
                    String performanLevel = ecsDiskService.getEcsTypeFromEcsDiskParamByDiskId(ecsDiskDO.getDiskId());
                    if (StringUtils.isEmpty(performanLevel)) {
                        dataDiskCategory = ecsDiskDO.getCategory();
                    } else {
                        if (supportCloudEssdPL.contains(performanLevel)) {
                            dataDiskCategory = CustinsParamSupport.getEssdPerLevel(performanLevel);
                        } else {
                            log.error("unsupport the cloud essd pl");
                            throw new RdsException(ErrorCode.UNSUPPORTED_EXTEND_DISK);
                        }
                    }
                }
            }

            if (DockerOnEcsConstants.ECS_ClOUD_ESSD2.equalsIgnoreCase(dataDiskCategory)
                    || DockerOnEcsConstants.ECS_ClOUD_ESSD3.equalsIgnoreCase(dataDiskCategory)
                    || DockerOnEcsConstants.ECS_ClOUD_ESSD0.equalsIgnoreCase(dataDiskCategory)) {
                dataDiskCategory = DockerOnEcsConstants.ECS_ClOUD_ESSD;
            }
        }

        return dataDiskCategory;
    }

    private String getTargetSnapshotId(CustInstanceDO custins, User user, BakhistoryDO bakHistory) throws RdsException, BaseServiceException {
        GetBackupSetResponse backupSetResponse;
        try {
            log.info("Start query backupSet: " + bakHistory.getHisId());
            backupSetResponse = backupService.getBackupSet(
                    BackupSetParam.builder()
                            .uid(user.getAliUid())
                            .user_id(user.getBid())
                            .dBInstanceId(custins.getId())
                            .backupSetId(Long.valueOf(bakHistory.getHisId()))
                            .build()
            );
        } catch (BaseServiceException ex) {
            log.error("GetBackupSet failed: ", ex);
            if (StringUtils.equalsIgnoreCase(ex.getCode(), "InvalidBackupSetID.NotFound")) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
            throw ex;
        }
        log.info("GetBackupSet success: {}", JSON.toJSONString(backupSetResponse));

        return  backupSetResponse.getSlaveStatusObj().getSnapshotId();
    }
}
