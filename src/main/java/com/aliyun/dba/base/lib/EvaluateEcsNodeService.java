package com.aliyun.dba.base.lib;

import com.alibaba.fastjson.JSON;
import com.alicloud.apsaradb.inventory.model.ECSStock;
import com.alicloud.apsaradb.inventory.model.SpecModificationRequest;
import com.alicloud.apsaradb.inventory.model.SpecModificationResponse;
import com.alicloud.apsaradb.k8s.provider.RmInventoryClient;
import com.alicloud.apsaradb.k8s.provider.response.MultipleDataResponse;
import com.alicloud.apsaradb.k8s.provider.rm_inventory.ModificationStockEcsRequest;
import com.alicloud.apsaradb.k8s.provider.rm_inventory.ModificationStockEcsRespData;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.ResourceScheduleHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 变配ECS资源评估服务
 */

@Service
@Slf4j
public class EvaluateEcsNodeService {

    private static final LogAgent logger = LogFactory.getLogAgent(EvaluateEcsNodeService.class);

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private ResourceScheduleHelper resourceScheduleHelper;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private ResourceService resourceService;

    @Resource
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;

    private final static String FORBID_LOCAL_UPGRADE_UIDS = "FORBID_LOCAL_UPGRADE_UIDS";

    private static String nameServiceUrl;

    @Value(value = "${rds.name-service.base-url}")
    public void setSavePath(String savePath) {
        nameServiceUrl = savePath;
    }

    private static volatile RmInventoryClient rmInventoryClient;

    private static RmInventoryClient getInventoryApiClient() throws Exception {
        if (rmInventoryClient == null) {
            synchronized (RmInventoryClient.class) {
                if (rmInventoryClient == null) {
                    rmInventoryClient = RmInventoryClient.NewClient(nameServiceUrl);
                }
            }
        }
        return rmInventoryClient;
    }

    /**
     * 评估ECS资源是否能本地变配（ReplicaSet维度）
     *
     * @param requestId
     * @param replicaSet
     * @param targetLevel
     * @return
     */
    public boolean evaluateReplicaSetCanLocalUpgrade(String requestId, ReplicaSet replicaSet, InstanceLevel targetLevel) throws Exception {
        if (!isAllowLocalUpgrade(replicaSet)) {
            return false;
        }
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSet.getName(), null, null, null, null);
        List<Replica> currentReplicas = listReplicasInReplicaSet.getItems();
        EvaluateResult evaluateResult = new EvaluateResult();
        for (Replica currentReplica : currentReplicas) {
            log.info("start check replica {} modify to class code {}", currentReplica.getId(), targetLevel.getClassCode());
            String availableEcsClassCode = evaluateNodeCanLocalUpgrade(requestId, replicaSet, currentReplica, targetLevel);
            if (availableEcsClassCode == null) {
                evaluateResult.addAvailableCount(false);
                log.info("end check replica {} modify to class code {}, available is false", currentReplica.getId(), targetLevel.getClassCode());
            } else {
                evaluateResult.addAvailableCount(true);
                evaluateResult.getTargetDetail().put(currentReplica, availableEcsClassCode);
                log.info("end check replica {} modify to class code {}, available is true", currentReplica.getId(), targetLevel.getClassCode());
            }
        }
        evaluateResult.setAvailable(evaluateResult.getNotAvailableCount() == 0);
        log.info("evaluate replica set {} modify to class code {}, available is {}", replicaSet.getName(), targetLevel.getClassCode(), evaluateResult.isAvailable());
        return evaluateResult.isAvailable();
    }

    public boolean evaluateReplicasCanLocalUpgrade(String requestId, ReplicaSet replicaSet, Map<Long, InstanceLevel> replica2LevelMapping) throws Exception {
        if (!isAllowLocalUpgrade(replicaSet)) {
            return false;
        }
        List<Replica> oriReplicas = dBaasMetaService.getDefaultClient().listReplicasInReplicaSet(
                requestId, replicaSet.getName(), null, null, null, null).getItems();
        Map<Long, Replica> id2ReplicaMapping = new HashMap<Long, Replica>() {{
            oriReplicas.forEach(rp -> put(rp.getId(), rp));
        }};
        EvaluateResult evaluateResult = new EvaluateResult();
        for (Long replicaId : replica2LevelMapping.keySet()) {
            Replica currentReplica = id2ReplicaMapping.get(replicaId);
            log.info("start check replica {} modify to class code {}", currentReplica.getId(), replica2LevelMapping.get(replicaId));
            String availableEcsClassCode = evaluateNodeCanLocalUpgrade(requestId, replicaSet, currentReplica, replica2LevelMapping.get(replicaId));
            if (availableEcsClassCode == null) {
                evaluateResult.addAvailableCount(false);
                log.info("end check replica {} modify to class code {}, available is false", currentReplica.getId(), replica2LevelMapping.get(replicaId));
            } else {
                evaluateResult.addAvailableCount(true);
                evaluateResult.getTargetDetail().put(currentReplica, availableEcsClassCode);
                log.info("end check replica {} modify to class code {}, available is true", currentReplica.getId(), replica2LevelMapping.get(replicaId));
            }
        }
        evaluateResult.setAvailable(evaluateResult.getNotAvailableCount() == 0);
        log.info("evaluate replica set {}}, available is {}", replicaSet.getName(), evaluateResult.isAvailable());
        return evaluateResult.isAvailable();
    }



    /**
     * 评估单租户单节点能否变配
     */
    public String evaluateNodeCanLocalUpgrade(String requestId, ReplicaSet replicaSet, Replica currentReplica, InstanceLevel targetLevel) throws Exception {
        //根据replica获取主机名
        String hostname = currentReplica.getHostName();
        //根据主机名获取主机信息
        EcsHost ecsHost = dBaasMetaService.getDefaultClient().getEcsHostByHostName(requestId, hostname, null);
        // get user
        User user = dBaasMetaService.getDefaultClient().getUser(requestId, replicaSet.getUserId(), null);

        SpecModificationRequest request = new SpecModificationRequest();
        request.setNormalizedResource(true);
        request.setUid(user.getAliUid());
        request.setBizType(replicaSet.getBizType().getValue());
        request.setAccount(ecsHost.getUserName());
        request.setRegionId(ecsHost.getRegion());
        request.setEngine(replicaSet.getService().toLowerCase());
        //设置要请求的ecs规格
        request.setCores(Long.valueOf(targetLevel.getCpuCores()));
        request.setMemory(BigDecimal.valueOf(targetLevel.getMemSizeMB() / 1024));
        request.setInstanceId(ecsHost.getEcsInsId());

        //评估单租户节点是否能变配。如果不加此参数，混开逻辑中可能匹配搭配多租户的配置
        request.setTenantType("single-tenant");
        //添加资源保障策略
        poddefaultResourceGuaranteeModelService.addResourceGuaranteeModelForLocalUpgrade(request, targetLevel);

        log.info("modificationStockEcs request is {}", request);
        SpecModificationResponse response = inventoryService.getDefaultApi().describeSpecModification(request);
        if (response.getCode() != 200) {
            logger.error("modificationStockEcs fail, requestId is {} error is {}", requestId, response.getMessage());
        } else {
            // inventory returns current + target class, we need filter current class here
            Optional<ECSStock> stock = response.getData().stream().filter(s -> !StringUtils.equals(s.getInstanceType(), ecsHost.getClassCode())).findFirst();
            logger.info("modificationStockEcs response is {}", response);
            if (!stock.isPresent()) {
                logger.error("modificationStockEcs fail, requestId is {} error is {}", requestId, "stock is null");
                return null;
            }

            // Do not throw exceptions when doing resource evaluation here, return null and do upgrade with cross machine.
            ReplicaResource replicaResource = dBaasMetaService.getDefaultClient().getReplica(requestId, currentReplica.getId(), null);
            // Evaluate ECS stock
            List<String> ecsClassCodes = inventoryService.evaluateEcsStock(targetLevel, replicaResource, stock.get().getCPU(), stock.get().getMemory());
            log.info("The result of evaluating the ecs class code is {}", ecsClassCodes);
            if (ecsClassCodes == null || ecsClassCodes.isEmpty()) {
                return null;
            }
            // Find and return available ECS class code
            return findAvailableClassCode(response.getData(), ecsClassCodes);
        }
        return null;
    }


    /**
     *
     * 单租户是否允许本地升级（白名单控制）
     *
     * @param replicaSet
     * @return
     */
    private boolean isAllowLocalUpgrade(ReplicaSet replicaSet) {
        try {
            ResourceDO resourceDO = resourceService.getResourceByResKey(FORBID_LOCAL_UPGRADE_UIDS);
            if (resourceDO != null && StringUtils.isNotEmpty(resourceDO.getRealValue())) {
                Set<String> whiteIds = JSON.parseObject(resourceDO.getRealValue(), Set.class);
                String loginId = replicaSet.getUserId();
                String[] arr = StringUtils.split(loginId, "_");
                if (arr != null && arr.length == 2) {
                    return !whiteIds.contains(arr[1]);  //在名名单中的，不允许本地升级
                }
            }
        } catch (Exception e) {
            //ignore
            logger.warn("Get FORBID_LOCAL_UPGRADE_UIDS failed, ignore", e);
        }
        return true; //默认都是允许
    }


    private String findAvailableClassCode(List<ECSStock> datas, List<String> limitEcsClaasCodes) {
        datas.sort(Comparator.comparingLong(ECSStock::getPriority)); //先根据优先级进行排序，优先级高的是最优机型
        String availableEcsClassCode = null;
        for (ECSStock data : datas) {
            String statusCategory = data.getStatusCategory();
            String status = data.getStatus();
            String instanceType = data.getInstanceType();
            if (limitEcsClaasCodes.contains(data.getInstanceType()) &&
                    "Available".equalsIgnoreCase(status) &&
                    ("WithStock".equalsIgnoreCase(statusCategory) || "ClosedWithStock".equalsIgnoreCase(statusCategory))) {
                logger.info("ecsClassCode [{}] is Available, statusCategory is {}", instanceType, statusCategory);
                availableEcsClassCode = instanceType;
                break;
            }
        }
        return availableEcsClassCode;
    }


    @Data
    private static class EvaluateResult {
        private boolean isAvailable;
        private int availableCount = 0;
        private int notAvailableCount = 0;
        private Map<Replica, String> targetDetail = new HashMap<>();

        public void addAvailableCount(boolean isAvailable) {
            if (isAvailable) {
                availableCount++;
            } else {
                notAvailableCount++;
            }
        }
    }

}

