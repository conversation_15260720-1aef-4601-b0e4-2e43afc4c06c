package com.aliyun.dba.base.support;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * MySQL 引擎相关的请求参数
 */
public class MySQLParamConstants {

    public static final String DISPATCH_TO_GRANDCANAL = "dispatchToGrandCanal";
    public static final String STORAGE_ENGINE = "StorageEngine";
    public static final String TIME_ZONE = "TimeZone";
    public static final String DEFAULT_TIME_ZONE = "default_time_zone";
    public static final String SYSTEM_TIME_ZONE = "SYSTEM";

    public static final String TASK_REPAIR_PARAMETER_TIME_ZONE = "repair_parameter_time_zone";

    public static final String DISASTER_RESTORE_BY_DBS_SERVICE = "disasterRestoreByDbsService";

    /**
     * 一主多从常量配置
     */
    public static final String PARAM_NAME_GENERAL_CATEGORY_GROUP_NAME = "GeneralGroupName";
    public static final String CATEGORY_GENERAL = "general";


    /**
     * 独享实例 支持V6机型cpu分配
     */
    public static final String V6_CPU_MATCH = "v6_cpu_match";
    public static final String V6_CPU_MATCH_VALUE = "1";

    /**
     * HA 切换方式, HA 会识别
     */
    public static final Integer AURORA_SWITCH_FLAG_TASK = 0;
    public static final Integer AURORA_SWITCH_FLAG_DBA_FORCE = 2;
    public static final Integer AURORA_SWITCH_FLAG_DBA_NORMAL = 3;
    public static final Integer AURORA_SWITCH_FLAG_API_NORMAL = 4;
    public static final Integer AURORA_SWITCH_FLAG_API_FORCE = 5;

    public static final String CONN_TYPE_TDDL = "tddl";
    public static final String CONN_TYPE_PHYSICAL = "physical";

    public static final String REPLICASET_BIZ_TYPE_ALGROUP = "aligroup";

    public static final String ALIGROU_DHG_PARTERN = "dhg-aligroup-%s-xdb";
    public static final String ALIGROU_TEST_DHG_PARTERN = "dhg-aligroup-%s-xdb-test";

    public static final String TDDL_TASK_MIGRATE = "tddlTaskMigrate";

    /**
     * 文件系统类型
     */
    public static final String FILE_SYSTEM_EXT4 = "ext4";
    public static final String FILE_SYSTEM_PFS = "cloud_pfs";

    /**
     * 灰度策略key
     */
    public static final String MIGRATE_PENGINE_TO_K8S_GRAY = "MIGRATE_PENGINE_TO_K8S_GRAY_MYSQL";

    /**
     * Quota 服务常量
     */
    public static final String EMPTY_HOST_LIST = "emptyhostlist";
    public static final String QUOTA_MAX_COUNT = "QuotaMaxCount";

    /**
     * mysql 参数配置
     */
    public static final String LOWER_CASE_TABLE_NAMES = "lower_case_table_names";
    public static final String INNODB_LARGE_PREFIX = "innodb_large_prefix";

    /**
     * 弹性CPU
     */
    public static final String FLEXIBLE_RESOURCE_CPU = "flexible_resource_cpu";

    /**
     * 云盘备份下载相关
     */
    public static final String RESTORE_USAGE = "RestoreUsage";
    public static final String IS_INTERNAL = "is_internal";
    public static final String DOWNLOAD_BAKSET = "DownloadBakSet";
    public static final String ACCESS_ID_DBS = "DBS";

    /**
     * CDM恢复
     */
    public static final String CDM_PREFIX = "dbs-cdm-";
    public static final String RBH_PREFIX = "rbh-";
    public static final String RESOURCE_KEY_USE_ENTERPRISE_REGISTRY = "COMMON_PROVIDER_USE_ENTERPRISE_REGISTRY";
    public static final String DEFAULT_DB_TYPE = "mysql";

    public final static String LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE = "disk_full_caused_by_big_tmp_table";

    public static final String CUR_QUOTA = "CurQuota";

    /**
     * access id
     */
    public static final String ACCESS_ID = "AccessID";
    public static final String ACCESS_ID_RESOURCE = "accessId";
    public static final String ACCESS_ID_YAOCHI = "YaoChi";

    public final static String PARAM_FORCE_REBUILD_IN_SITE = "forceRebuildInSite";

    public static final String EMERGENT_RES_ALLOC = "emergentResAlloc";
    public static String ALB_CONNECTION_DRAINING_SWITCH = "alb_connection_drain_switch";
    public static String ALB_CONNECTION_DRAINING_TIMEOUT = "alb_connection_drain_timeout";

    /**
     *  duckDB
     */
    public static final String PARAM_IS_DUCKDB = "isDuckDB";

    /**
     * 分析型只读实例相关的 14 个参数
     */
    public static final Set<String> ANALYTIC_READ_ONLY_PARAMS = Sets.newHashSet(
            "duckdb_memory_limit",
            "duckdb_threads",
            "duckdb_max_memory",
            "duckdb_temp_directory",
            "duckdb_access_mode",
            "duckdb_checkpoint_threshold",
            "duckdb_debug_checkpoint_abort",
            "duckdb_use_temporary_directory",
            "duckdb_temp_directory_path",
            "duckdb_allocator_flush_threshold",
            "duckdb_allow_unsigned_extensions",
            "duckdb_custom_extension_repository",
            "duckdb_default_collation",
            "duckdb_default_order"
    );

}