package com.aliyun.dba.base.support;

import com.alicloud.apsaradb.resmanager.response.Response;
import com.aliyun.dba.support.property.ResultCode;

public enum MysqlErrorCode {


    //库表恢复场景需要的错误码
    INVALID_PARAM_TABLE_META(ResultCode.CODE_ERROR,"InvalidParamTableMeta","Invalid parameter TableMeta is empty or not json format"),
    INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE(ResultCode.CODE_ERROR,"InvalidParamTableMeta.Duplicate","TableMeta has duplicate db or table with other newname, commons or system"),
    INVALID_PARAM_TABLE_META_TOO_MARY_TABLES(ResultCode.CODE_ERROR,"InvalidParamTableMeta.TooManyTables","TableMeta supports at most 100 tables, suggest restore the hole db."),
    MISSING_NEW_OR_OLD_DBNAME_IN_TABLEMETA(ResultCode.CODE_ERROR,"InvalidParamTableMeta.Content","TableMeta missing old dbname or new dbname, please check"),
    //原库名称太长，加上rds_xxxx_前缀可能会超过64
    INVALID_DB_NAME_TOO_LONG(ResultCode.CODE_ERROR, "InvalidDBName.TooLong", "db name wait to restore is too long"),
    INVALID_BAKHISTORY_DB_VERSION_MISMATCH(ResultCode.CODE_ERROR, "InvalidBakHistory.DbVersionMismatch", "db version of bakhistory is mismatch with custins"),
    //dbossapi中检查新库表名称是否与原实例重复，重复则报错
    INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME(ResultCode.CODE_ERROR, "InvalidParamTableMetaForRestore.Content","new dbname or table in TableMeta cannot be same with old when restore to source instance"),
    INVALID_BAKHISTORYDO_WHEN_CHECK_TABLEMETA(ResultCode.CODE_ERROR,"InvalidBakHistoryDO", "BakHistory is inbalid when check restore TableMeta"),
    INVALID_BAKTABLEMETADO_WHEN_CHECK_TABLEMETA(ResultCode.CODE_ERROR, "InvalidBakTableMetaDO", "BakTableMeta in BakHistory.Info is invalid"),
    INVALID_PARAM_TABLE_META_OLD_DBNAME_NOT_FOUND(ResultCode.CODE_ERROR, "InvalidSourceRestoreDBName.NotFound","specific source restore dbname is not found in db list"),
    //恢复空间不足
    STORAGE_FOR_RESTORE_NOT_ENOUGH(ResultCode.CODE_ERROR, "InvalidRestoreStorage.NotEnough","space is not enough for restore dbstables"),
    //无法获取剩余空间
    EVALUATE_STORAGE_FOR_RESTORE_NOT_FOUND(ResultCode.CODE_ERROR, "InvalidRestoreStorage.NotFound","current disk used cannot be available"),

    //原实例VPC网段和传入IPAddress在同一个网段
    RSCECS_CIDRBLOCK_SAME_WTTH_IPADDRESS(ResultCode.CODE_ERROR, "InvalidIPAddress.Conflict", "src ecs cidr block conflicts with parameter ipaddress, please check"),

    //备库重搭主机实例id不能是master
    INVALID_REBUILD_SLAVE_INSTANCE(ResultCode.CODE_ERROR, "InvaildRebuildSlaveInstance.Master", "current rebuild instance is master, which must be slave role"),
    //docker on ecs 变配到物理机，传递的目标集群的dbType需要与规格一致（如果指定了ClusterName参数）
    INVALID_CLUSTER_NOT_SAME_WITH_CLASS_CODE(ResultCode.CODE_ERROR, "InvalidClusterAndDBInstanceClassForTrans","Current cluster's db_type not same with TargetDBInstanceClass"),

    //重启备库，当前实例hostins数量不对
    INVALID_CUSTINS_HAS_NO_SLAVE_INSTACE(ResultCode.CODE_ERROR, "InvaildRestartSlaveIns.NotFound", "current custins has no slave instance."),

    // 新架构添加
    INVALID_TDDL_CLUSTER_NAME(ResultCode.CODE_ERROR, "InvalidTddlClusterName", "TddlClusterName can not be null for creating migrate TDDL instance."),
    INVALID_CLOUD_DISK_SIZE(ResultCode.CODE_ERROR, "InvalidCloudDiskSizeForPFS", "Cloud disk size for PFS is a multiple of 10."),
    INVALID_INSTRUCTIONSET_ARCH(ResultCode.CODE_ERROR, "InvalidInstructionSetArch", "InstructionSetArch is either ARM or X86."),
    ALREADY_EXISIT_REBUILD_TASK(ResultCode.CODE_ERROR, "InvalidReplicaSetStatus", "Rebuild node task is already exist."),

    // GDN
    GDN_QUERY_FAILED (ResultCode.CODE_SERVER, "GdnQueryFailed", "Gdn Query Failed."),
    GDN_INSTANCE_NOT_FOUND (ResultCode.CODE_NOTFOUND, "InvalidGdnInstance.NotFound", "Specified GDN instance does not exist."),
    GDN_INSTANCE_EXIST (ResultCode.CODE_NOTFOUND, "InvalidGdnInstance.InstanceExist", "Specified GDN instance already exist."),
    GDN_MEMBERS_IS_EMPTY (ResultCode.CODE_NOTFOUND, "InvalidGdnInstanceMember.Empty", "Specified GDN instance members is empty."),

    //资源调度模板不存在
    INVALID_RSESOURCE_SCHEDULE_TEMPLATE(ResultCode.CODE_ERROR, "InvalidRsScheduleTemplate", "resource schedule template is empty"),

    //原表名称太长，加上_shadow_bak之后会超过64位
    INVALID_TABLE_NAME_TOO_LONG(ResultCode.CODE_ERROR, "InvalidTableName.TooLong", "old table name wait to restore is too long"),


    //一主多从错误码
    MISSING_PARAM_GROUP_NAME(ResultCode.CODE_ERROR, "ParamGeneralGroupName.Missing", "missing param GeneralGroupName"),
    LOGIC_INS_NOT_FOUND(ResultCode.CODE_ERROR, "Group.NotFound", "group not found"),
    LOGIC_INS_DEFAULT_LEVEL_REQUIRED(ResultCode.CODE_ERROR, "LogicInsLevel.Required", "logic ins default instance level is required"),
    LOGIC_INS_CREATE_FAILED(ResultCode.CODE_ERROR, "LogicIns.CreateFailed", "group init failed"),


    GENERAL_INS_TYPE_NOT_SUPPORTED(ResultCode.CODE_ERROR, "GeneralInsType.NotSupported", "ins type not supported for general ins"),


    MASTER_INS_NOT_FOUND(ResultCode.CODE_ERROR, "MasterIns.NotFound", "master ins not found"),
    MASTER_INS_MORE_THAN_1(ResultCode.CODE_ERROR, "MasterIns.TooMany", "master ins count is more than 1"),
    FOLLOWER_INS_MORE_THAN_1(ResultCode.CODE_ERROR, "FollowerIns.TooMany", "follwer ins count is more than 1"),
    MASTER_INS_NOT_READY(ResultCode.CODE_ERROR, "MasterIns.NotReady", "master ins not ready"),
    STORAGE_EMPTY(ResultCode.CODE_ERROR, "Storage.Empty", "storage is empty"),

    GENERAL_INS_NOT_READY(ResultCode.CODE_ERROR, "GeneralIns.NotReady", "general ins not ready"),
    GENERAL_INS_BATCH_INSTALLING(ResultCode.CODE_ERROR, "GeneralIns.BatchInstalling", "general ins batch installing, please wait"),
    GENERAL_INS_ORDER_ID_REQUIRED(ResultCode.CODE_ERROR, "GeneralIns.OrderIdRequired", "need a order id when create general ins"),
    GENERAL_INS_CREATE_LOGIC_INS_FAILED(ResultCode.CODE_ERROR, "GeneralIns.CreateLogicInsFailed", "server error"),
    GENERAL_INS_BATCH_ID_CREATE_FAILED(ResultCode.CODE_ERROR, "GeneralIns.BatchIdCreateFailed", "server error"),
    GENERAL_INS_BATCH_TASK_ID_CREATE_FAILED(ResultCode.CODE_ERROR, "GeneralIns.BatchTaskIdCreateFailed", "server error"),

    INVALID_CATEGORY(ResultCode.CODE_ERROR, "InvalidCategory.Malformed","Specified parameter category is not valid."),
    INVALID_DB_INSTANCE_STORAGE_TYPE(ResultCode.CODE_ERROR, "InvalidDBInstanceStorageType.Malformed","Specified parameter DBInstanceStorageType is not valid."),

    INVALID_DB_INSTANCE_CLASS_MISMATCH_STORAGE_TYPE(ResultCode.CODE_ERROR, "InvalidDBInstanceClass.MismatchStorageType","Specified DBInstanceClass not match with DBInstanceStorageType"),
    INVALID_APPLY_RESOURCE_QUANTITY(ResultCode.CODE_ERROR, "InvalidApplyResourceQuantity","Specified parameter Quantity must be a number less than 10."),
    INVALID_APPLY_RESOURCE_EXPECTED_TIME(ResultCode.CODE_ERROR, "InvalidApplyResourceExpectedTime","Specified parameter ExpectedTime must be UTC format and later than now."),
    INVALID_APPLY_RESOURCE_QUERY_CONDITION(ResultCode.CODE_ERROR, "InvalidApplyResourceQueryCondition","Specified query condition is not valid."),
    INVALID_APPLY_RESOURCE_ID(ResultCode.CODE_ERROR, "InvalidApplyResourceId","Specified parameter ApplyResourceId is not valid."),

    // 禁用/启用经典网络地址功能需要用到的错误码
    MISSING_PARAM_LBID(ResultCode.CODE_NOTFOUND, "MissParamLbID", "The request is missing a [LbId] parameter."),
    MISSING_PARAM_TYPE(ResultCode.CODE_NOTFOUND, "MissParamOperateType", "The request is missing a [type] parameter."),
    INVALID_LBID_PARAM(ResultCode.CODE_ERROR, "InvalidLbIdParam", "Specified LbId is not matched with specified current custins, or Specified LbId is not belonging to the classic network."),
    INVALID_OPERATION_TYPE(ResultCode.CODE_ERROR, "InvalidOperationType", "Specified operation Type is not valid."),
    ;




    private int    code;
    private String summary;
    private String desc;
    MysqlErrorCode(int code, String summary, String desc) {
        this.code = code;
        this.summary = summary;
        this.desc = desc;
    }
    public int getCode() {
        return code;
    }
    public String getSummary() {
        return summary;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String customizedErrorDesc(Response resp) {
        return resp.getMessage() + "(" + resp.getDescription() + ")";
    }

    public Object[] toArray(){
        return new Object[]{this.code,this.summary,this.desc};
    }
}
