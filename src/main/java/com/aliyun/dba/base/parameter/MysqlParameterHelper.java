package com.aliyun.dba.base.parameter;

import com.alibaba.fastjson.*;
import com.alicloud.apsaradb.resmanager.CustinsResModel;
import com.alicloud.apsaradb.resmanager.NetMode;
import com.alicloud.apsaradb.resmanager.PbdResModel;
import com.alicloud.apsaradb.resmanager.RdsResModel;
import com.alicloud.apsaradb.resmanager.ResourceContainer;
import com.alicloud.apsaradb.resmanager.VipResModel;
import com.alicloud.apsaradb.resmanager.response.AllocateResRespModel;
import com.alicloud.apsaradb.resmanager.response.PbdRespModel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.bak.dataobject.BakTableMetaDO;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.dataobject.CheckBaksetDO;
import com.aliyun.dba.bak.dataobject.LogPlanDO;
import com.aliyun.dba.bak.entity.BaksetMetaInfo;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.bak.support.BakSupport;
import com.aliyun.dba.base.support.MySQLParamConstants;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.common.ResponseSupport;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.common.dataobject.RequestParamsDO;
import com.aliyun.dba.common.dataobject.ShardInfo;
import com.aliyun.dba.common.dataobject.VpcInfo;
import com.aliyun.dba.commonkindcode.support.ParameterGroupTemplateGenerator;
import com.aliyun.dba.commonkindcode.support.helper.TimezoneHelper;
import com.aliyun.dba.custins.dataobject.*;
import com.aliyun.dba.custins.entity.CustinsState;
import com.aliyun.dba.custins.pojo.ShardsInfo;
import com.aliyun.dba.custins.service.CloneEnabledCustinsService;
import com.aliyun.dba.custins.service.ClusterService;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.service.IpWhiteListService;
import com.aliyun.dba.custins.service.MycnfService;
import com.aliyun.dba.custins.service.MysqlDBCustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.ConnAddrSupport;
import com.aliyun.dba.custins.support.CustInsType;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.custins.support.CustinsValidator;
import com.aliyun.dba.custins.support.DTZSupport;
import com.aliyun.dba.dbs.dataobject.AccountsDO;
import com.aliyun.dba.dbs.support.AccountPriviledgeType;
import com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants;
import com.aliyun.dba.ecs.service.EcsService;
import com.aliyun.dba.host.dataobject.ClustersDO;
import com.aliyun.dba.host.dataobject.ClustersQuery;
import com.aliyun.dba.host.idao.HostIDao;
import com.aliyun.dba.host.service.HostService;
import com.aliyun.dba.instance.dataobject.InstanceDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.dataobject.InstanceLevelRelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.instance.support.InstanceSupport;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.resource.support.ResourceKey;
import com.aliyun.dba.resource.support.ResourceSupport;
import com.aliyun.dba.support.api.DbossApi;
import com.aliyun.dba.support.dataobject.ProxyMetaPool;
import com.aliyun.dba.support.datasource.DataSourceMap;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.CheckService;
import com.aliyun.dba.support.service.HaProxyService;
import com.aliyun.dba.support.utils.*;
import com.aliyun.dba.task.dataobject.TransListDO;
import com.aliyun.dba.user.service.UserService;
import com.aliyun.dba.user.support.UserSupport;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.sql.Time;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.zip.DataFormatException;

import static com.aliyun.dba.bak.support.BakSupport.BACKUP_SET_TYPE_DDR;
import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_FULL;
import static com.aliyun.dba.bak.support.BakSupport.BAKTYPE_INCREMENT;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_MYSQLDUMP;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_SNAPSHOT;
import static com.aliyun.dba.bak.support.BakSupport.BAKWAY_XTRABACKUP;
import static com.aliyun.dba.bak.support.BakSupport.BAK_FOR_INSTANCE;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_LASTEST;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_OSSBAK;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_SRCCUST;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_STANDBY;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_TIME;
import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_USER;
import static com.aliyun.dba.bak.support.BakSupport.TYPE_DATA;
import static com.aliyun.dba.custins.support.CustinsSupport.*;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_SYS;
import static com.aliyun.dba.dbs.support.DbsSupport.BIZ_TYPE_USER;
import static com.aliyun.dba.dockerdefault.support.DockerOnEcsConstants.DEDICATED_HOST_GOURP_TYPE;
import static com.aliyun.dba.instance.support.InstanceSupport.CATEGORY_ENTERPRISE;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_STATUS_RECOVER_NB;
import static com.aliyun.dba.instance.support.InstanceSupport.TRANS_TYPE_RECOVER;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_NAME_IS_POLARX_HATP;
import static com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper.CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES;
import static com.aliyun.dba.resource.support.ResourceSupport.*;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_BAK;
import static com.aliyun.dba.support.datasource.DataSourceMap.DATA_SOURCE_DBAAS;
import static com.aliyun.dba.support.property.ParamConstants.*;
import static com.aliyun.dba.support.property.RdsConstants.*;

@Component
public class MysqlParameterHelper {

	private static final Logger logger = Logger.getLogger(MysqlParameterHelper.class);

    protected static final String INTERNAL_SYSTEM = "internal_system";

    //缓存以下内容
    private static List<String> dbTypeList = null;

    private static Map<String, List<String>> dbVersionMap = null;

    private volatile static Map<String, String> connLastMap = null;

    private static Map<String, Map<String,String>> regionConnLastMap = null;

    private static Map<String, Integer> metaDBTimeZoneDiffMap = new ConcurrentHashMap<>();

    /**
     * old class code don't display cpu cores.
     */
    private static Set<String> classCodeSetNotDisplayCpuCores = new HashSet<String>();
    private static long classCodeSetUpdateTimestamp = 0;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected CustinsService custinsService;
    @Autowired
    protected MysqlDBCustinsService mysqlDBCustinsService;
    @Autowired
    private BakService bakService;
    @Autowired
    private InstanceService instanceService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private IpWhiteListService ipWhiteListService;
    @Autowired
    private MycnfService mycnfService;
    @Autowired
    private CheckService checkService;
    @Autowired
    protected CloneEnabledCustinsService cloneEnabledCustinsService;
    @Autowired
    protected UserSupport userSupport;
    @Autowired
    protected ResourceSupport resourceSupport;
    @Autowired
    protected DbossApi dbossApi;
    @Autowired
    protected HaProxyService haProxyService;
    @Autowired
    protected DTZSupport dtzSupport;
    @Autowired
    protected CustinsParamService custinsParamService;
    @Autowired
    protected AVZSupport avzSupport;
    @Autowired
    protected ConnAddrCustinsService connAddrCustinsService;
    @Autowired
    protected HostService hostService;
    @Autowired
    protected HostIDao hostIDao;
    @Autowired
    protected ParameterGroupTemplateGenerator parameterGroupTemplateGenerator;
    @Autowired
    private MysqlParamMinorVerionHelper mysqlParamMinorVerionHelper;


    public static final String COMPOSE_TAG_ALIYUN = "aliyun";
    public static final String COMPOSE_TAG_ALIOS = "alios";

    //历史原因:xdb只读使用的是standard实例规格,目前xdb只读过滤4系机器,需要通过excludeHostLevelNameSet来显式通知ResManager
    public static final String[] XDB_RO_EXCLUDE_HOST_LEVELS = new String[]{"V42", "H43", "V42S1"};

    private static final String CLUSTER_PARAM_VALUE_OPEN_HOST_PERMISSION = "OPEN_HOST_PERMISSION";
    private static final String custinsParamUserSyncBinlogValueKey = "user_sync_binlog_value";



    public void setParameter(String param, String value) {
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        paramsMap.put(param.toLowerCase(), value);
    }

    public Map<String, String> getParameters() {
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return paramsMap;
    }

    public String getParameterValue(String param) {
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return paramsMap.get(param.toLowerCase());
    }

    public String getParameterValue(String param, Object defaultValue) {
        param = param.toLowerCase();
        if (defaultValue != null && !(defaultValue instanceof String)) {
            defaultValue = defaultValue.toString();
        }
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return hasParameter(param) ? paramsMap.get(param) : (String)defaultValue;
    }

    //检查创建的数据库账号是否合法
    public String checkAccountName(String accountName, String dbVersion) throws RdsException {
        if (accountName == null) {
            throw new RdsException(ErrorCode.INVALID_ACCOUNTNAME);
        }
        // 检查格式
        if (accountName.length() > 32 || !Pattern.compile(REGEX_MYSQL_ACCOUNT_NAME).matcher(accountName).matches()) {
            throw new RdsException(ErrorCode.INVALID_ACCOUNTNAME);
        }

        // 检查是否是关键词
        String keywordRegex = DB_VERSION_MYSQL_57.equals(dbVersion) ? REGEX_MYSQL_57_ACCOUNT_KEYWORD : REGEX_MYSQL_80_ACCOUNT_KEYWORD;
        if (Pattern.compile(keywordRegex).matcher(accountName.toLowerCase()).matches()) {
            throw new RdsException(ErrorCode.INVALID_ACCOUNTNAME_KEYWORD);
        }
        return accountName;
    }

    public boolean hasParameter(String param) {
        param = param.toLowerCase();
        Map<String, String> paramsMap = ActionParamsProvider.ACTION_PARAMS_MAP.get();
        return paramsMap.containsKey(param) && StringUtils.isNotEmpty(paramsMap.get(param));
    }

    public Map<String, String> getAndCheckMysqlCustomParams() throws RdsException {
        Map mysqlCustomParams = new HashMap<String, String>(2);
        String defaultTimeZone = getParameterValue(ParamConstants.DB_PARAM_TIME_ZONE,"");
        String region = getParameterValue(REGION_ID, "");
        if (StringUtils.isEmpty(defaultTimeZone)) {
            defaultTimeZone = TimezoneHelper.getDefaultTimeZone(region);
        }

        if (StringUtils.isNotBlank(defaultTimeZone)) {
            if (!TimezoneHelper.validator(defaultTimeZone)) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, ErrorCode.INVALID_PARAMETERS.getDesc() + "[Invalid defaultTimeZone:" + defaultTimeZone + "]");
            }
            mysqlCustomParams.put("default_time_zone", defaultTimeZone);
        }

        String isIgnoreCase = getParameterValue(ParamConstants.DB_PARAM_IGNORE_CASE,"");
        if (StringUtils.isNotBlank(isIgnoreCase)){
            if (isIgnoreCase.equals("true")){
                mysqlCustomParams.put("lower_case_table_names", "1");
            } else {
                mysqlCustomParams.put("lower_case_table_names", "0");
            }
        }

        return mysqlCustomParams;
    }

    public DockerInsLevelParseConfig parseDockerInsExtraInfo(String extraInfo) throws RdsException {

        DockerInsLevelParseConfig config = new DockerInsLevelParseConfig();
        try {
            DockerInsLevelExtraInfo info = new Gson().fromJson(extraInfo, DockerInsLevelExtraInfo.class);
            List<DockerInsLevelExtraInfo.PortInfo> portMapper = info.getPort_mapper();

            Integer portCountPerIns = 0;
            for (DockerInsLevelExtraInfo.PortInfo portInfo : portMapper) {
                portCountPerIns += portInfo.getNum() == null ? 1 : portInfo.getNum();
            }
            config.setPortCountPerIns(portCountPerIns);

            boolean containLinkPort = false;
            for (DockerInsLevelExtraInfo.PortInfo portInfo : portMapper) {
                if (portInfo.getLabels() == null) {
                    continue;
                }
                Set<String> labelSet = new HashSet<>(portInfo.getLabels());
                if (labelSet.contains(CustinsSupport.PORT_LABEL_LINK)) {
                    containLinkPort = true;
                    break;
                }
            }

            Integer insCount = 0;
            Integer linkPortCount = 0;
            List<DockerInsLevelExtraInfo.Topology> topologies = info.getTopology();
            for (DockerInsLevelExtraInfo.Topology topology : topologies) {
                insCount += topology.getNum();
                if (containLinkPort && topology.getNeed_vip()) {
                    linkPortCount += topology.getNum();
                }
            }
            config.setInsCount(insCount);

            Integer vipCount = 0;
            Integer vportCountPerVip = 0;
            if (linkPortCount > 0) {
                String linkMap = info.getLink_map();
                switch (linkMap) {
                    case CustinsSupport.LINK_MAP_1NN:
                        vipCount = 1;
                        vportCountPerVip = linkPortCount;
                        break;
                    case CustinsSupport.LINK_MAP_11N:
                        vipCount = 1;
                        vportCountPerVip = 1;
                        break;
                    case CustinsSupport.LINK_MAP_111:
                        vipCount = linkPortCount;
                        vportCountPerVip = 1;
                        break;
                    default:
                        String message = "Unknown link map: " + linkMap;
                        logger.error(message);
                        throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
                }
            }
            config.setVipCount(vipCount);
            config.setVportCountPerVip(vportCountPerVip);

            Integer diskType = CustinsSupport.getDiskType(info.getDisk());
            if (diskType == null) {
                String message = "Unknown disk type: " + info.getDisk();
                throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
            }
            config.setDiskType(diskType);

            if (info.getDistribute_policy() != null) {
                config.setDistributePolicy(info.getDistribute_policy());
            }
            config.setServiceType(info.getType());

        } catch (RdsException e) {
            throw e;
        } catch (Exception e) {
            String message = "Parse ins Level extra info failed: " + extraInfo + "\n" + e;
            logger.error(message, e);
            throw new RdsException(ErrorCode.INVALID_INSTANCE_LEVEL_EXTRA_INFO, message);
        }
        return config;
    }

    public String getAction() {
        return getParameterValue("action");
    }

    /**
     * 生成日志需要使用UUID,此处有问题
     */
    public String getUUID() {
        return this.getParameterValue(RdsConstants.REQUEST_UUID);
    }

    public String getBID() {//万网等供应商ID，对应user表corp_name
        return getParameterValue("user_id");
    }

    public String getAndCheckBID() throws RdsException {
        String bid = getParameterValue("user_id");
        if (org.apache.commons.lang3.StringUtils.isEmpty(bid)) {
            throw new RdsException(ErrorCode.MISSING_USER_ID);
        }
        return bid;
    }

    public String getAndCheckGeneralCategoryGroupName() throws RdsException {
        String groupName = getParameterValue(MySQLParamConstants.PARAM_NAME_GENERAL_CATEGORY_GROUP_NAME);
        if (org.apache.commons.lang3.StringUtils.isEmpty(groupName)) {
            throw new RdsException(MysqlErrorCode.MISSING_PARAM_GROUP_NAME.toArray());
        }
        return groupName;
    }


    public String getAndCheckCustomsServerCert() throws RdsException {
        String serverCert = getParameterValue(ParamConstants.SERVER_CERT);
        if (StringUtils.isEmpty(serverCert)) {
            throw new RdsException(ErrorCode.PARAM_NOT_FOUND, ParamConstants.SERVER_CERT);
        }
        String prefix = "-----BEGIN CERTIFICATE-----";
        String suffix = "-----END CERTIFICATE-----";
        serverCert = StringUtils.strip(serverCert);
        if (!serverCert.startsWith(prefix) || !serverCert.endsWith(suffix)) {
            throw new RdsException(ErrorCode.INVALID_CERT_OR_KEY);
        }
        return serverCert;
    }

    public String getAndCheckCustomsServerKey() throws RdsException {
        String serverKey = getParameterValue(ParamConstants.SERVER_KEY);
        if (StringUtils.isEmpty(serverKey)) {
            throw new RdsException(ErrorCode.PARAM_NOT_FOUND, ParamConstants.SERVER_KEY);
        }
        serverKey = StringUtils.strip(serverKey);
        if (serverKey.contains("ENCRYPTED")) {
            throw new RdsException(ErrorCode.INVALID_CERT_OR_KEY);
        }
        return serverKey;
    }

    public CustInstanceDO getAndCheckCustInstanceById(String CustinsIdKey)
        throws RdsException {
        Integer userId = getAndCheckUserId();
        Integer custinsId = CheckUtils.parseInt(getParameterValue(CustinsIdKey), null, null,
            ErrorCode.DBINSTANCE_NOT_FOUND);
        CustInstanceDO custins = custinsService.getCustInstanceByCustinsIdIgnoreDelete(userId, custinsId, 0);
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public Integer getAndCheckTargetUserId() throws RdsException {
        return getAndCheckUserId("targetuserid", "targetuid",
            ErrorCode.MISSING_TARGET_USER_ID, ErrorCode.MISSING_TARGET_UID);
    }

    public Integer getAndCheckUserId() throws RdsException {
        return getAndCheckUserId("user_id", "uid", ErrorCode.MISSING_USER_ID, ErrorCode.MISSING_UID);
    }

    private Integer getAndCheckUserId(String bidArgName,
                                      String uidArgName,
                                      ErrorCode missingBid,
                                      ErrorCode missingUid) throws RdsException {

        String action = this.getAction();
        String bid = this.getParameterValue(bidArgName);
        String uid = this.getParameterValue(uidArgName);
        if (this.hasParameter(ParamConstants.INNER_USER_ID)) {
            return CustinsValidator.getRealNumber(this.getParameterValue(ParamConstants.INNER_USER_ID),
                -1);
        }
        return checkService.getAndCheckUserId(bid, uid, action);
    }

    /**
     * 检查备份集ID是否有效
     *
     * @param custins
     * @param bakId
     * @return
     * @throws RdsException
     */
    public BakhistoryDO getAndCheckBakhistory(CustInstanceDO custins, Long bakId) throws RdsException {

        BakhistoryDO history = new BakhistoryDO();

        history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);

        if (history == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        if (!history.isBakForInstance()) {//仅支持实例级别备份
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (!history.getStatus().equals("OK") || history.getIsAvail() == 0) {//加判断is avail
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETSTATUS);
        }
        if (custins.isMysql() && custins.isExcluse() && BAKWAY_MYSQLDUMP
            .equals(history.getBakWay())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (custins.isMysql()) {
            String bakWay;
            if (custins.isCustinsUseEcsSnapshot()) {
                bakWay = BAKWAY_SNAPSHOT;
            } else {
                bakWay = BAKWAY_XTRABACKUP;
            }
            if (!history.getBakWay().equals(bakWay)) {
                throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
            }
        }
        if (BAKTYPE_INCREMENT.equals(history.getBakType())) {
            Integer countBakHis = bakService.countBakHisotryBeforeBakId(custins.getId(), bakId, BAKTYPE_FULL, 1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
        }
        return history;
    }

    //真正用户的ID（各供应商生成），对应user表dept_name
    public String getUID() {

        return getParameterValue("uid");
    }

    public Integer getNetType() throws RdsException {
        String netType = getParameterValue("DBInstanceNetType");
        if (netType == null) {
            netType = "1";
        }

        Integer netTyeValue = (Integer)CustinsSupport.NET_TYPE_MAP.get(netType);
        if (netTyeValue == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENETTYPE);
        } else {
            return netTyeValue;
        }
    }

    public String getResourceStrategy() {
        return getParameterValue("ResourceStrategy");
    }

    public String getAndCheckUID() throws RdsException {
        String uid = getParameterValue("uid");
        if (org.apache.commons.lang3.StringUtils.isEmpty(uid)) {
            throw new RdsException(ErrorCode.MISSING_UID);
        }
        return uid;
    }

    public Boolean getAndCheckRetainClassic() throws RdsException {
        String retainClassic = getParameterValue(ParamConstants.RETAIN_CLASSIC);
        if (org.apache.commons.lang3.StringUtils.isEmpty(retainClassic)) {
            // 默认为切换
            return false;
        } else if (retainClassic.equals("1")) {
            return true;
        } else if (retainClassic.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public String selectOneAssociatedDomain(String parentDomain, String resKey, String associatedDbType) {
        Gson gson = new Gson();
        List<String> associatedDomainList = resourceService.getResourceRealValueList(resKey);
        for (String associatedDomain : associatedDomainList) {
            HashMap<String, Map> associatedDomainMap =
                gson.fromJson(associatedDomain, new TypeToken<HashMap<String, Object>>() {
                }.getType());
            for (String key : associatedDomainMap.keySet()) {
                if (key.equals(parentDomain) && associatedDomainMap.get(key).containsKey(associatedDbType)) {
                    ArrayList<String> domainList = gson.fromJson(associatedDomainMap.get(key).get(associatedDbType)
                            .toString(),
                        new TypeToken<ArrayList<String>>() {
                        }.getType());
                    if (domainList.size() > 0) {
                        Collections.shuffle(domainList);
                        return domainList.get(0);
                    }
                }
            }
        }
        return null;
    }

    public Boolean getAndCheckValidateOnly() throws RdsException {
        String validateOnly = getParameterValue(ParamConstants.VALIDATE_ONLY);
        if (org.apache.commons.lang3.StringUtils.isEmpty(validateOnly)) {
            // 默认为切换
            return false;
        } else if (validateOnly.equals("1")) {
            return true;
        } else if (validateOnly.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public Boolean supportVpcInLvs(CustInstanceDO custins) {

        if (!(custins.isMysql() && !custins.isCustinsOnEcs())) {
            return true;
        }

        if (custins.isMysql()) {
            return true;
        }

        try {
            // 全局开关，如果实例开关不存在，以全局开关为准
            return resourceSupport.getIntegerRealValue(ResourceKey.GLOBAL_SUPPORT_LVS_IN_VPC) == 1;
        } catch (RdsException e) {
            e.printStackTrace();
        }
        return false;
    }

    public Boolean getAndCheckSafeDelete() throws RdsException {
        String safeDelete = getParameterValue(ParamConstants.SAFE_DELETE);
        if (org.apache.commons.lang3.StringUtils.isEmpty(safeDelete)) {
            // 默认为不保留切换
            return false;
        } else if (safeDelete.equals("1")) {
            return true;
        } else if (safeDelete.equals("0")) {
            return false;
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
    }

    public String getConnectionString() {//实例连接地址

        return getParameterValue("connectionstring");
    }

    public String getDBInstanceName() {//实例名

        return getParameterValue("dbinstancename");
    }

    public String getDBInstanceID() {

        return getParameterValue("dbinstanceid");
    }

    public String getSourceDBInstanceName() {
        return getParameterValue("sourcedbinstancename");
    }

    public String getAndCheckDBInstanceName() throws RdsException {//实例名
        return CheckUtils.checkNullForInsName(getParameterValue("dbinstancename"));
    }

    public String getAndCheckisAllowDel() throws RdsException {
        String IsAllowDel = getParameterValue("IsAllowDel");
        if (Validator.isNull(IsAllowDel)) {
            throw new RdsException(ErrorCode.ISALLOWDEL_NOT_FOUND);
        }
        if (!IsAllowDel.equals("0") && !IsAllowDel.equals("1")) {
            throw new RdsException(ErrorCode.INVALID_ISALLOWDELDBDINSTANCE);
        }
        return IsAllowDel;
    }

    public String getAndCheckSourceDBInstanceName() throws RdsException {//来源实例名
        return CheckUtils.checkNullForInsName(getParameterValue("sourcedbinstancename"));
    }

    public String getAndCheckTargetDBInstanceName() throws RdsException {//目标实例名
        return CheckUtils.checkNullForInsName(getParameterValue("targetdbinstancename"));
    }

    public String getSourceDBInstanceID() throws RdsException {
        return getParameterValue("sourcedbinstanceid");
    }

    public String getAccountName() {//帐号
        return getParameterValue(ParamConstants.ACCOUNT_NAME);
    }

    public String getAcountBizType() {//帐号类型

        return getParameterValue(ParamConstants.ACCOUNT_BIZ_TYPE);
    }

    public String getAndCheckAccountName() throws RdsException {
        return CheckUtils.checkNullForAccountName(this.getAccountName());
    }

    public String getAccountPassword() {//操作帐号的密码

        return getParameterValue("accountpassword");
    }

    public String getAndCheckAccountPassword() throws RdsException {
        String accountPassword = getParameterValue("accountpassword");
        return CheckUtils.checkValidForAccountPassword(accountPassword);
    }

    public String getTargetEngineVersion() {

        return getParameterValue("TargetEngineVersion");
    }

    public String getTargetEngine() {

        return getParameterValue("TargetEngine");
    }

    public String getMinorVersion() {

        return getParameterValue("minorversion");
    }

    public String getMajorVersion() {

        return getParameterValue("majorversion");
    }

    public String getTargetMinorVersion() {

        return getParameterValue("targetminorversion");
    }

    public String getAndCheckUpgradePolicy() throws RdsException {
        String upgradePolicy = getParameterValue(ParamConstants.UPGRADE_POLICY);
        return CheckUtils.checkUpgradePolicy(upgradePolicy);
    }

    public String getAndCheckTargetCustinsCount() throws RdsException {
        String targetCustinsCount = getParameterValue(ParamConstants.TARGET_CUSTINS_COUNT);
        return targetCustinsCount == null ? "1" : targetCustinsCount;
    }

    public String getInstanceId() {

        return getParameterValue("instance_id");
    }

    public List<Integer> getInstanceIdList() throws RdsException {
        List<Integer> instanceIdList = new ArrayList<Integer>();
        String instanceIdListSrc = getParameterValue("InstanceIdList");
        if (instanceIdListSrc == null || instanceIdListSrc.equals("")) {
            return instanceIdList;
        } else {
            for (String instanceIdSrc : instanceIdListSrc.split(",")) {
                try {
                    instanceIdList.add(Integer.parseInt(instanceIdSrc.trim()));
                } catch (NumberFormatException e) {
                    logger.error("Parsing getInstanceIdList(" + instanceIdListSrc + ") got error: " + e);
                    throw new RdsException(ErrorCode.INVALID_INSTANCE_ID_LIST);
                }
            }
            return instanceIdList;
        }
    }

    public String getUpgradeMode() {

        return getParameterValue("upgrademode");
    }

    public String getRollbackMode() {

        return getParameterValue("rollbackmode");
    }

    /**
     * 获取解密后的密码
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckDecryptedAccountPassword() throws RdsException {
        return getAndCheckDecryptedAccountPassword("accountpassword", "encryptaccountpassword");
    }

    /**
     * 获取解密后的密码
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckDecryptedOldPassword() throws RdsException {
        return getAndCheckDecryptedAccountPassword("OldPassword", "EncryptOldPassword");
    }
    /**
     * 获取解密后的密码
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckDecryptedNewPassword() throws RdsException {
        return getAndCheckDecryptedAccountPassword("NewPassword", "EncryptNewPassword");
    }

    public String getAndCheckAccountBizType(CustInstanceDO custIns)
        throws RdsException {
        String accountBizType = this.getAcountBizType();
        // kepler_cstore不支持AccountBizType设置为"userbiz"
        if ((accountBizType == null
            || accountBizType.equals(BIZ_TYPE_USER))
            && !custIns.isKeplerCstore()) {
            return BIZ_TYPE_USER;
        } else if (accountBizType.equals(BIZ_TYPE_SYS) &&
            custIns.isCustinsOnDocker()) {
            return BIZ_TYPE_SYS;
        } else {
            throw new RdsException(ErrorCode.INVALID_ACCOUNT_BIZ_TYPE);
        }
    }

    public String getAndCheckDecryptedAccountPassword(String argAccountPassword,
                                                      String argEncryptAccountPassword)
        throws RdsException {
        String encryptAccountPassword = getParameterValue(argEncryptAccountPassword);
        String decryptedAccountPassword = getParameterValue(argAccountPassword);
        if (encryptAccountPassword != null) {
            String decryptKey = resourceSupport.getStringRealValue(ResourceKey.RESOURCE_PWD_TRANS_CRYPTKEY);
            decryptedAccountPassword = AES.decryptPassword(encryptAccountPassword, decryptKey);
        }

        return CheckUtils.checkValidForAccountPassword(decryptedAccountPassword);
    }

    /**
     * 获取解密后的密码，但不校验(可能为空)
     *
     * @param argAccountPassword
     * @param argEncryptAccountPassword
     * @return
     * @throws RdsException
     */
    public String getDecryptedAccountPasswordWithoutCheck(String argAccountPassword,
                                                          String argEncryptAccountPassword)
        throws RdsException {
        String encryptAccountPassword = getParameterValue(argEncryptAccountPassword);
        String decryptedAccountPassword = getParameterValue(argAccountPassword);
        if (encryptAccountPassword != null) {
            String decryptKey = resourceSupport.getStringRealValue(ResourceKey.RESOURCE_PWD_TRANS_CRYPTKEY);
            decryptedAccountPassword = AES.decryptPassword(encryptAccountPassword, decryptKey);
        }
        return decryptedAccountPassword;
    }

    /**
     * 获取 redis hmacEncryptAccountNewPassword
     *
     * @param hmacEncryptAccountNewPassword
     * @return
     * @throws RdsException
     */
    public String getHmacEncryptAccountNewPasswordForRedis(String hmacEncryptAccountNewPassword) throws RdsException {
        String encryptAccountPassword = getParameterValue(hmacEncryptAccountNewPassword);
        if (encryptAccountPassword != null && Pattern.compile("^[A-Fa-f0-9]{1,128}").matcher(encryptAccountPassword)
            .matches() && encryptAccountPassword.length() % 2 == 0) {
            return encryptAccountPassword;
        }
        throw new RdsException(ErrorCode.INVALID_ACCOUNTPASSWORD);
    }

    public String getDbInfo() {

        return getParameterValue("dbinfo");
    }

    public String getAccountPrivilege() {
        return getParameterValue("AccountPrivilege");
    }

    /**
     * 获取dbName列表，多个dbname通过逗号来分割
     *
     * @return
     */
    public List<String> getDBNames() {//数据库名称
        String dbname = this.getDBName();
        List<String> dbNameList = Collections.emptyList();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dbname)) {
            String[] dbnames = SupportUtils.splitToArray(dbname, ",");
            dbNameList = Arrays.asList(dbnames);
        }
        return dbNameList;
    }

    public String getAndCheckAccountDesc() throws RdsException {
        String comment = getParameterValue("accountdescription");
        if (StringUtils.isEmpty(comment)) {
            return "";
        }
        return CheckUtils.checkLength(SupportUtils.decode(comment), 1, 256, ErrorCode.INVALID_DESCRIPTION);
    }

    public String getDBName() {

        return getParameterValue(ParamConstants.DB_NAME);
    }

    public String getAndCheckDBName() throws RdsException {
        return CheckUtils.checkNullForDBName(this.getDBName());
    }

    public Integer getAndCheckEvaluateNum() throws RdsException {
        String evaluateNumStr = getParameterValue(ParamConstants.EVALUATE_NUM, "1");
        if (evaluateNumStr != null) {
            return Integer.parseInt(evaluateNumStr);
        }
        return null;
    }

    public Integer getAndCheckServiceType() throws RdsException {
        String serviceType = getParameterValue("servicetype", "0");
        return CheckUtils.checkServiceType(serviceType);
    }

    public String getAndCheckRegion() throws RdsException {//数据中心
        String region = getParameterValue("region");
        return CheckUtils.checkNullForRegion(region);
    }

    public Integer getAndCheckNetType() throws RdsException {
        String netType = getParameterValue("dbinstancenettype", "1");
        if (netType == null || !CustinsSupport.NET_TYPE_MAP.containsKey(netType)) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCENETTYPE);
        }
        return CustinsSupport.NET_TYPE_MAP.get(netType);
    }

    public Integer getAndCheckMaxscaleNetType() throws RdsException {
        String netType = getParameterValue("maxscalenettype", "1");
        if (netType == null || !CustinsSupport.NET_TYPE_MAP.containsKey(netType)) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALENETTYPE);
        }
        return CustinsSupport.NET_TYPE_MAP.get(netType);
    }

    public String getAndCheckMaxscaleConnectionString() throws RdsException {
        String connectionString = getParameterValue("maxscaleconnectionstring");
        if (connectionString == null) {
            return null;
        } else if (connectionString.length() > 40 || !Pattern.compile(REGEX_CONN_ADDR).matcher(connectionString)
            .matches()) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_CONNECTION_STRING);
        } else {
            return connectionString;
        }
    }

    public String getAndCheckMaxscaleInsName() throws RdsException {
        String maxscaleInsName = getParameterValue("maxscaleinsname", "");
        if (org.apache.commons.lang3.StringUtils.isBlank(maxscaleInsName)) {
            throw new RdsException(ErrorCode.INVALID_MAXSCALE_INS_NAME);
        }
        return maxscaleInsName;
    }

    public Integer getAndCheckDistType() throws RdsException {
        String distType = getParameterValue("distributiontype");
        if (distType == null) {
            return null;
        }
        if (!CustinsSupport.RW_DIST_POLICY_MAP.containsKey(distType)) {
            throw new RdsException(ErrorCode.INVALID_DISTRIBUTIONTYPE);
        }
        return CustinsSupport.RW_DIST_POLICY_MAP.get(distType);
    }

    public Integer getAndCheckMaxDelayTime() throws RdsException {

        String maxDelayTime = getParameterValue("maxdelaytime");
        if (maxDelayTime == null) {
            return null;
        }
        return CheckUtils.parseInt(maxDelayTime, 0, 7200, ErrorCode.INVALID_MAX_DELAY_TIME);
    }

    public void CheckRWWeightFormat(Map<String, Object> rwWeightMap, List<String> insNames) throws RdsException {
        for (String key : rwWeightMap.keySet()) {
            if (!insNames.contains(key.toString())) {
                throw new RdsException(ErrorCode.INVALID_WEIGHT_INSNAME);
            }
        }
        Integer weightSum = 0;
        for (Object value : rwWeightMap.values()) {
            Integer wt = CheckUtils.parseInt(value.toString(), 0, 10000, ErrorCode.INVALID_WEIGHT);
            if (wt % 100 != 0) {
                throw new RdsException(ErrorCode.INVALID_WEIGHT);
            }
            weightSum += wt;
        }
        if (weightSum == 0) {
            throw new RdsException(ErrorCode.INVALID_WEIGHT);
        }
    }

    public String getAndChangeEngine() throws RdsException {//数据库类型
        String engine = getParameterValue("engine");
        if (Validator.isNotNull(engine)) {
            engine = CustinsSupport.changeEngine(engine);
            if (Validator.isNull(engine)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
        }
        return engine;
    }

    public String validAndChangeEngine(String engine) throws RdsException {//数据库类型
        if (Validator.isNotNull(engine)) {
            engine = CustinsSupport.changeEngine(engine);
            if (Validator.isNull(engine)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
        }
        return engine;
    }

    /**
     * 获取要创建rds实例的业务类型（partition分区实例 或 普通实例）
     *
     * @return
     */
    public Integer getAndCheckBizType() {
        String bizTypeStr = getParameterValue(ParamConstants.BIZ_TYPE);
        if (ParamConstants.PARTITION_NAME.equalsIgnoreCase(bizTypeStr)) {
            return CustinsSupport.BIZ_TYPE_PARTITION;
        }
        return CustinsSupport.BIZ_TYPE_RDS;
    }

    public Integer getAndCheckBizType(String bizType) {
        if (bizType == null) {
            bizType = getParameterValue(ParamConstants.BIZ_TYPE);
        }
        if (ParamConstants.PARTITION_NAME.equalsIgnoreCase(bizType)) {
            return CustinsSupport.BIZ_TYPE_PARTITION;
        }
        return CustinsSupport.BIZ_TYPE_RDS;
    }

    public String getAndCheckDBType(String defaultType) throws RdsException {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        String dbType = this.getAndChangeEngine();
        if (dbType == null && defaultType != null) {
            dbType = defaultType;
        }
        if (dbType == null || !dbTypeList.contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        return dbType;
    }

    public String getAndCheckDockerDBType(String defaultType) throws RdsException {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        String dbType = this.validAndChangeEngine(defaultType);
        if (dbType == null && defaultType != null) {
            dbType = defaultType;
        }
        if (dbType == null || !dbTypeList.contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        return dbType;
    }

    public String getAndCheckClassCode() throws RdsException {
        String classCode = getParameterValue(ParamConstants.DB_INSTANCE_CLASS);
        if (org.apache.commons.lang3.StringUtils.isBlank(classCode)) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }
        return classCode;
    }

    public String getAndCheckDescription() throws RdsException {
        String description = getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION);
        return checkDescription(description);
    }

    public String checkDescription(String description) throws RdsException {
        String desc = SupportUtils.decode(description);
        desc = CheckUtils
            .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION);
        return desc;
    }

    private void initDbVersionMap(String dbType) {
        if (dbVersionMap == null) {
            dbVersionMap = new HashMap<>();
        }
        if (dbType != null && !dbVersionMap.containsKey(dbType)) {
            List<String> dbVersionList = resourceService.getResourceRealValueList(
                RESOURCE_DB_VERSION, RESOURCE_DB_TYPE, dbType);
            logger.warn("initDbVersionMap is invoke...dbVersionList.size=" + dbVersionList.size());
            if (!dbVersionList.isEmpty()) {
                dbVersionMap.put(dbType, dbVersionList);
            }
        }
    }

    public Long getExtendDiskSizeForEcsIns(String dbType, Long diskSize) {
        // For MysqlOnEcsDBEngineExtAdapter 5.7 ins on ecs, allocate extra disk. 1G ~ 20G
        if (DB_TYPE_MYSQL.equals(dbType) || CustinsSupport.DB_TYPE_DOCKER.equals(dbType)) {
            Long extendDiskSizeGB = Math.max(Math.round(diskSize.floatValue() / 1024 * 0.1), 1L);
            diskSize += (Math.min(extendDiskSizeGB * 1024, 20 * 1024L) + 4096L);
        }
        if (CustinsSupport.DB_TYPE_DOCKER.equals(dbType)){
            //            每个inode占用存储空间为256Bytes。根据这个计算，2024G的磁盘而外的inode 空间为：2024.0 * 10241024/16 * 256 / 1024/1024/1024  (GB) = 31.6 G空间
            //            修改rds-api创建是时磁盘空间计算公式添加而外的inode空间消耗计算：Math.ceil(disk-quota + 20 + 4) 1.016 ) 小数点最后意味向上取整
            //            0.016 约等于每MB空间inode占用的空间，需添加此部分空间，df看到的实际大小才是匹配的。
            return new Double(Math.ceil(diskSize)*1.016).longValue();
        }
        return diskSize;
    }

    public List<String> getDbTypeList() {
        if (dbTypeList == null) {
            dbTypeList = resourceService.getResourceRealValueList(RESOURCE_DB_TYPE);
        }
        return dbTypeList;
    }

    public List<String> getDbVersionList(String dbType) throws RdsException {
        if (!this.getDbTypeList().contains(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        if (dbVersionMap == null || !dbVersionMap.containsKey(dbType)) {
            initDbVersionMap(dbType);
        }
        return dbVersionMap.get(dbType);
    }

    /**
     * 慎用，取不到mysql会默认拿5.7的
     *
     * @param dbType
     * @param setDefault
     * @return
     * @throws RdsException
     */
    public String getAndCheckDBVersion(String dbType, boolean setDefault)
        throws RdsException {
        this.initDbVersionMap(dbType);
        if (!dbVersionMap.containsKey(dbType)) {
            throw new RdsException(ErrorCode.INVALID_ENGINE);
        }
        String dbVersion = getParameterValue("engineversion");
        logger.warn("CommponentsAction.getAndCheckDBVersion version=" + dbVersion);
        if (org.apache.commons.lang3.StringUtils.isEmpty(dbVersion)) {
            if (setDefault) {
                dbVersion = CustinsSupport.getDbTypeDefaultVersion(dbType);
                logger.warn("step1=CommponentsAction.getAndCheckDBVersion version=" + dbVersion);
            } else {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        }
        if (!dbVersionMap.get(dbType).contains(dbVersion)) {
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }
        return dbVersion;
    }

    public String getAndCheckCharacterType(boolean setDefault) throws RdsException {
        String characterType = getParameterValue("charactertype");
        if (org.apache.commons.lang3.StringUtils.isEmpty(characterType)) {
            if (setDefault) {
                characterType = CustinsSupport.CHARACTER_TYPE_NORMAL;
            } else {
                throw new RdsException(ErrorCode.INVALID_CHARACTER_TYPE);
            }
        }
        if (!CustinsSupport.CHARACTER_TYPE_LOGIC.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_NORMAL.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_MYSQL_DB.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_MYSQL_SPHINX.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_REDIS_PROXY.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_REDIS_CS.equals(characterType) &&
            !CustinsSupport.CHARACTER_TYPE_PHYSICAL.equals(characterType)) {
            throw new RdsException(ErrorCode.INVALID_CHARACTER_TYPE);
        }
        if (CustinsSupport.CHARACTER_TYPE_MYSQL_DB.equals(characterType)) {
            return CustinsSupport.CHARACTER_TYPE_NORMAL;
        }
        return characterType;
    }

    public String getDBVersion(String dbType) throws RdsException {
        this.initDbVersionMap(dbType);
        String dbVersion = getParameterValue("engineversion");
        if (Validator.isNotNull(dbVersion)) {
            if (!dbVersionMap.containsKey(dbType)) {
                throw new RdsException(ErrorCode.INVALID_ENGINE);
            }
            if (!dbVersionMap.get(dbType).contains(dbVersion)) {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        }
        return dbVersion;
    }

    public String getAndCheckBakType(String bakWay) throws RdsException {
        String baktype = getParameterValue("backupType");
        return CheckUtils.checkBakType(baktype, bakWay);
    }

    public String getAndCheckBakWay(String dbType, Boolean onEcs)
        throws RdsException {
        String bakway = getParameterValue("backupMethod");
        return CheckUtils.checkBakWay(dbType, onEcs, bakway);
    }

    public String getAndCheckCharacterSetName(String dbType, String dbVersion)
        throws RdsException {//字符集
        String character = getParameterValue("charactersetname");
        return this.checkCharacterSetName(dbType, dbVersion, character);
    }

    public Set<String> getAndCheckSecurityIpList() throws RdsException {
        String ipList = getParameterValue(ParamConstants.SECURITY_IP_LIST);
        if (ipList == null) {
            throw new RdsException(ErrorCode.INVALID_SECURITYIPLIST_FORMAT);
        }
        String ipType = getAndCheckSecurityIpType();
        return CheckUtils.checkIpList(ipList,
            resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_IP_WHILE_LIST_NUM), ipType);
    }

    public String getAndCheckWhitelistNetType() throws RdsException {
        // 若用户不传WhitelistNetType，则默认为mix，当控制台支持后，netType为必传，只能选择classic和vpc
        String netType = getParameterValue(ParamConstants.WHITELIST_NET_TYPE,
            CustinsIpWhiteListDO.DEFAULT_NET_TYPE);
        return CheckUtils.checkValidForIpWhiteListNetType(netType);
    }

    public String getAndCheckSecurityIpType() throws RdsException {
        // 若用户不传 SecurityIpType，则默认为 ipv4
        String ipType = getParameterValue(ParamConstants.SECURITY_IP_TYPE,
            CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
        if (ipType == null || !ParamConstants.SECURITY_IP_TYPE_SET.contains(ipType.toLowerCase())) {
            throw new RdsException(ErrorCode.INVALID_SECURITY_IP_TYPE);
        }
        return ipType.toLowerCase();
    }

    public CustinsIpWhiteListDO getAndCheckCustinsIpWhiteList(CustInstanceDO custins)
        throws RdsException {
        Set<String> ipSet = getAndCheckSecurityIpList();
        String whitelistNetType = getAndCheckWhitelistNetType();
        return new CustinsIpWhiteListDO(custins.getId(), SupportUtils.getIpWhiteListStr(ipSet),
            CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, "", whitelistNetType);
    }

    public String getWhitelistNetTypeByNetType(Integer netType) {
        String whitelistNetType;
        if (CustinsSupport.NET_TYPE_VPC.equals(netType)) {
            whitelistNetType = ParamConstants.VPC_WHITELIST_NET_TYPE;
        } else {
            whitelistNetType = ParamConstants.CLASSIC_WHITELIST_NET_TYPE;
        }
        return whitelistNetType;
    }

    public void fillCustinsListDefaultIpWhiteList(List<Map<String, Object>> custinsList) {
        for (Map<String, Object> custins : custinsList) {
            Integer custinsId = (Integer)custins.get("DBInstanceID");
            String whitelistNetType = getWhitelistNetTypeByNetType((Integer)custins.get("DBInstanceNetType"));
            List<CustinsIpWhiteListDO> custinsIpWhiteList = ipWhiteListService.getCustinsIpWhiteList(
                custinsId, CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, whitelistNetType,
                CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
            if (custinsIpWhiteList.isEmpty()) {
                custinsIpWhiteList = ipWhiteListService.getCustinsIpWhiteList(
                    custinsId, CustinsIpWhiteListDO.DEFAULT_GROUP_NAME, CustinsIpWhiteListDO.DEFAULT_NET_TYPE,
                    CustinsIpWhiteListDO.DEFAULT_IP_TYPE);
            }
            if (!custinsIpWhiteList.isEmpty()) {
                custins.put(ParamConstants.SECURITY_IP_LIST, custinsIpWhiteList.get(0).getIpWhiteList());
            }
        }
    }

    public void fillCustinsListRwType(List<Map<String, Object>> custinsList) {
        for (Map<String, Object> custins : custinsList) {
            Integer custinsId = (Integer)custins.get("DBInstanceID");
            String engine = (String)custins.get("Engine");
            Integer insRWType = 0;
            if (!CustinsSupport.DB_ENGINE_MYSQL.equals(engine) && !CustinsSupport.DB_ENGINE_REDIS.equals(engine) &&
                !CustinsSupport.DB_ENGINE_MONGODB.equals(engine)) {
                return;
            } else {
                AuroraListDO aurora = custinsService.getAuroraListByCustinsId(custinsId);
                if (aurora != null) {
                    insRWType = aurora.getIsReadonly();
                }
            }
            custins.put("ReadWriteType", insRWType);
        }
    }

    public String getStartTime() {

        return getParameterValue("starttime");
    }

    public String getEndTime() {

        return getParameterValue("endtime");
    }

    public String getFlushSysImage() {

        return getParameterValue("Image");
    }

    public String getParameterName() {

        return getParameterValue("parametername");
    }

    public long getAndCheckTimestampByParam(String dateParamName, ErrorCode errorCode)
        throws RdsException {
        String dateStr = getParameterValue(dateParamName);
        try {
            Date date = DateSupport.str2second_gmt(dateStr);
            return date.getTime() / 1000;
        } catch (Exception e) {
            throw new RdsException(errorCode);
        }
    }

    public Date getAndCheckTimeByParam(String dateParamName, DateUTCFormat format,
                                       ErrorCode errorCode) throws RdsException {
        String dateStr = getParameterValue(dateParamName);
        return getAndCheckTimeByDateStr(dateStr, format, errorCode);
    }

    public Date getAndCheckTimeByParam(String paramName, DateUTCFormat format,
                                       ErrorCode errorCode,
                                       String defaultDate) throws RdsException {
        String date = getParameterValue(paramName);
        if (date == null && defaultDate != null) {
            date = defaultDate;
        }
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, errorCode, timeZoneDiffSec);
    }

    public Date getAndCheckTimeByDateStr(String dateStr, DateUTCFormat format, ErrorCode errorCode)
        throws RdsException {
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(dateStr, format, errorCode, timeZoneDiffSec);
    }

    public Date getAndCheckStartTime(DateUTCFormat format) throws RdsException {
        String date = getParameterValue("starttime");
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_STARTTIME, timeZoneDiffSec);
    }

    public Date getAndCheckEndTime(DateUTCFormat format) throws RdsException {
        String date = getParameterValue("endtime");
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds();
        return CheckUtils.getAndCheckDateTime(date, format, ErrorCode.INVALID_ENDTIME, timeZoneDiffSec);
    }

    public Integer getAndCheckPageSize() throws RdsException {
        String maxRecordsPerPage = getParameterValue("maxrecordsperpage");
        return CheckUtils.checkMaxRecordsPerPage(maxRecordsPerPage);
    }

    public Integer getAndCheckPageNo() throws RdsException {
        String pageNumbers = getParameterValue("pagenumbers");
        return CheckUtils.checkPageNumbers(pageNumbers);
    }

    public String getAndCheckSiteName() throws RdsException {
        String siteName = this.getParameterValue(ParamConstants.SITE_NAME);
        if (org.apache.commons.lang3.StringUtils.isBlank(siteName)) {
            throw new RdsException(ErrorCode.SITENAME_NOT_FOUND);
        }
        return siteName;
    }

    public Integer getAndCheckSlaveCount() throws RdsException {
        String insCount = this.getParameterValue(ParamConstants.INSTANCE_COUNT,
            CustinsSupport.Default_Instance_Count);

        Integer instanceCount = -1;
        try {
            instanceCount = Integer.valueOf(insCount);
        } catch (Exception e) {
            throw new RdsException(ErrorCode.REBUILD_INSCOUNT_ERROR);
        }
        if (instanceCount < 1) {
            throw new RdsException(ErrorCode.REBUILD_INSCOUNT_LESS1ERROR);
        }

        return instanceCount;
    }

    /**
     * 获得静默迁移请求中的switch time mode 参数
     *
     * @param switchTimeMode
     * @return
     */
    public String getAndCheckSwitchTimeMode(String switchTimeMode) {
        String timeMode = getParameterValue(switchTimeMode);
        return timeMode;
    }

    /**
     * switchPromptlyParam,切换模式 switchTime,切换时间 isTrans, 是否是迁移任务,ha任务也会调用这个接口
     */
    public String getAndCheckSwitchTimeMode(String switchPromptlyParam,
                                            Date switchTime, boolean isTrans) throws RdsException {

        String switchPromptly = this.getParameterValue(switchPromptlyParam);

        if (org.apache.commons.lang3.StringUtils.isEmpty(switchPromptly)) {
            //没有传递switchTimeMode参数
            if (switchTime == null) {
                return CustinsSupport.NOW_MODE;
            } else {
                if (isTrans) {
                    //jian rong
                    return CustinsSupport.COMPATIBILITY_MODE;
                } else {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIMEMODE);
                }
            }
        } else {
            if (CustinsSupport.SWITCH_NOW.equals(switchPromptly)) {
                //立即切换
                return CustinsSupport.NOW_MODE;
            } else if (CustinsSupport.SWITCH_MAINTAIN.equals(switchPromptly)) {
                //可运维时间
                return CustinsSupport.MAINTAIN_MODE;
            } else if (CustinsSupport.SWITCH_POINT.equals(switchPromptly)) {
                //指定时间点
                if (switchTime == null) {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
                } else {
                    return CustinsSupport.APPOINT_MODE;
                }

            } else {
                throw new RdsException(ErrorCode.INVALID_UNKNOWMODE);
            }
        }
    }

    public String getAndCheckRestoreType() throws RdsException {
        String restoreType = this.getParameterValue(ParamConstants.RESTORE_TYPE);
        if (restoreType == null) {
            return RESTORE_TYPE_BAKID;
        }
        if (RESTORE_TYPE_TIME.equals(restoreType) || RESTORE_TYPE_BAKID.equals(restoreType) || RESTORE_TYPE_OSSBAK
            .equals(restoreType)
            || RESTORE_TYPE_USER.equals(restoreType) || RESTORE_TYPE_STANDBY.equals(restoreType) || RESTORE_TYPE_SRCCUST
            .equals(restoreType) || RESTORE_TYPE_LASTEST.equals(restoreType)) {
            return restoreType;
        }
        throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
    }

    /**
     * flush ap账号,默认 maintainTime
     */
    public String getAndCheckSwitchModeForAP(String switchPromptlyParam,
                                             Date switchTime) throws RdsException {

        String switchPromptly = getParameterValue(switchPromptlyParam);

        if (org.apache.commons.lang3.StringUtils.isEmpty(switchPromptly)) {
            //没有传递switchTimeMode参数
            return CustinsSupport.MAINTAIN_MODE;
        } else {
            if (CustinsSupport.SWITCH_NOW.equals(switchPromptly)) {
                //立即切换
                return CustinsSupport.NOW_MODE;
            } else if (CustinsSupport.SWITCH_MAINTAIN.equals(switchPromptly)) {
                //可运维时间
                return CustinsSupport.MAINTAIN_MODE;
            } else if (CustinsSupport.SWITCH_POINT.equals(switchPromptly)) {
                //指定时间点
                if (switchTime == null) {
                    throw new RdsException(ErrorCode.INVALID_SWICTHTIME);
                } else {
                    return CustinsSupport.APPOINT_MODE;
                }

            } else {
                throw new RdsException(ErrorCode.INVALID_UNKNOWMODE);
            }
        }
    }

    public String getAndCheckEntityType() throws RdsException {
        String entityType = getParameterValue(ParamConstants.ENTITY_TYPE);
        if (org.apache.commons.lang3.StringUtils.isBlank(entityType)) {
            throw new RdsException(ErrorCode.INVALID_ENTITY_TYPE);
        }

        return entityType;
    }

    public Integer getAndCheckUserLogEntityId() throws RdsException {
        String str = this.getParameterValue(ParamConstants.ENTITY_ID);
        Integer entityId = CustinsValidator.getRealNumber(str);
        return entityId;
    }

    public String getAndCheckEntityId() throws RdsException {
        String entityId = getParameterValue(ParamConstants.ENTITY_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(entityId)) {
            throw new RdsException(ErrorCode.INVALID_ENTITY_ID);
        }
        return entityId;
    }

    public String getEntityId() throws RdsException {
        String entityId = this.getParameterValue(ParamConstants.ENTITY_ID);

        return entityId;
    }

    public String getTagType() {
        String tagType = this.getParameterValue(ParamConstants.TAG_TYPE);
        return tagType;
    }

    public String getAndCheckTagType() throws RdsException {
        String tagType = this.getParameterValue(ParamConstants.TAG_TYPE);
        if (org.apache.commons.lang3.StringUtils.isBlank(tagType)) {
            throw new RdsException(ErrorCode.INVALID_TAG_TYPE);
        }

        return tagType;
    }

    public String getAndCheckTagValue() throws RdsException {
        String tagValue = this.getParameterValue(ParamConstants.TAG_VALUE);
        if (org.apache.commons.lang3.StringUtils.isBlank(tagValue)) {
            throw new RdsException(ErrorCode.INVALID_TAG_VALUE);
        }

        return tagValue;
    }

    /**
     * 获取操作者ID，杜康使用
     *
     * @return
     */
    public Integer getOperatorId() {
        if (hasParameter(ParamConstants.OPERATOR_ID)) {
            return CustinsValidator.getRealNumber(
                getParameterValue(ParamConstants.OPERATOR_ID));
        } else {
            return 999999;
        }
    }

    /**
     * 取值范围：utf8/gbk/latin1
     *
     * @param dbType
     * @param character
     * @return
     * @throws RdsException TODO 2.7.17 字符排除mysql5.1，配置成utf8mb4#5.1
     */
    public String checkCharacterSetName(String dbType, String dbVersion, String character)
        throws RdsException {
        if (Validator.isNull(character)) {
            throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
        }
        String charsetValue = resourceService.getDisplayValue(RESOURCE_DB_CHARSET, character,
            RESOURCE_DB_TYPE, dbType);
        if (charsetValue != null) {
            int idx = charsetValue.indexOf('#');
            if (idx != -1) {
                String version = charsetValue.substring(idx + 1);
                if (dbVersion.equals(version)) {
                    throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
                }
            }
            return character;
        }
        if (dbType
            .equals(CustinsSupport.DB_TYPE_MSSQL)) {//传入mssql字符集在mysql字符集列表里，则返回mssql的字符集Chinese_PRC_CI_AS
            List<String> list = resourceService.getResourceRealValueList(RESOURCE_DB_CHARSET,
                RESOURCE_DB_TYPE, DB_TYPE_MYSQL);
            if (list.contains(character)) {
                return "Chinese_PRC_CI_AS";
            }
        }
        throw new RdsException(ErrorCode.INVALID_CHARACTERSETNAME);
    }
    public String getRegionIdByClusterName(String cluster) throws RdsException{
        ClustersDO clusterByClusterName = clusterService.getClusterByClusterName(cluster);
        if(clusterByClusterName.getType() >= 1) {
            return clusterByClusterName.getRegion();
        }
        List<String> regionIds = clusterService.getRegionByClusterNames(new String[]{cluster});
        if (regionIds == null || regionIds.isEmpty()){
            throw new RdsException(ErrorCode.INVALID_PARAM, "Can't find regionId, with clusterName=" + cluster);
        }
        return regionIds.get(0);
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param dbType
     * @return
     */
    public String getConnAddrCust(String connectionString, String dbType) throws RdsException {
        return connectionString + getConnAddrCustLast(dbType);
    }

    public String getConnAddrCustLast(String dbType) throws RdsException {
        if (connLastMap == null || !connLastMap.containsKey(dbType)) {
            // 修复并发问题 https://work.aone.alibaba-inc.com/issue/39617812
            synchronized (MysqlParameterHelper.class) {
                if (connLastMap == null || !connLastMap.containsKey(dbType)) {
                    List<Map<String, String>> resMapList = resourceService.getResourceMapList(RESOURCE_CONNADDR_LAST);
                    HashMap<String, String> newConnMap = new HashMap<>();
                    for (Map<String, String> resMap : resMapList) {
                        newConnMap.put(resMap.get("RealValue"), resMap.get("DisplayValue"));
                    }
                    connLastMap = newConnMap;
                }
            }
        }
        if (!connLastMap.containsKey(dbType)) {
            throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
        }
        return connLastMap.get(dbType);
    }

    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param regionId
     * @param dbType
     * @return
     */
    public String getConnAddrCust(String connectionString, String regionId, String dbType) throws RdsException {
        return connectionString + getConnAddrCustLast(regionId, dbType);
    }
    /**
     * 获取用户连接地址后辍，失败返回null
     *
     * @param regionId
     * @param dbType
     * @return
     */
    public String getConnAddrCustLast(String regionId, String dbType) throws RdsException {
        if (regionConnLastMap == null || !regionConnLastMap.containsKey(dbType)) {
            List<Map<String, String>> resMapList = resourceService.getResourceMapList(RESOURCE_REGION_CONNADDR_LAST);
            regionConnLastMap = new HashMap<>(resMapList.size());
            for (Map<String, String> resMap : resMapList) {
                try {
                    regionConnLastMap.put(resMap.get("RealValue"), JSON.parseObject(resMap.get("DisplayValue"), new TypeReference<Map<String, String>>() {
                    }));
                } catch (Throwable e) {
                    logger.warn(
                            String.format("%s config for %s cannot be parsed: %s; value: %s",
                                    RESOURCE_REGION_CONNADDR_LAST, resMap.get("RealValue"), e, resMap.get("DisplayValue")), e);
                }
            }
        }
        if ((!regionConnLastMap.containsKey(dbType)) || regionConnLastMap.get(dbType) == null) {
            return getConnAddrCustLast(dbType);
        }
        String regionConnAddr = regionConnLastMap.get(dbType).getOrDefault(regionId, null);
        return (StringUtils.isEmpty(regionConnAddr) ? getConnAddrCustLast(dbType) : regionConnAddr);
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param dbType
     * @param dockerType
     * @return
     */
    public String getConnAddrCust4Docker(String connectionString, String dockerType, String dbType)
        throws RdsException {
        try{
            return getConnAddrCust(connectionString, dbType);
        }catch (RdsException ignored){
            return getConnAddrCust(connectionString, dockerType);
        }
    }
    /**
     * 获取用户连接地址，连接字符串+后辍，失败返回null
     *
     * @param connectionString
     * @param regionId
     * @param dbType
     * @param dockerType
     * @return
     */
    public String getConnAddrCust4Docker(String connectionString, String regionId, String dockerType, String dbType)
            throws RdsException {
        try{
            return getConnAddrCust(connectionString, regionId, dbType);
        }catch (RdsException ignored){
            return getConnAddrCust(connectionString, regionId, dockerType);
        }
    }
    /**
     * 判断用户的连接串是否是完成的地址，否则作为前缀处理
     *
     * @param connectionString
     * @param dbType
     * @return
     */
    public Boolean isValidFullConnectionAddr(String connectionString, String regionId, String dbType) throws RdsException {
        String last = getConnAddrCustLast(regionId, dbType);
        if (connectionString.endsWith(last)) {
            String preFix = org.apache.commons.lang3.StringUtils.substringBeforeLast(connectionString,
                    last);
            CheckUtils.checkValidForConnAddrCust(preFix);
            return true;
        }
        return false;
    }

    /**
     * 获取实例支持的最大DB数量
     *
     * @param dbType
     * @return
     * @throws RdsException
     */
    public Integer getCustinsMaxDbs(String dbType) throws RdsException {
        List<Map<String, String>> resMapList = resourceService
            .getResourceMapList(RESOURCE_CUSTINS_MAX_DBS);
        for (Map<String, String> resMap : resMapList) {
            if (dbType.equals(resMap.get("RealValue"))) {
                try {
                    return Integer.parseInt(resMap.get("DisplayValue"));
                } catch (NumberFormatException e) {
                    throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
                }
            }
        }
        throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
    }

    /**
     * 获取用户传入的账号信息
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public AccountsDO getAndCheckAccount(CustInstanceDO custins) throws RdsException {
        AccountsDO account = null;
        if (custins.isRedis()) {
            if (hasParameter(ParamConstants.ACCOUNT_PASSWORD)
                || hasParameter(ParamConstants.ENCRYPT_ACCOUNT_PASSWORD)) {
                account = new AccountsDO(custins, custins.getInsName(),
                    getAndCheckDecryptedAccountPassword());
            } else {
                throw new RdsException(ErrorCode.ACCOUNT_NOT_FOUND);
            }
        } else if (hasParameter(ParamConstants.ACCOUNT_NAME)) {
            String accountName = CheckUtils.checkValidForAccountName(
                getAccountName(), custins.getDbType(), custins.isTop(),
                custins.isSuperAccountMode() ?
                    AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN :
                    AccountPriviledgeType.PRIVILEDGE_NORMAL,
                custins.getDbVersion());
            account = new AccountsDO(custins, accountName,
                getAndCheckDecryptedAccountPassword());
            if (custins.isSuperAccountMode()) {
                account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_SUPER_ALIYUN.getValue());
            } else {
                account.setPriviledgeType(AccountPriviledgeType.PRIVILEDGE_NORMAL.getValue());
            }
        }
        return account;
    }

    /**
     * 获取实例支持创建的最大Account数量
     *
     * @param dbType
     * @return
     * @throws RdsException
     */
    public Integer getCustinsMaxAccounts(String dbType) throws RdsException {
        List<Map<String, String>> resMapList = resourceService
            .getResourceMapList(RESOURCE_CUSTINS_MAX_ACCOUNTS);
        for (Map<String, String> resMap : resMapList) {
            if (dbType.equals(resMap.get("RealValue"))) {
                try {
                    return Integer.parseInt(resMap.get("DisplayValue"));
                } catch (NumberFormatException e) {
                    throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
                }
            }
        }
        throw new RdsException(ErrorCode.RESOURCE_CONFIG_ERROR);
    }

    /**
     * 获取创建实例时指定的磁盘类型
     *
     * @return
     */
    public String getAndCheckHostType() throws RdsException {
        String hostType = getParameterValue(ParamConstants.CUSTINS_HOST_TYPE);
        return CheckUtils.checkHostType(hostType);
    }

    /**
     * 获取创建Docker化实例的磁盘类型
     *
     * @return
     */
    public String getAndCheckDockerHostType(String dbType, String dbVersion)
        throws RdsException {
        String hostType = getParameterValue(ParamConstants.CUSTINS_HOST_TYPE);
        if (CustinsSupport.isDockeronEcs(dbVersion, dbType)) {
            hostType = CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD;
        }

        if (hostType == null || CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE.contains(hostType)) {
            return hostType;
        }
        throw new RdsException(ErrorCode.INVALID_HOST_TYPE);
    }

    public void checkRestoreTimeValid(CustInstanceDO custins, Date restoreTime, LogPlanDO logPlan)
        throws RdsException {
        long times = restoreTime.getTime();
        logger.warn("times is: " + times + ",System.currentTimeMillis() is: " + System.currentTimeMillis());
        if (times >= System.currentTimeMillis()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        logger.warn(
            "times is: " + times + ",custins.getGmtCreated().getTime() is: " + custins.getGmtCreated().getTime());
        if (times <= custins.getGmtCreated().getTime()) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }

        // 不允许恢复到备份保留周期之前的时间点
        Integer retention = bakService.getBaklistByCustinsId(custins.getId()).getRetention();
        if (logPlan != null) {
            Integer logRetention = logPlan.getRetention();
            if (retention.equals(-1)) {
                retention = logRetention;
            } else {
                retention = Math.min(retention, logRetention);
            }
        }
        Date expireTime = DateUtils.addDays(DateSupport.str2date(DateSupport.date2str(new Date())),
            -retention);
        logger.warn("times is: " + times + ",expireTime.getTime() is: " + expireTime.getTime());
        if (times < expireTime.getTime()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }

        if (logPlan != null) {
            // TODO: mongo公测期间不收费,后续收费时需去除特殊处理
            if ((!custins.isMongoDB() && !logPlan.isEnableBackupLog()) || logPlan.getEnableUploadTime().after(
                restoreTime)) {
                throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
            }
            // make sure there is a valid backup set between enable upload time and restore time.
            Integer countBakHis = bakService.countBakHisotry(custins.getId(),
                logPlan.getEnableUploadTime(), restoreTime, BAKTYPE_FULL, 1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
            }
        }

        this.checkBakupSetAvailForRestoreTime(custins, restoreTime);
    }

    public void checkBakupSetAvailForRestoreTime(CustInstanceDO custins, Date restoreTime)
        throws RdsException {
        String bakWay;
        if (custins.isSqlserver()) {
            bakWay = BAKWAY_XTRABACKUP;
        } else if (custins.isMongoDB()) {
            // MongoDB过渡期间同时支持逻辑备份和物理备份
            bakWay = null;
        } else if (custins.isCustinsDockerOnPolarStore()) {
            bakWay = null;
        } else {
            if (custins.isCustinsOnEcs() || custins.isCustinsDockerOnEcs()) {
                bakWay = BAKWAY_SNAPSHOT;
            } else {
                bakWay = BAKWAY_XTRABACKUP;
            }
        }
        Date fullBakTimeBefore = bakService.getLatestBakTimeBefore(custins.getId(), restoreTime, BAKTYPE_FULL, bakWay);
        if (fullBakTimeBefore == null) {
            throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
        }
        Boolean checkOK = bakService.checkBinlogNotExpired(custins.getId(), fullBakTimeBefore, restoreTime);
        // 备份集和还原时间点期间的日志不能有被清理的
        if (!checkOK) {
            throw new RdsException(ErrorCode.RECOVERTIME_BINLOG_NOT_FOUND);
        }

        if (custins.isPolarDB()) {
            checkOK = bakService.checkBinlogSameSize(custins.getId(), fullBakTimeBefore, restoreTime);
            if (!checkOK) {
                throw new RdsException(ErrorCode.RECOVERTIME_BINLOG_NOT_VALID);
            }
        }

        if (custins.isSqlserver()) {

            Integer count = bakService.countBakHisotry(custins.getId(), fullBakTimeBefore,
                restoreTime, BAKTYPE_INCREMENT, null);

            if (count > 0) {
                Integer availCount = bakService.countBakHisotry(custins.getId(), fullBakTimeBefore, restoreTime,
                    BAKTYPE_INCREMENT, 1);
                if (!count.equals(availCount)) {
                    throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
                }
            }

            Date fullBakTimeAfter = bakService.getLatestBakTimeAfter(custins.getId(), restoreTime, BAKTYPE_FULL,
                bakWay);
            if (fullBakTimeAfter == null) {
                fullBakTimeAfter = new Date();
            }
            count = instanceService.countTransListByTypeCondition(null, custins.getId(),
                fullBakTimeBefore, fullBakTimeAfter, TRANS_TYPE_RECOVER);
            if (count > 0) {
                throw new RdsException(ErrorCode.RECOVERTIME_BACKUP_NOT_FOUND);
            }
        }
    }

    /**
     * 获取创建实例时指定的几点类型：single|double
     *
     * @return
     */
    public String getAndCheckNodeType() throws RdsException {
        String nodeType = getParameterValue(ParamConstants.CUSTINS_NODE_TYPE);
        return CheckUtils.checkNodeType(nodeType);
    }

    /**
     * 获取创建实例时指定的region_id(ex:cn-hangzhou)
     *
     * @return
     */
    public String getAndCheckRegionID() throws RdsException {//地域
        String regionID = getParameterValue(ParamConstants.REGION_ID);
        return CheckUtils.checkNullForRegion(regionID);
    }

    public Integer getAndCheckDBInstanceUsedType() throws RdsException {
        String insTypeStr = getParameterValue(ParamConstants.DB_INSTANCE_USED_TYPE);
        Integer insType = CUSTINS_INSTYPE_PRIMARY; //默认值
        if (insTypeStr != null) {
            insType = SupportUtils.strToint(insTypeStr);
        }
        return insType;
    }

    /**
     * 获取创建实例时指定的av_zone(ex:cn-hangzhou-a)
     *
     * @return
     */
    public String getAndCheckAvZone() throws RdsException {//可用区
        String avZone = getParameterValue(ParamConstants.ZONE_ID);
        return CheckUtils.checkNullForAvZone(avZone);
    }

    /**
     * 获取校验参数DBInstanceNodeCount参数，仅主实例MySQL5.6有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckDBInstanceNodeCount(CustInstanceDO custins)
        throws RdsException {

        String nodeCountStr = getParameterValue(ParamConstants.DB_INSTANCE_NODE_COUNT);
        if (nodeCountStr == null) {
            return null;
        }
        Integer nodeCount = CustinsValidator.getRealNumber(nodeCountStr, -1);
        if (custins.isPrimary() && custins.isMysql56()) {
            if (nodeCount < 2) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        } else if (custins.isRedisNormal()) {
            if (nodeCount > 3 || nodeCount < 1) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        } else if (custins.isKeplerRc()) {
            if (nodeCount != 1) {
                throw new RdsException(ErrorCode.INVALID_NODE_COUNT);
            }
            return nodeCount;
        }
        return nodeCount;
    }

    /**
     * 获取校验DBInstanceGroupCount参数，仅对GP实例有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckDBInstanceGroupCount(CustInstanceDO custins)
        throws RdsException {
        if (custins.isGpdb() || custins.isHawq() || custins.isHBase()) {
            String groupCountStr = getParameterValue(ParamConstants.DB_INSTANCE_GROUP_COUNT);
            Integer groupCount = CustinsValidator.getRealNumber(groupCountStr, -1);
            if (groupCount < 2) {
                throw new RdsException(ErrorCode.INVALID_GROUP_COUNT);
            }
            return groupCount;
        }
        return null;
    }

    /**
     * @return
     * @throws RdsException
     */
    public Integer getTasksFilter() throws RdsException {
        //默认过滤，只查询从API下发的任务
        String tasksFilterStr = getParameterValue(ParamConstants.TASKS_FILTER, "1");
        return Integer.valueOf(tasksFilterStr);
    }

    /**
     * 获取校验参数DBInstanceSyncMode参数，仅主实例MySQL5.6有效
     *
     * @param custins
     * @return
     * @throws RdsException
     */
    public String getAndCheckDBInstanceSyncMode(CustInstanceDO custins)
        throws RdsException {

        String syncModeStr = getParameterValue(ParamConstants.DB_INSTANCE_SYNC_MODE);
        if (syncModeStr == null) {
            if (mysqlDBCustinsService.isMysqlEnterprise(custins)) {
                return CustinsParamSupport.CUSTINS_PARAM_VALUE_SYNC_MODE_NO_REVERT_SEMI_SYNC;
            }
            return null;
        }
        if (custins.isPrimary() && custins.isMysql56()) {
            if (!CustinsParamSupport.CUSTINS_PARAM_VALUE_SET_SYNC_MODE.contains(syncModeStr)) {
                throw new RdsException(ErrorCode.INVALID_SYNC_MODE);
            }
            return syncModeStr;
        }
        return null;
    }

    /**
     * 获取当前元数据库与 UTC 时间的差值, 单位为秒
     *
     * @return FIXME:后续需要将识别不同调用者所属的元数据库，获取对应时间
     */
    private Integer getMetaDBTimeZoneDiffSeconds() {
        /*String dataSource = DataSourceHolder.getCurrentDataSource();
        dataSource = dataSource != null? dataSource: "default_datasource";
        Integer timeZoneDiffSec = metaDBTimeZoneDiffMap.get(dataSource);
        if (timeZoneDiffSec == null) {
            timeZoneDiffSec = resourceService.getSecondsDiffBetweenLocalAndGMT();
            metaDBTimeZoneDiffMap.put(dataSource, timeZoneDiffSec);
        }
        return timeZoneDiffSec;*/
        return 28800;
    }

    private String makeLog(String action, String result) {
        return "response message, #message[" +
            "action:" +
            action +
            ", result:" +
            result +
            "]";
    }

    public String getAndCheckConnType(String paramName) throws RdsException {
        if (paramName == null) {
            paramName = ParamConstants.DB_INSTANCE_CONN_TYPE;
        }
        String connType = getParameterValue(paramName);
        return CheckUtils.checkConnType(connType);
    }

    /**
     * 判断是否为备份验证服务产生的克隆实例
     *
     * @return
     */
    public boolean isBvsInstance() {
        String accessId = getParameterValue(ParamConstants.ACCESSID);
        String targetUid = getParameterValue(ParamConstants.TARGET_UID);
        if ("BVS".equals(accessId) && "apsaradb_bvs".equals(targetUid)) {
            return true;
        }
        return false;
    }

    /**
     * 获取主机实例角色
     */
    public Integer getAndCheckInstanceRole() throws RdsException {
        String instanceRoleStr = getParameterValue(ParamConstants.INSTANCE_ROLE);
        Integer instanceRole = ParamConstants.INSTANCE_ROLE_MASTER; //默认值
        if (instanceRoleStr != null) {
            instanceRole = SupportUtils.strToint(instanceRoleStr);
        }
        return instanceRole;
    }

    public String getAndCheckDownloadUrl() throws RdsException {
        String downloadUrl = Base64Decoder.decode(getParameterValue(ParamConstants.DOWNLOAD_URL));
        //FIXME 增加下载地址有效性的校验
        if (Validator.isNull(downloadUrl)) {
            throw new RdsException(ErrorCode.INVALID_DOWNLOAD_URL);
        }
        return downloadUrl;
    }

    private static boolean isBase64(String str) {
        String base64Pattern = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Pattern, str);
    }

    public String getAndCheckCommand() throws RdsException {
        String parameterValue = getParameterValue(ParamConstants.COMMAND);
        parameterValue = parameterValue.replaceAll(" ", "+");
        logger.info("parameterValue:" + parameterValue);
        if (!isBase64(parameterValue)) {
            throw new RdsException(ErrorCode.INVALID_BASE64);
        }
        String command = Base64Decoder.decode(parameterValue);
        if (Validator.isNull(command)) {
            throw new RdsException(ErrorCode.INVALID_BASE64);
        }
        return command;
    }

    public CheckBaksetDO getAndCheckCheckBakset() throws RdsException {
        String baksetName = this.getParameterValue(ParamConstants.BAKSET_NAME);
        String checksum = this.getParameterValue(ParamConstants.CHECKSUM);
        if (Validator.isNull(baksetName) || Validator.isNull(checksum)) {
            throw new RdsException(ErrorCode.INVALID_BAKSET_NAME);
        }

        CheckBaksetDO checkBakset = bakService.getCheckBaksetByBaksetName(baksetName);
        if (checkBakset == null) {
            throw new RdsException(ErrorCode.INVALID_BAKSET_NAME);
        }
        // 校验Checksum
        if (!checksum.equals(checkBakset.getChecksum())) {
            throw new RdsException(ErrorCode.INVALID_BAKSET);
        }
        return checkBakset;
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public List<String> getAndCheckInstanceGroupId() throws RdsException {
        String groupIds = getParameterValue(ParamConstants.INSTANCE_GROUP_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(groupIds)) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_GROUP_ID);
        }
        return Arrays.asList(groupIds.split(","));
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public String getAndCheckResourceGroupId() throws RdsException {
        String groupId = getParameterValue(ParamConstants.RESOURCE_GROUP_ID, "").trim();
        if (groupId != null && groupId.length() > 256) {
            throw new RdsException(ErrorCode.INVALID_INSTANCE_GROUP_ID);
        }
        return groupId;
    }

    /**
     * 获取InstanceGroupId参数
     *
     * @return
     * @throws RdsException
     */
    public List<String> getInstanceGroupId() throws RdsException {
        String groupIds = getParameterValue(ParamConstants.INSTANCE_GROUP_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(groupIds)) {
            return new ArrayList<String>(0);
        }
        return Arrays.asList(groupIds.split(","));
    }

    /**
     * @return null if no specified parameter found
     * @throws RdsException if specified parameter not valid
     */
    public List<Integer> getAndCheckHostStatus() throws RdsException {
        String str = getParameterValue(ParamConstants.HOST_STATUS);
        if (str == null) {
            return null;
        }

        List<Integer> list = new ArrayList<Integer>();
        String[] hostStatusList = SupportUtils.splitToArray(str, ",");
        for (String hostStatus : hostStatusList) {
            Integer status = CustinsValidator.getRealNumber(hostStatus);
            if (status < 0) {
                throw new RdsException(ErrorCode.INVALID_HOST_STATUS);
            } else {
                list.add(status);
            }
        }
        return list;
    }

    /**
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckHostId() throws RdsException {
        String str = getParameterValue(ParamConstants.HOST_ID);
        Integer hostId = CustinsValidator.getRealNumber(str);
        if (hostId < 0) {
            throw new RdsException(ErrorCode.INVALID_HOST_ID);
        }
        return hostId;
    }

    public Integer getAndCheckHostIsAvail() throws RdsException {
        String str = getParameterValue(ParamConstants.ISAVAIL);
        Integer isAvail = null;
        try {
            isAvail = Integer.parseInt(str);
            if (isAvail != 0 && isAvail != 1) {
                throw new RdsException(ErrorCode.INVALID_HOST_ISAVAIL);
            }
        } catch (NumberFormatException e) {
            throw new RdsException(ErrorCode.INVALID_HOST_ISAVAIL);
        }
        return isAvail;
    }

    public Set<Integer> getAndCheckHostIdSet() throws RdsException {
        Set<Integer> hostIdSet = new HashSet<Integer>();
        String hostIdStr = getParameterValue(ParamConstants.HOST_ID);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(hostIdStr)) {
            String[] hostIds = hostIdStr.split(",");
            for (String hostId : hostIds) {
                hostIdSet.add(CustinsValidator.getRealNumber(hostId.trim()));
            }
        }
        return hostIdSet;
    }

    public String getAndCheckTransType() throws RdsException {
        String specifyTransType = getParameterValue(ParamConstants.DB_INSTANCE_TRANS_TYPE,
            CustinsSupport.CUSTINS_TRANS_TYPE_DEFAULT);
        if (!CustinsSupport.CUSTINS_TRANS_TYPE_SET.contains(specifyTransType)) {
            throw new RdsException(ErrorCode.INVALID_TRANS_TYPE);
        }
        return specifyTransType;
    }

    public String getAndCheckRebuildType() throws RdsException {
        String rebuildType = getParameterValue(ParamConstants.SLAVE_REBUILD_TYPE,
            CustinsSupport.SLAVE_REBUILD_TYPE_REMOTE);
        if (!CustinsSupport.SLAVE_REBUILD_TYPE_SET.contains(rebuildType)) {
            throw new RdsException(ErrorCode.INVALID_SLAVE_REBUILD_TYPE);
        }

        return rebuildType;
    }

    /**
     * @param classCode
     * @return
     */
    public boolean displayCpuCores(String classCode) {
        if (System.currentTimeMillis() - classCodeSetUpdateTimestamp > 10 * 60 * 1000L) {
            classCodeSetUpdateTimestamp = System.currentTimeMillis();
            List<ResourceDO> resourceList = resourceService.getResourceListByResourceKey(
                ResourceKey.RESOURCE_CLASSCODE_NOT_DISPLAY_CPU_CORES);
            Set<String> newClassCodeSet = new HashSet<String>();
            for (ResourceDO resource : resourceList) {
                newClassCodeSet.add(resource.getRealValue());
            }
            classCodeSetNotDisplayCpuCores = newClassCodeSet;
        }
        return !classCodeSetNotDisplayCpuCores.contains(classCode);
    }

    /**
     * 校验实例切换时间
     *
     * @return
     * @throws RdsException
     */
    public Date getAndCheckSwitchTime() throws RdsException {
        String switchTimeStr = getParameterValue(ParamConstants.SWITCH_TIME);
        if (switchTimeStr == null) {
            return null;
        }

        // validate switch time
        DateTime now = new DateTime(DateTimeZone.UTC);
        Integer expire = resourceSupport.getIntegerRealValue(ResourceKey.RESOURCE_SWITCH_TIME_ALLOWED);
        try {
            Date switchTimeUTC = DateSupport.str2second_gmt(switchTimeStr);
            if (switchTimeUTC.getTime() <= now.getMillis()
                || switchTimeUTC.getTime() > now.plusHours(expire).getMillis()) {
                throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
            }
        } catch (ParseException e) {
            throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
        }

        return getAndCheckTimeByParam(ParamConstants.SWITCH_TIME,
            DateUTCFormat.SECOND_UTC_FORMAT,
            ErrorCode.INVALID_SWITCH_TIME);
    }

    /**
     * 校验实例切换时间
     *
     * @return
     * @throws RdsException
     */
    public Date getAndCheckSwitchTime(String dataSource) throws RdsException {
        String switchTimeStr = getParameterValue(ParamConstants.SWITCH_TIME);
        if (Strings.isNullOrEmpty(switchTimeStr)) {
            return null;
        }

        // validate switch time
        DateTime now = new DateTime(DateTimeZone.UTC);
        Integer expire = ResourceSupport.getInstance()
            .getIntegerRealValue(ResourceKey.RESOURCE_SWITCH_TIME_ALLOWED);
        try {
            Date switchTimeUTC = DateSupport.str2second_gmt(switchTimeStr);
            if (switchTimeUTC.getTime() <= now.getMillis()
                || switchTimeUTC.getTime() > now.plusHours(expire).getMillis()) {
                throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
            }
        } catch (ParseException e) {
            throw new RdsException(ErrorCode.INVALID_SWITCH_TIME);
        }

        return getAndCheckTimeByParam(ParamConstants.SWITCH_TIME,
            DateUTCFormat.SECOND_UTC_FORMAT,
            ErrorCode.INVALID_SWITCH_TIME, dataSource);
    }


    /**
     * 判断是否强制flush 返回 1 :强制 返回 0 : 不强制
     *
     * @return
     * @throws RdsException
     */
    public Integer getAndCheckIsForce() throws RdsException {
        String isforce = "";
        if (hasParameter(ParamConstants.IS_FORCE)) {
            isforce = getParameterValue(ParamConstants.IS_FORCE);
        }
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("1", isforce)) {
            return 1;
        }
        return 0;
    }

    /**
     * 获取Proxy Host ID
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckProxyHostId() throws RdsException {
        String str = getParameterValue(ParamConstants.PROXY_HOST_ID);
        Integer proxyHostId = CustinsValidator.getRealNumber(str);
        if (proxyHostId <= 0) {
            throw new RdsException(ErrorCode.INVALID_PROXY_HOST_ID);
        }
        return proxyHostId;
    }

    /**
     * 获取Proxy组名
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyGroupName() throws RdsException {
        String proxyGroupName = getParameterValue(ParamConstants.PROXY_GROUP_NAME);
        if (proxyGroupName == null || proxyGroupName == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_GROUP_NAME);
        }
        return proxyGroupName;
    }

    /**
     * 获取等待连接主动关闭的等待时长
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckWaitSessionCloseTimeout() throws RdsException {
        String str = getParameterValue(ParamConstants.PROXY_WAIT_SESSION_CLOSE_TIMEOUT);
        Integer waitSessionCloseTimeout = CustinsValidator.getRealNumber(str);
        if (waitSessionCloseTimeout < 0 || waitSessionCloseTimeout > 365) {
            throw new RdsException(ErrorCode.INVALID_WAIT_SESSION_CLOSE_TIMEOUT);
        }
        return waitSessionCloseTimeout;
    }

    /**
     * 获取可强制关闭的连接数
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckMaxSessionNum() throws RdsException {
        String str = getParameterValue(ParamConstants.PROXY_MAX_SESSION_NUM);
        Integer maxSessionNum = CustinsValidator.getRealNumber(str);
        if (maxSessionNum < 0 || maxSessionNum > 10000) {
            throw new RdsException(ErrorCode.INVALID_MAX_SESSION_NUM);
        }
        return maxSessionNum;
    }

    /**
     * 获取Proxy Delay Stop 的最大等待时长
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckDelayStopTimeout() throws RdsException {
        String str = getParameterValue(ParamConstants.DELAY_STOP_TIMEOUT);
        Integer delayStopTimeout = CustinsValidator.getRealNumber(str);
        if (delayStopTimeout < 0 || delayStopTimeout > 3600) {
            throw new RdsException(ErrorCode.INVALID_DELAY_STOP_TIMEOUT);
        }
        return delayStopTimeout;
    }

    /**
     * 获取强制下线Proxy节点开关状态
     *
     * @return not null
     * @throws RdsException
     */
    public boolean getAndCheckForceOffline() throws RdsException {
        String proxyForceOffline = getParameterValue(ParamConstants.PROXY_FORCE_OFFLINE);
        if (org.apache.commons.lang3.StringUtils.isBlank(proxyForceOffline)) {
            return false;
        }

        if (proxyForceOffline.toLowerCase().equals("false")) {
            return false;
        } else if (proxyForceOffline.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.INVALID_PROXY_FORCE_OFFLINE);
        }
    }

    /**
     * 获取切换的新分组
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckNewProxyGroupName() throws RdsException {
        String newProxyGroupName = getParameterValue(ParamConstants.NEW_PROXY_GROUP_NAME);
        if (org.apache.commons.lang3.StringUtils.isBlank(newProxyGroupName)) {
            throw new RdsException(ErrorCode.INVALID_NEW_PROXY_GROUP_NAME);
        }
        return newProxyGroupName;
    }

    /**
     * 获取Proxy集群名
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyClusterName() throws RdsException {
        String proxyClusterName = getParameterValue(ParamConstants.PROXY_CLUSTER_NAME);
        if (proxyClusterName == null || proxyClusterName == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_CLUSTER_NAME);
        }
        return proxyClusterName;
    }

    /**
     * 获取Site列表
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckSiteListString() throws RdsException {
        String siteListString = getParameterValue(ParamConstants.SITE_LIST);
        if (siteListString == null || siteListString == "") {
            throw new RdsException(ErrorCode.INVALID_SITE_LIST);
        }
        return siteListString;
    }

    /**
     * 获取Proxy Role
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckProxyRole() throws RdsException {
        String proxyRole = getParameterValue(ParamConstants.PROXY_ROLE);
        if (proxyRole == null || proxyRole == "") {
            throw new RdsException(ErrorCode.INVALID_PROXY_ROLE);
        }
        return proxyRole;
    }

    /**
     * 获取Proxy Node RPM Version
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckRPMVersion() throws RdsException {
        String rpmVersion = getParameterValue(ParamConstants.RPM_VERSION);
        if (rpmVersion == null || rpmVersion == "") {
            throw new RdsException(ErrorCode.INVALID_RPM_VERSION);
        }
        return rpmVersion;
    }

    /**
     * 获取扩容节点数
     *
     * @return not null
     * @throws RdsException
     */
    public Integer getAndCheckAddNodeNumber() throws RdsException {
        String str = getParameterValue(ParamConstants.ADD_NODE_NUMBER);
        Integer addNodeNumber = CustinsValidator.getRealNumber(str);
        if (addNodeNumber <= 0) {
            throw new RdsException(ErrorCode.INVALID_ADD_NODE_NUMBER);
        }
        return addNodeNumber;
    }

    /**
     * 获取Creator
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckCreator() throws RdsException {
        String creator = getParameterValue(ParamConstants.CREATOR);
        if (creator == null || creator == "") {
            throw new RdsException(ErrorCode.INVALID_CREATOR);
        }
        return creator;
    }

    /**
     * 获取IP列表
     *
     * @return not null
     * @throws RdsException
     */
    public String getAndCheckIPListString() throws RdsException {
        String ipListString = getParameterValue(ParamConstants.IP_LIST);
        if (ipListString == null || ipListString == "") {
            throw new RdsException(ErrorCode.INVALID_IP_LIST);
        }
        return ipListString;
    }

    /**
     * 获取&解析外部参数
     *
     * @return
     * @throws RdsException
     */
    public Map<String, List<MycnfCustinstanceDO>> getAndCheckExternalParameter(CustInstanceDO custins)
        throws RdsException {
        String custInsDbType = this.getAndCheckDBType(null);
        String custInsDbVersion = this.getAndCheckDBVersion(custInsDbType, true);
        String externalParamStr = getParameterValue(ParamConstants.EXTERNAL_PARAMETER);
        Map<String, List<MycnfCustinstanceDO>> externalParam = new HashMap<>();

        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custins.getLevelId());

        String composeTag = this.selectComposeTag(custins.getClusterName());

        EngineCompose engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(custInsDbType,
            custInsDbVersion, instanceLevelDO.getCategory(), composeTag);
        JSONObject jsonServices = JSON.parseObject(engineCompose.getServices());

        if (org.apache.commons.lang3.StringUtils.isNotBlank(externalParamStr)) {
            try {
                JSONObject externalParamJSON = JSON.parseObject(externalParamStr);
                for (String dbType : externalParamJSON.keySet()) {
                    // 根据外部传参的dbType和engine_compose中该dbType对应的dbVersion查询MycnfTemplate表对应的参数
                    EngineService engineService = new Gson().fromJson(
                        jsonServices.getString(dbType), EngineService.class);
                    if (engineService == null) {
                        ErrorCode errorCode = ErrorCode.INVALID_EXTERNAL_PARAMETER_DBTYPE.resetDesc();
                        errorCode.setDesc(String.format(errorCode.getDesc(), dbType));
                        throw new RdsException(errorCode);
                    }

                    List<MycnfTemplateDO> mycnfTemplateDOList = mycnfService
                        .getMycnfTemplateDOListByDbTypeAndDbVersion(dbType, engineService.getVersion());
                    List<String> mycnfTemplateParameterNames = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(mycnfTemplateDOList)) {
                        for (MycnfTemplateDO mycnfTemplateDO : mycnfTemplateDOList) {
                            mycnfTemplateParameterNames.add(mycnfTemplateDO.getName());
                        }
                    }

                    JSONObject params = externalParamJSON.getJSONObject(dbType);
                    List<MycnfCustinstanceDO> engineParams = new ArrayList<>();
                    for (String paramName : params.keySet()) {
                        if (mycnfTemplateParameterNames.contains(paramName)) {
                            ErrorCode errorCode = ErrorCode.INVALID_EXTERNAL_PARAMETER_NAME.resetDesc();
                            errorCode.setDesc(String.format(errorCode.getDesc(), paramName, dbType));
                            throw new RdsException(errorCode);
                        }
                        MycnfCustinstanceDO cnf = new MycnfCustinstanceDO();
                        cnf.setName(paramName);
                        cnf.setParaValue(params.getString(paramName));
                        engineParams.add(cnf);
                    }
                    externalParam.put(dbType, engineParams);
                }
            } catch (RdsException e) {
                logger.error("Invalid external parameter json format. ", e);
                throw new RdsException(e.getErrorCode());
            } catch (Exception e) {
                logger.error("Invalid external parameter json format. ", e);
                throw new RdsException(ErrorCode.INVALID_EXTERNAL_PARAMETER);
            }
        }
        return externalParam;
    }

    public String getAndCheckProxyApiVersion(String clusterName) throws RdsException {

        String proxyApiVersion = getParameterValue("proxyapiversion");
        if (org.apache.commons.lang3.StringUtils.isEmpty(proxyApiVersion)) {
            proxyApiVersion = clusterService.getDefaultAppVersionOfCluster(clusterName, "proxyapi");
            if (proxyApiVersion == null) {
                logger.error("Can not get default proxyapi version of cluster " + clusterName);
                throw new RdsException(ErrorCode.INVALID_PROXY_API_VERSION);
            }
        } else {
            ClusterAppVersionsDO clusterAppVersionsDO = clusterService.getAppVersionOfCluster(clusterName, "proxyapi",
                proxyApiVersion);
            if (clusterAppVersionsDO == null) {
                throw new RdsException(ErrorCode.INVALID_PROXY_API_VERSION);
            }
        }
        return proxyApiVersion;
    }

    public String getAndCheckProxyNodeVersion(String clusterName)
        throws RdsException {

        String proxyNodeVersion = getParameterValue("proxynodeversion");
        if (org.apache.commons.lang3.StringUtils.isEmpty(proxyNodeVersion)) {
            proxyNodeVersion = clusterService.getDefaultAppVersionOfCluster(clusterName, "proxynode");
            if (proxyNodeVersion == null) {
                logger.error("Can not get default proxynode version of cluster " + clusterName);
                throw new RdsException(ErrorCode.INVALID_PROXY_NODE_VERSION);
            }
        } else {
            ClusterAppVersionsDO clusterAppVersionsDO = clusterService.getAppVersionOfCluster(clusterName, "proxynode",
                proxyNodeVersion);
            if (clusterAppVersionsDO == null) {
                throw new RdsException(ErrorCode.INVALID_PROXY_NODE_VERSION);
            }
        }
        return proxyNodeVersion;
    }

    public String getAndCheckClusterName() throws RdsException {//Cluster Name
        String clusterName = getParameterValue(ParamConstants.CLUSTER_NAME);
        if (clusterName == null || clusterName.equals("")) {
            throw new RdsException(ErrorCode.INVALID_PROXY_CLUSTER_NAME);
        } else {
            return clusterName;
        }
    }

    public Boolean getAndCheckEnablePartition() throws RdsException {//Enable Partition
        String enablePartition = getParameterValue(ParamConstants.ENABLE_PARTITION);
        if (org.apache.commons.lang3.StringUtils.isBlank(enablePartition)) {
            return false;
        }
        if (enablePartition.toLowerCase().equals("false")) {
            return false;
        } else if (enablePartition.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.INVALID_ENABLE_PARTITION);
        }
    }

    public Boolean getAndCheckOnlyGenerateData() throws RdsException {
        String onlyGenerateData = getParameterValue("OnlyGenerateData");
        if (org.apache.commons.lang3.StringUtils.isBlank(onlyGenerateData)) {
            return false;
        }
        if (onlyGenerateData.toLowerCase().equals("false")) {
            return false;
        } else if (onlyGenerateData.toLowerCase().equals("true")) {
            return true;
        } else {
            throw new RdsException(ErrorCode.HAPROXY_CREATING_ERROR, "OnlyGenerateData only can be 'true' of 'false'.");
        }
    }

    /**
     * add by fengyi (jianming.wjm) to split oss urls split mulit-oss url to list
     */
    public List<String> getOssUrls(String ossUrls) {
        List<String> ossUrlList = Collections.emptyList();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ossUrls)) {
            String[] urls = ossUrls.split("\\|", -1);
            ossUrlList = Arrays.asList(urls);
        }
        return ossUrlList;
    }

    public String getFileNameOnOss(String ossUrl) {
        String rt = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(ossUrl)) {
            int index = ossUrl.lastIndexOf("/");
            String temp = ossUrl.substring(index + 1);

            rt = temp.split("\\?")[0];

        }
        return rt;
    }

    public String getFileExtension(String fileName) {
        String rt = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(fileName)) {
            int index = fileName.lastIndexOf(".");
            rt = fileName.substring(index + 1);
        }
        return rt;
    }

    public boolean checkFileExtensionValidity(String[] acceptExtArray, String targetValue) {
        for (String ext : acceptExtArray) {
            if (ext.equalsIgnoreCase(targetValue)) {
                return true;
            }
        }
        return false;
    }

    /*
     * we don't acccept users' data name is in list:
     * [master, tempdb, msdb, model, distribution]
     *
     * we don't accept users' database name is sys_info for top biz(聚石塔)
     * top biz for mssql system name is sys_info by default
     *
     * if users' db name is in this list, return false
     * **/
    public boolean checkDBNameValidity(String[] exceptDBArray, String targetDb) {
        for (String ext : exceptDBArray) {
            if (ext.equalsIgnoreCase(targetDb)) {
                return false;
            }
        }
        return true;
    }

    public String getAndCheckEcsSecurityGroupId(boolean mustExist)
        throws RdsException {
        String securityGroupId = getParameterValue(ParamConstants.ECS_SECURITY_GROUP_ID);
        if (securityGroupId == null) {
            if (mustExist) {
                throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
            }
            return null;
        }

        if (securityGroupId.length() == 0) {
            return "";
        }

        String[] securityGroupIdList = securityGroupId.split(",");
        if (securityGroupIdList.length != 1) {
            throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
        }

        for (String one : securityGroupIdList) {
            if (one.length() > 50) {
                throw new RdsException(ErrorCode.INVALID_ECS_SECURITY_GROUP_ID);
            }
        }
        return securityGroupId;
    }

    public Map<String, Object> genPbdTaskStat(List<AllocateResRespModel.CustinsResRespModel> custinsResRespModelList) {
        Map<String, Object> pbdTaskStat = new HashMap<String, Object>();
        for (AllocateResRespModel.CustinsResRespModel custModel : custinsResRespModelList) {
            if (custModel.getPbdRespModelList() != null) {
                List<Map<String, Object>> pbd_status = new ArrayList<Map<String, Object>>();
                for (PbdRespModel pbdRespModel : custModel.getPbdRespModelList()) {
                    Map<String, Object> pbd_stat = new HashMap<String, Object>(2);
                    pbd_stat.put("pbd_name", pbdRespModel.getPbdName());
                    pbd_stat.put("task_id", pbdRespModel.getTaskId());
                    pbd_status.add(pbd_stat);
                }
                pbdTaskStat.put(String.valueOf(custModel.getCustinsId()), pbd_status);
            }

        }

        return pbdTaskStat;
    }

    public List<PbdResModel> genPbdModelList(EngineService engineService, CustInstanceDO logicCustins,
                                             Long storageNum) {
        List<PbdResModel> pbdResModelList = new ArrayList<PbdResModel>();

        if (engineService.getStorage().getstorageDataPathInfo().isNeedapply()
            && engineService.getStorage().getstorageDataPathInfo().getType().equals("pbd")) {
            String pbd_name = "pbd_" + "data_dir_" + String.valueOf(logicCustins.getId()) + "_" +
                engineService.getStorage().getstorageDataPathInfo().getLabel();
            String Label = engineService.getStorage().getstorageDataPathInfo().getLabel();
            PbdResModel pbdResModel = new PbdResModel(pbd_name);
            pbdResModel.setNickName(pbd_name);
            pbdResModel.setPbdSize(Integer.valueOf(storageNum.intValue() / 1024 + 10));
            pbdResModel.setLabel(Label);
            pbdResModelList.add(pbdResModel);
        }
        if (engineService.getStorage().getstorageLogPathInfo().isNeedapply()
            && engineService.getStorage().getstorageLogPathInfo().getType().equals("pbd")) {
            String pbd_name = "pbd_" + "log_dir_" + String.valueOf(logicCustins.getId()) + "_" +
                engineService.getStorage().getstorageLogPathInfo().getLabel();
            String Label = engineService.getStorage().getstorageLogPathInfo().getLabel();
            PbdResModel pbdResModel = new PbdResModel(pbd_name);
            pbdResModel.setNickName(pbd_name);
            pbdResModel.setPbdSize(Integer.valueOf(storageNum.intValue() / 1024 + 10));
            pbdResModel.setLabel(Label);
            pbdResModelList.add(pbdResModel);
        }

        return pbdResModelList;
    }

    /**
     * 获取规格对应的container_type和host_type,并设到parameter中
     *
     * @param dbType
     * @param dbVersion
     * @param classCode
     * @throws RdsException
     */
    public void getAndSetContainerTypeAndHostTypeIfEmpty(String dbType,
                                                         String dbVersion,
                                                         String classCode) throws RdsException {
        // TODO: force use host_type from ins level?
        if (StringUtils.isNotBlank(classCode) &&
            (!hasParameter(ParamConstants.CONTAINER_TYPE)
                || !hasParameter(ParamConstants.CUSTINS_HOST_TYPE))) {
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
                dbType, dbVersion, null, null);
            if (insLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            // do not has container_type and host_type params, try get from instance_level
            if (!hasParameter(ParamConstants.CONTAINER_TYPE)) {
                getAndSetContainerTypeFromInsLevel(insLevel);
            }
            if (!hasParameter(ParamConstants.CUSTINS_HOST_TYPE)) {
                getAndSetHostTypeFromInsLevel(insLevel);
            }
        }
    }

    /**
     * 获取规格对应的contaner_type,并设到parameter中
     *
     * @param insLevel
     * @throws RdsException
     */
    public void getAndSetContainerTypeFromInsLevel(InstanceLevelDO insLevel)
        throws RdsException {
        if (StringUtils.isNotBlank(insLevel.getExtraInfo())) {
            DockerInsLevelParseConfig config = custinsService
                .parseDockerInsExtraInfo(insLevel.getExtraInfo());
            String containerType = config.getContainerType();
            if (StringUtils.isNotEmpty(containerType)) {
                setParameter(ParamConstants.CONTAINER_TYPE, containerType);
            }
        }
    }

    /**
     * 获取规格对应的host_type,并设到parameter中
     *
     * @param insLevel
     * @throws RdsException
     */
    public void getAndSetHostTypeFromInsLevel(InstanceLevelDO insLevel)
        throws RdsException {
        Integer hostType = insLevel.getHostType();
        setParameter(ParamConstants.CUSTINS_HOST_TYPE, hostType.toString());
    }

    /**
     * 获取实例规格信息
     *
     * @param custins
     * @param classCode
     * @return
     * @throws RdsException
     */
    public CustInstanceDO setInstanceLevel(CustInstanceDO custins, String classCode,
                                           Integer bizType,
                                           String diskSize) throws RdsException {
        InstanceLevelDO insLevel = instanceService.getInstanceLevelByClassCode(classCode,
            custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
        if (insLevel == null) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        } else {
            custins.setLevelId(insLevel.getId());
            custins.setDiskSize(insLevel.getDiskSize());
        }
        if (Validator.isNotNull(diskSize) && custins.isExcluse()) {
            setDiskSize(custins, bizType, diskSize);
        }
        custins.setCharacterType(insLevel.getCharacterType());
        return custins;
    }

    public void setDiskSize(CustInstanceDO custins, Integer bizType, String diskSize) throws RdsException {
        ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE;
        if (CustinsSupport.BIZ_TYPE_PARTITION.equals(bizType)) {
            resourceKey = ResourceKey.RESOURCE_PARTITION_MAX_DISK_SIZE;
        }
        Integer maxDiskSize = resourceSupport.getIntegerRealValue(resourceKey);
        custins.setDiskSize(
            CheckUtils.parseInt(diskSize, 5, maxDiskSize, ErrorCode.INVALID_STORAGE)
                * 1024L);
    }

    /**
     * CreateDBInstance, CreateDistributeDBInstance
     *
     * @return
     * @throws RdsException
     */
    public Integer getAndCreateUserId() throws RdsException {
        if (hasParameter(ParamConstants.INNER_USER_ID)) {
            return CustinsValidator.getRealNumber(getParameterValue(ParamConstants.INNER_USER_ID), -1);
        }
        String bid = getParameterValue("user_id");
        if (StringUtils.isEmpty(bid)) {
            throw new RdsException(ErrorCode.MISSING_USER_ID);
        }
        String uid = getParameterValue("uid");
        if (StringUtils.isEmpty(uid)) {
            throw new RdsException(ErrorCode.MISSING_UID);
        }

        for (int i = 0; i < 3; i++) {
            try {
                Integer userId = null;

                try {
                    userId = userSupport.getUserIdByLoginId(bid + "_" + uid);
                } catch (RdsException e) {
                    logger.warn("LoginID: " + bid + "_" + uid + " doesn't exist, now create it.");
                }

                if (userId == null) {
                    if (!userService.hasBid(bid)) {
                        userService.createBid(bid);
                    }
                    userId = userService.createUser(bid, uid);
                }
                return userId;
            } catch (Exception e) {
                logger.error("Query/Insert user failed, try again." + bid + "_" + uid, e);
                try {
                    Thread.sleep(new Random().nextInt(100) + 100);
                } catch (InterruptedException ignore) {

                }
            }
        }
        throw new RdsException(ErrorCode.INVALID_UID);
    }

    public CustInstanceDO getAndCheckCustInstance() throws RdsException {
        return this.getAndCheckCustInstance(0);
    }

    public CustInstanceDO getAndCheckCustInstance(Integer isTmp) throws RdsException {
        Integer userId = this.getAndCheckUserId();
        CustInstanceDO custins;
        if (INTERNAL_SYSTEM.equals(this.getBID())) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckDBInstanceName(), isTmp);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckDBInstanceName(), isTmp);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public CustInstanceDO getWithoutCheckCustInstance() throws RdsException {
        CustInstanceDO custins = custinsService.getCustInstanceByInsName(null, this.getAndCheckDBInstanceName(), 0);
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public boolean isReadSwitch(CustInstanceDO custins) {
        return (custins.isRead() || custins.isReadBackup()) && CUSTINS_STATUS_SWITCH.equals(custins.getStatus());
    }

    public boolean cloneValidSrcCustins(CustInstanceDO custins) throws RdsException {

        if (custins.isShare() || custins.isReadOrBackup() || custins.isSub() || !custins.isLogicPrimary()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE);
        }

        // 去除判断源实例是否在clone中，允许并发clone
        if (!custins.isActive()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS);
        }

        if (custins.isReadAndWriteLock()) {
            throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE);
        }

        return true;
    }

    public CustInstanceDO getCustInstance() throws RdsException {
        Integer userId = this.getAndCheckUserId();
        this.checkUserOperatorCluster(userId);
        return custinsService.getCustInstanceByInsName(userId,
            this.getAndCheckDBInstanceName());
    }

    public CustInstanceDO getAndCheckSourceCustInstance() throws RdsException {
        Integer userId = this.getAndCheckUserId();
        this.checkUserOperatorCluster(userId);
        CustInstanceDO custins = null;
        if (INTERNAL_SYSTEM.equals(this.getBID())) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckSourceDBInstanceName(), 0);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckSourceDBInstanceName(), 0);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public void checkUserOperatorCluster(Integer userId) throws RdsException {
        String clusterName = this.getParameterValue(CLUSTER_NAME);
        if (StringUtils.isEmpty(clusterName)) {
            return;
        }
        ClustersDO clustersDO = clusterService.getClusterByClusterName(clusterName);
        if (clustersDO.getType() >= DEDICATED_HOST_GOURP_TYPE && !clustersDO.getUserId().equals(userId)) {
            throw new RdsException(ErrorCode.CLUSTER_NOT_FOUND);
        }
    }

    public CustInstanceDO getAndCheckTargetCustInstance() throws RdsException {
        Integer userId = this.getAndCheckUserId();
        CustInstanceDO custins = null;
        if (INTERNAL_SYSTEM.equals(this.getBID())) {
            custins = custinsService.getCustInstanceByInsName(null,
                this.getAndCheckTargetDBInstanceName(), 0);
        } else {
            custins = custinsService.getCustInstanceByInsName(userId,
                this.getAndCheckTargetDBInstanceName(), 0);
        }
        if (custins == null) {
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        return custins;
    }

    public boolean cloneValidCommon(CustInstanceDO srcCustins) throws RdsException {
        return cloneValidCommon(srcCustins, false);
    }

    public boolean cloneValidCommon(CustInstanceDO srcCustins, Boolean checkClone)
        throws RdsException {
        // 判断克隆实例个数是否达到限制
        Map<String, Object> condition = new HashMap<String, Object>(2);
        condition.put("custinsId", srcCustins.getId());
        List<Integer> status = new ArrayList<Integer>();
        status.add(0);
        condition.put("status", status);
        List<Integer> cloneInsList = cloneEnabledCustinsService.getCloneCustInstanceByCondition(condition);
        if (cloneInsList.size() >= resourceSupport.getIntegerRealValue(
            ResourceKey.RESOURCE_CREATING_CLONE_CUSTINS_COUNT)) {
            throw new RdsException(ErrorCode.CREATING_CLONE_INS_EXCEEDED);
        }

        if (!checkClone || hasParameter(ParamConstants.DB_INSTANCE_NAME)) {
            String cloneInsName = CheckUtils.checkValidForInsName(getDBInstanceName());
            if (custinsService.hasCustInstanceByInsName(cloneInsName)) {
                throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
            }
        }

        if (!checkClone || hasParameter(ParamConstants.CONNECTION_STRING)) {
            CheckUtils.checkValidForConnAddrCust(getParameterValue(ParamConstants.CONNECTION_STRING));
        }

        return true;
    }

    /**
     * 设置实例公共属性
     *
     * @param custins
     * @throws RdsException
     */
    public void updateCustinsCommonProperties(CustInstanceDO custins)
        throws RdsException {
        // 设置实例名
        custins.setInsName(CheckUtils.checkValidForInsName(getDBInstanceName()));
        if (custinsService.hasCustInstanceByInsName(custins.getInsName())) {
            throw new RdsException(ErrorCode.DBINSTANCENAME_ALREADYEXISTS);
        }

        // 设置ProxyGroupId
        if (hasParameter(ParamConstants.PROXY_GROUP_ID)) {
            String proxyGroupId = getParameterValue(ParamConstants.PROXY_GROUP_ID);
            Integer perferedProxyGroupId = Integer.valueOf(proxyGroupId);
            custins.setProxyGroupId(perferedProxyGroupId);
        }

        // 设置实例描述
        if (hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            String desc = SupportUtils
                .decode(getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION));
            custins.setComment(CheckUtils
                .checkLength(desc, 1, 256, ErrorCode.INVALID_DBINSTANCEDESCRIPTION));
        }

        // 设置实例过期时间
        if (hasParameter(ParamConstants.EXPIRED_TIME)) {
            Date expiredTime = getAndCheckTimeByParam(ParamConstants.EXPIRED_TIME,
                DateUTCFormat.MINUTE_UTC_FORMAT, ErrorCode.INVALID_EXPIREDTIME);
            if (expiredTime.getTime() <= System.currentTimeMillis()) {
                throw new RdsException(ErrorCode.INVALID_EXPIREDTIME);
            } else {
                custins.setGmtExpired(expiredTime);
            }
        }

        // 设置实例可维护时间
        Date maintainStartTime = getAndCheckTimeByParam(ParamConstants.MAINTAIN_STARTTIME,
            DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
            ErrorCode.INVALID_STARTTIME,
            CustinsSupport.DEFAULT_MAINTAIN_START_TIME);
        Date maintainEndTime = getAndCheckTimeByParam(ParamConstants.MAINTAIN_ENDTIME,
            DateUTCFormat.MINUTE_ONLY_UTC_FORMAT,
            ErrorCode.INVALID_ENDTIME, CustinsSupport.DEFAULT_MAINTAIN_END_TIME);
        custins.setMaintainStarttime(Time.valueOf(DateSupport.timeWithSecond2str(maintainStartTime)));
        custins.setMaintainEndtime(Time.valueOf(DateSupport.timeWithSecond2str(maintainEndTime)));

        // 设置是否接受优化建议服务
        String optmization = getParameterValue(ParamConstants.OPTMIZATION_SERVICE, "0");
        custins.setIsAccept(
            Integer.valueOf(CheckUtils.checkBooleanInt(optmization, ErrorCode.INVALID_OPTMIZATIONSERVICE)));
    }

    public void checkBaksetMetaInfo(BaksetMetaInfo baksetMetaInfo) throws RdsException {
        String gtidPurgedKey = "bak_gtid_purged";
        // 判空
        if (baksetMetaInfo.getBaksetName() == null || baksetMetaInfo.getBaksetSize() == null ||
            baksetMetaInfo.getDbType() == null || baksetMetaInfo.getDbVersion() == null ||
            baksetMetaInfo.getChecksum() == null || baksetMetaInfo.getRemoteUrl() == null ||
            baksetMetaInfo.getExtraInfo() == null || baksetMetaInfo.getExtraInfo().get(gtidPurgedKey) == null) {
            logger.error(baksetMetaInfo.toString());
            throw new RdsException(ErrorCode.NO_AVAILABLE_DISASTER_RESTORE_BAKSET);
        }
        String dbType = getAndCheckDBType(null);
        String dbVersion = getAndCheckDBVersion(dbType, true);
        //仅支持实例级别的物理备份
        boolean invalidBakScale = baksetMetaInfo.getBakScale() != null && !BAK_FOR_INSTANCE.equals(
            baksetMetaInfo.getBakScale());
        boolean invalidBakWay = baksetMetaInfo.getBakWay() != null && !BAKWAY_XTRABACKUP.equals(
            baksetMetaInfo.getBakWay());
        if (invalidBakScale || invalidBakWay) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        //仅支持type为DATA的全量备份
        boolean invalidBakType = baksetMetaInfo.getBakType() != null && !BAKTYPE_FULL.equals(
            baksetMetaInfo.getBakType());
        boolean invalidType = baksetMetaInfo.getType() != null && !TYPE_DATA.equals(baksetMetaInfo.getType());
        if (invalidBakType || invalidType) {
            throw new RdsException(ErrorCode.INVALID_BAK_TYPE);
        }
        if (baksetMetaInfo.getDbVersion().compareTo(DB_VERSION_MYSQL_56) >= 0) {
            if (baksetMetaInfo.getDbVersion().equals(DB_VERSION_MYSQL_56)) {
                if (!dbVersion.equals(DB_VERSION_MYSQL_55) && !dbVersion.equals(DB_VERSION_MYSQL_56)) {
                    throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
                }
            } else if (!baksetMetaInfo.getDbVersion().equals(dbVersion)) {
                throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
            }
        } else {
            throw new RdsException(ErrorCode.UNSUPPORTED_BAKSET_VERSION);
        }
        // tde备份只支持恢复到源region
        String tdeEnabledKey = "tde_enabled";
        String destRegion = getAndCheckRegionID();
        if (baksetMetaInfo.getExtraInfo().get(tdeEnabledKey) != null && (boolean)baksetMetaInfo.getExtraInfo().get(
            tdeEnabledKey)
            && !destRegion.equals(baksetMetaInfo.getSrcRegion())) {
            throw new RdsException(ErrorCode.CROSS_REGION_UNSUPPORT_TDE);
        }

        // 容灾恢复只支持恢复到源和目的region
        String backupSetType = getParameterValue("BackupSetType", BACKUP_SET_TYPE_DDR);
        if (BACKUP_SET_TYPE_DDR.equals(backupSetType) && !destRegion.equals(baksetMetaInfo.getSrcRegion())
            && !destRegion.equals(baksetMetaInfo.getDdrRegion())) {
            throw new RdsException(ErrorCode.DISASTER_RESTORE_REGION_NOT_MATCHED);
        }
    }

    /**
     * 判断是否是库表恢复
     */
    public boolean getAndCheckRestoreTable() {

        return hasParameter("RestoreTable");
    }

    /**
     * TableMeta参数校验 1.任何场景下，都有可能有整库恢复（tables=[])，此时需要从baktablemeta中获取原表名称作为新表名称 2.整库恢复场景下，无论库是否重命名，都需要保证dbname,
     * newdbname属性存在，且新库表db.table之间不重复 3.任何场景下，新库名称一定存在；新库名称之间不能重复；新库表名称db.table之间不能重复（库不重复，表就不会重复）；
     * 4.restore场景下，新库名称与旧有库名称（包括oldDBName）不能重复；新库表名称与旧有库表名称不能重复（库不重复，表就不会重复）
     */
    public String getAndCheckTableMeta(BakhistoryDO bakhistoryDO, String type)
        throws RdsException {

        //系统保留表
        Set<String> systemDBNames = new HashSet<>();
        systemDBNames.add("performance_schema");
        systemDBNames.add("mysql");
        systemDBNames.add("information_schema");
        if ("5.7".compareTo(bakhistoryDO.getDbVersion()) < 0) {
            systemDBNames.add("sys");
        }

        String tableMeta = getParameterValue("TableMeta");
        if (StringUtils.isEmpty(tableMeta)) {
            //抛出TableMeta参数缺失异常
            throw new RdsException(ErrorCode.INVALID_PARAM_TABLE_META);
        }

        //旧的库表信息，新库名称与原来所有旧库名称不相同
        Map<String, Set<String>> oldDBTablesMap = getDBTablesFromBakMetaInfo(bakhistoryDO);
        Set<String> oldDBNamesSet = oldDBTablesMap.keySet();

        //新库表db.table名称之间不能重复
        Set<String> newDBTables = new HashSet<>();
        Integer newDBTablesNum = 0;

        //记录当前新库是整库恢复还是库表恢复
        Map<String, Boolean> newDBNamesMap = new HashMap<>();

        try {
            JSONArray jsonArray = JSONArray.parseArray(tableMeta);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String oldDBName = dbInfo.getString("name");
                String newDBName = dbInfo.getString("newname");
                //新，旧库名称不能为null
                if (StringUtils.isBlank(newDBName) || StringUtils.isBlank(oldDBName)) {
                    logger.error("old db name and new db name cannot be null");
                    throw new RdsException(MysqlErrorCode.MISSING_NEW_OR_OLD_DBNAME_IN_TABLEMETA.toArray());
                }
                if (systemDBNames.contains(newDBName)) {
                    logger.error("new db name cannot be same with system db");
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                }
                //旧库名称不存在
                if (oldDBNamesSet != null && !oldDBNamesSet.contains(oldDBName)) {
                    logger.error("old db name is not invild");
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_OLD_DBNAME_NOT_FOUND.toArray());
                }
                //tables不存在，或tables=[]，则为整库恢复
                JSONArray tables = dbInfo.getJSONArray("tables");
                boolean isRestoreDB = (tables == null || tables.size() == 0);

                //restore场景下整库恢复，新库名称不能和任何原库相同（增加dbossApi检查）
                if (isRestoreDB && "restore".equals(type) && oldDBNamesSet.contains(newDBName)) {
                    throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLEMETA_DUPLICATE_DBNAME.toArray());
                }

                //新库列表中已包含整库恢复的库名称
                if (newDBNamesMap.containsKey(newDBName)) {
                    //db1.* => db_new.* db2.table => db_new.table场景，restore和clone都不允许出现
                    if (isRestoreDB || newDBNamesMap.get(newDBName) == true) {
                        logger.error("newDBName for restore total db has already exists");
                        throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                    }
                } else {
                    newDBNamesMap.put(newDBName, isRestoreDB);
                }

                //整库恢复，需要从baktablemeta中获取原库所有表信息作为新库表名称
                Set<String> oldDBTables = oldDBTablesMap.get(oldDBName);
                if (isRestoreDB) {
                    newDBTables.addAll(oldDBTables);
                    newDBTablesNum += oldDBTables.size();
                }
                //非整库恢复
                else {
                    for (int j = 0; j < tables.size(); j++) {
                        JSONObject tableInfo = (JSONObject)tables.get(j);
                        String oldTableName = tableInfo.getString("name");
                        String newTableName = tableInfo.getString("newname");
                        String oldDBTableName = oldDBName + "." + oldTableName;
                        String newDBTableName = newDBName + "." + newTableName;
                        //新库表名称和旧库表名称不能冲突
                        if ("restore".equals(type) && oldDBTables.contains(newDBTableName)) {
                            throw new RdsException(
                                MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                        }
                        //clone场景下，旧库表名称中包含新库表名称，只允许和原来的db.table相同，不允许和其他相同
                        if ("clone".equals(type) && oldDBTables.contains(newDBTableName)) {
                            if (!oldDBTableName.equalsIgnoreCase(newDBTableName)) {
                                throw new RdsException(
                                    MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
                            }
                        }
                        newDBTables.add(newDBTableName);
                        newDBTablesNum++;
                    }
                }
            }
            //新库表名称之间都不允许重复
            if (newDBTables.size() != newDBTablesNum) {
                throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META_DUPLICATE_DB_TABLE.toArray());
            }
        } catch (RdsException e) {
            logger.error("check parameter TableMeta exception : ", e);
            throw e;
        } catch (Exception e) {
            logger.error("parse parameter TableMeta Error : ", e);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        return tableMeta;
    }

    /**
     * 从备份元信息common-tables中获取<db,Set<db.table>>信息，供判断重复使用
     */
    public Map<String, Set<String>> getDBTablesFromBakMetaInfo(BakhistoryDO bakhistoryDO) throws RdsException {

        if (bakhistoryDO == null) {
            throw new RdsException(MysqlErrorCode.INVALID_BAKHISTORYDO_WHEN_CHECK_TABLEMETA.toArray());
        }

        Long bakHisId = bakhistoryDO.getHisId();
        Map<String, Object> condition = new HashMap<>();
        condition.put("backupSetId", bakHisId);
        BakTableMetaDO bakTableMetaDO = bakService.getTableMeta(condition);

        if (bakTableMetaDO == null) {
            throw new RdsException(MysqlErrorCode.INVALID_BAKTABLEMETADO_WHEN_CHECK_TABLEMETA.toArray());
        }

        String metaCompress = bakTableMetaDO.getInfo();
        byte[] metaCompressData = BakSupport.hexStrToByteArray(metaCompress);

        Map<String, Set<String>> dbTablesMap = new HashMap<>();

        try {
            byte[] output = BakSupport.zlibDecompress(metaCompressData);
            String info = new String(output);
            JSONArray jsonArray = JSONArray.parseArray(info);
            if (bakhistoryDO.getDbVersion().compareTo("5.7") <= 0) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                    String dbName = dbInfo.getString("name");
                    String typeName = dbInfo.getString("type");
                    //检查common信息
                    if ("common-tables".equalsIgnoreCase(dbName) && "common".equalsIgnoreCase(typeName)) {
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for (int j = 0; j < bakTableInfo.size(); j++) {
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String commonDBName = tableInfo.getString("db");
                            String tableName = tableInfo.getString("name");
                            String dbTableName = commonDBName + "." + tableName;
                            if (dbTablesMap.containsKey(commonDBName)) {
                                dbTablesMap.get(commonDBName).add(dbTableName);
                            } else {
                                Set<String> tablesSet = new HashSet<>();
                                tablesSet.add(dbTableName);
                                dbTablesMap.put(commonDBName, tablesSet);
                            }
                        }
                    }
                }
            } else {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                    //检查common信息
                    if ("common-tables".equalsIgnoreCase(dbInfo.getString("name"))
                        && "common".equalsIgnoreCase(dbInfo.getString("type"))) {
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for (int j = 0; j < bakTableInfo.size(); j++) {
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String fileType = tableInfo.getString("filetype");
                            String dbName = "";
                            String dbTableName = "";
                            if ("myisam".equalsIgnoreCase(fileType)) {
                                dbTableName = tableInfo.getString("name").replaceAll("\\.MYD", "").replaceAll("\\.MYI",
                                    "");
                                dbName = dbTableName.split("/")[0];
                            } else if ("opt".equalsIgnoreCase(fileType)) {
                                dbName = tableInfo.getString("db");
                                dbTableName = dbName + "." + tableInfo.getString("name");
                            }
                            if (dbName.length() > 0) {
                                if (dbTablesMap.containsKey(dbName)) {
                                    dbTablesMap.get(dbName).add(dbTableName);
                                } else {
                                    Set<String> tablesSet = new HashSet<>();
                                    tablesSet.add(dbTableName);
                                    dbTablesMap.put(dbName, tablesSet);
                                }
                            }
                        }
                    } else if ("db".equalsIgnoreCase(dbInfo.getString("type"))) {
                        String dbName = dbInfo.getString("name");
                        JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                        for (int j = 0; j < bakTableInfo.size(); j++) {
                            JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                            String dbTableName = dbName + "." + tableInfo.getString("name");
                            if (dbTablesMap.containsKey(dbName)) {
                                dbTablesMap.get(dbName).add(dbTableName);
                            } else {
                                Set<String> tablesSet = new HashSet<>();
                                tablesSet.add(dbTableName);
                                dbTablesMap.put(dbName, tablesSet);
                            }
                        }
                    }
                }
            }
        } catch (DataFormatException d) {
            logger.error("parse compress meta info error", d);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        return dbTablesMap;
    }

    /**
     * 返回库表备份恢复所需空间 所需空间包括两部分： 1.恢复数据所需空间 2.恢复common-tables中表结构所需空间
     *
     * @return long 单位：B
     */
    public long getAndCheckStorageForRestoreDbtables(String tableMeta, BakhistoryDO bakhistoryDO) throws RdsException {

        long commonTablesSize = 0;
        long restoreDataSize = 0;

        JSONArray tableMetaArray = JSONArray.parseArray(tableMeta);

        Long bakHisId = bakhistoryDO.getHisId();
        Map<String, Object> condition = new HashMap<>();
        condition.put("backupSetId", bakHisId);
        BakTableMetaDO bakTableMetaDO = bakService.getTableMeta(condition);
        String metaCompress = bakTableMetaDO.getInfo();
        byte[] metaCompressData = BakSupport.hexStrToByteArray(metaCompress);
        try {
            byte[] output = BakSupport.zlibDecompress(metaCompressData);
            String info = new String(output);
            JSONArray jsonArray = JSONArray.parseArray(info);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String dbName = dbInfo.getString("name");
                String typeName = dbInfo.getString("type");
                //检查common信息
                if ("common-tables".equalsIgnoreCase(dbName) && "common".equalsIgnoreCase(typeName)) {
                    JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                    for (int j = 0; j < bakTableInfo.size(); j++) {
                        JSONObject tableInfo = (JSONObject)bakTableInfo.get(j);
                        Long begin = tableInfo.getLong("begin");
                        Long end = tableInfo.getLong("end");
                        if (begin != null && end != null) {
                            commonTablesSize += end - begin;
                        }
                    }
                }
                //检查其他DB是否是需要恢复的DB
                else {
                    //遍历tableMeta中的数据库
                    for (int k = 0; k < tableMetaArray.size(); k++) {
                        JSONObject tableMetaDBInfo = (JSONObject)tableMetaArray.get(k);
                        String oldDBName = tableMetaDBInfo.getString("name");
                        //需要恢复的数据库
                        if (dbName.equals(oldDBName)) {
                            //tableMeta中的tables=[]为整库恢复
                            JSONArray tables = tableMetaDBInfo.getJSONArray("tables");
                            boolean isRestoreDB = tables == null || tables.size() == 0;
                            //获取备份的数据库下所有的库表信息
                            JSONArray bakTableInfo = dbInfo.getJSONArray("tables");
                            for (int j = 0; j < bakTableInfo.size(); j++) {
                                //备份的一个表信息
                                JSONObject bakTableInfoDetail = (JSONObject)bakTableInfo.get(j);
                                String bakTableName = bakTableInfoDetail.getString("name");
                                Long begin = bakTableInfoDetail.getLong("begin");
                                Long end = bakTableInfoDetail.getLong("end");
                                //整库恢复
                                if (isRestoreDB && begin != null && end != null) {
                                    restoreDataSize += end - begin;
                                }
                                //遍历tableMeta指定库下tables，比较表名称
                                else {
                                    for (int w = 0; w < tables.size(); w++) {
                                        JSONObject restoreTable = (JSONObject)tables.get(w);
                                        String name = restoreTable.getString("name");
                                        //当前备份中表和tableMeta中表名称相同，需要恢复
                                        if (bakTableName.equals(name)) {
                                            restoreDataSize += end - begin;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (DataFormatException e) {
            logger.error("parse compress meta info error", e);
            //抛出参数格式异常
            throw new RdsException(MysqlErrorCode.INVALID_PARAM_TABLE_META.toArray());
        }

        logger.info("commonTablesSize:" + commonTablesSize + " B, restoreDataSize:" + restoreDataSize + " B");
        //用户最终只需要承担原数据大小即可
        return commonTablesSize + restoreDataSize;
    }

    /**
     * 通过dbossApi检查tableMeta中新库表名称是否与原实例有重复 1.整库恢复场景，新库名不能与现有实例重复 2.库表恢复场景，新 库.表 名称不能与现有实例重复
     */
    public Map<String, Object> checkTableMetaWithDboss(CustInstanceDO srcCustins, String tableMeta)
        throws RdsException {

        //整库恢复新库名称
        List<String> restoreDBSet = new ArrayList<>();
        //库表恢复新库表名称
        List<String> restoreDBTableSet = new ArrayList<>();

        try {
            JSONArray jsonArray = JSONArray.parseArray(tableMeta);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject dbInfo = (JSONObject)jsonArray.get(i);
                String oldDBName = dbInfo.getString("name");
                String newDBName = dbInfo.getString("newname");
                //tables不存在，或tables=[]，则为整库恢复
                JSONArray tables = dbInfo.getJSONArray("tables");
                boolean isRestoreDB = (tables == null || tables.size() == 0);
                //整库恢复
                if (isRestoreDB) {
                    restoreDBSet.add(newDBName);
                }
                //非整库恢复
                else {
                    for (int j = 0; j < tables.size(); j++) {
                        JSONObject tableInfo = (JSONObject)tables.get(j);
                        String newTableName = tableInfo.getString("newname");
                        String newDBTableName = newDBName + "." + newTableName;
                        restoreDBTableSet.add(newDBTableName);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("check parameter tablemeta error, detail:", ex);
            //检查多次都没有结果时，放开，防止紧急情况下造成无法恢复
            return createDbossCheckResultMap(true, null, null);
        }

        //整库恢复是否已经检查通过，防止重复检查
        boolean hasCheckTotalDB = false;
        //重试三次,单次查询5秒超时
        int retryTimes = 3;
        while (retryTimes-- > 0) {
            try {

                Map<String, Object> checkDBsExistsMap = null;
                Map<String, Object> checkDBTablesExistsMap = null;

                //整库恢复为空，则整库恢复不校验
                if (restoreDBSet.isEmpty()) {
                    hasCheckTotalDB = true;
                } else {
                    if (!hasCheckTotalDB) {
                        checkDBsExistsMap = dbossApi.queryDBsBasicInfo(srcCustins.getId(), restoreDBSet);
                    }
                    //没有结果则重试
                    if (checkDBsExistsMap == null || checkDBsExistsMap.get("result") == null) {
                        continue;
                    }
                    List<String> existsDBsList = (List<String>)checkDBsExistsMap.get("result");
                    if (existsDBsList.size() > 0) {
                        return createDbossCheckResultMap(false, existsDBsList, null);
                    }
                    hasCheckTotalDB = true;
                }

                //校验库表部分
                if (restoreDBTableSet.isEmpty()) {
                    return createDbossCheckResultMap(true, null, null);
                } else {
                    checkDBTablesExistsMap = dbossApi.queryDBTablesBasicInfo(srcCustins.getId(), restoreDBTableSet);
                    if (checkDBTablesExistsMap == null || checkDBTablesExistsMap.get("result") == null) {
                        continue;
                    }
                    Map<String, String> existsDbTablesMap = (Map<String, String>)checkDBTablesExistsMap.get("result");
                    if (existsDbTablesMap.size() > 0) {
                        return createDbossCheckResultMap(false, null, existsDbTablesMap);
                    }
                }
                //返回
                return createDbossCheckResultMap(true, null, null);
            } catch (Exception ex) {
                logger.error("check parameter tablemeta error, detail:", ex);
                //某些场景下，库表回复比较紧急，需要忽略当前校验
            }
        }
        //检查多次都没有结果时，放开，防止紧急情况下造成无法恢复
        return createDbossCheckResultMap(true, null, null);
    }

    private Map<String, Object> createDbossCheckResultMap(boolean result, List<String> existsDBList,
                                                          Map<String, String> existsDbTableMap) {

        Map<String, Object> checkResult = new HashMap<>();
        String descInfo = null;
        checkResult.put("result", result);
        if (existsDBList != null) {
            checkResult.put("existsDBs", existsDBList);
            descInfo = "following dbs already exists:" + JSON.toJSONString(existsDBList);
        } else if (existsDbTableMap != null) {
            checkResult.put("existsDbTables", existsDbTableMap);
            descInfo = "following db-tables already exists:" + JSON.toJSONString(existsDbTableMap);
        }
        checkResult.put("descInfo", descInfo);
        return checkResult;
    }

    //dbType=mysql,dbVersion=5.7,instanceLevel.category=enterprise
    public boolean isMysqlXDB57() throws RdsException {

        String dbType = getAndCheckDBType(null);
        String dbVersion = getAndCheckDBVersion(dbType, true);
        String classCode = getParameterValue(ParamConstants.DB_INSTANCE_CLASS);

        if (DB_TYPE_MYSQL.equalsIgnoreCase(dbType) && DB_VERSION_MYSQL_57
            .equalsIgnoreCase(dbVersion)) {
            InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByClassCode(classCode, dbType, dbVersion,
                null, CustinsSupport.CHARACTER_TYPE_NORMAL);
            if (instanceLevel == null) {
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            return InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevel.getCategory());
        }

        return false;
    }

    public void checkNotDeleteHaProxyCustins(CustInstanceDO custins) throws RdsException {
        //FIXME:HA元数据库过滤，需要从业务中剔除
        HashMap<String, Object> haproxyMetadbFilter = new HashMap<>();
        haproxyMetadbFilter.put("metadb_ins_name", custins.getInsName());
        haproxyMetadbFilter.put("source", "rds");
        List<ProxyMetaPool> hm = null;
        try {
            hm = haProxyService.queryHaproxyMetadbPool(haproxyMetadbFilter);
        } catch (Exception e) {
            logger.warn(e.toString());
        }
        if (hm != null && hm.size() > 0) {
            throw new RdsException(ErrorCode.HAPROXY_METADB_DELETE_ERROR,
                String.format("this rds instance:%s used by haproxy metadb pool, can't delete", custins.getInsName()));
        }
        if (getBlsCustInstance(custins) != null) {
            if (haProxyService.checkCustinsConnAddrCountMoreThanOne(custins.getId(), null, null)) {
                //return createErrorResponse(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
                throw new RdsException(ErrorCode.CONNECTIONSTRING_NOT_FOUND);
            }
        }
    }

    protected EntityTagDO getBlsCustInstance(CustInstanceDO custins) {
        List<EntityTagDO> entityTagList = custinsService.getEntityTagList(ParamConstants.ENTITY_TYPE_CUSTINS,
            String.valueOf(custins.getId()), "bls-db-replica-id");
        if (null != entityTagList && entityTagList.size() > 0) {
            return entityTagList.get(0);
        }
        return null;
    }

    public boolean checkIsXdbEnterprise(CustInstanceDO custIns) {

        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custIns.getLevelId());
        if (instanceLevel == null) {
            return false;
        }
        return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
            && DB_TYPE_MYSQL.equalsIgnoreCase(custIns.getDbType())
            && DB_VERSION_MYSQL_57.equalsIgnoreCase(custIns.getDbVersion());
    }

    public boolean isMysqlEnterprise57(InstanceLevelDO instanceLevel) {
        return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
            && DB_TYPE_MYSQL.equalsIgnoreCase(instanceLevel.getDbType())
            && DB_VERSION_MYSQL_57.equalsIgnoreCase(instanceLevel.getDbVersion());
    }

    // 根据mysql实例检查maxscale实例是否正常
    public void checkMaxscaleStatus(CustInstanceDO custins) throws RdsException {
        CustInstanceDO maxscale_ins;
        if (custins.getPrimaryCustinsId() != 0) {
            CustInstanceDO primary_ins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            maxscale_ins = custinsService.getMaxscaleCustins(primary_ins);
        } else {
            maxscale_ins = custinsService.getMaxscaleCustins(custins);
        }
        if (maxscale_ins != null) {
            if (!maxscale_ins.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS, "maxscale status mot active");
            }
            CustInstanceDO maxscale_ins_server = custinsService.getCustInstanceByParentId(maxscale_ins.getId()).get(0);
            if (!maxscale_ins_server.isActive()) {
                throw new RdsException(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS, "maxscale status mot active");
            }
        }
    }

    public Integer getPenginePolicyID() {
        String penginePolicyId = getParameterValue("PenginePolicy", "0");
        try {
            return Integer.parseInt(penginePolicyId);
        } catch (NumberFormatException e) {
            logger.error(e.getMessage(), e);
            return 0;
        }
    }

    public BakhistoryDO validCloneStorageForEcs(CustInstanceDO srcCustins,
                                                Long cloneCustinsDiskSize,
                                                String restoreType,
                                                Date recoverTime)
        throws RdsException {
        long diskSize = 0;
        BakhistoryDO history = new BakhistoryDO();
        Integer custinsId = srcCustins.getId();
        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            if (KIND_CODE_NC.equals(srcCustins.getKindCode())|| KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD.equals(srcCustins.getKindCode())) {
                history = bakService.getBakhistoryByRecoverTime(custinsId, recoverTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
            } else {
                history = bakService.getBakhistoryByRecoverTime(custinsId, recoverTime, BAKWAY_SNAPSHOT, BAKTYPE_FULL);
            }
            Long maxBinlogSize = bakService.getMaxArchivelogByRecoverTime(custinsId, recoverTime,
                history.getBakBegin());

            String baksetInfo = history.getBaksetInfo();
            JSONObject baksetInfoJsonObject = JSONObject.parseObject(baksetInfo);
            long diskSizeUsed = baksetInfoJsonObject.getLongValue("disk_size");

            if (cloneCustinsDiskSize < history.getBaksetSize() / 1024) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }

            // 克隆实例的磁盘不能小于快照的大小 + 2 * max_binlog，单位KB
            diskSize = diskSizeUsed * 1024 + 2 * maxBinlogSize / 1024;

        } else if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            Long bakId = CheckUtils.parseLong(getParameterValue("BackupSetID"), null,
                null, ErrorCode.BACKUPSET_NOT_FOUND);
            history = bakService.getBakhistoryByBackupSetId(custinsId, bakId);
            // 克隆实例的磁盘不能小于快照的大小，单位KB
            diskSize = history.getBaksetSize();
        }

        if (cloneCustinsDiskSize < diskSize / 1024) {
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }

        return history;
    }

    public boolean isRdsSrvDockerize(String dbType) {
        return !isRdsSrvUnDockerize(dbType);
    }

    public boolean isRdsSrvUnDockerize(String dbType) {
        if (dbType.equals("hbase") || dbType.equals("mysql")) {
            return true;
        } else {
            return false;
        }
    }

    public void setDiskSize(CustInstanceDO custins, Integer bizType, String diskSize, Integer min) throws RdsException {
        ResourceKey resourceKey = ResourceKey.RESOURCE_CUSTINS_MAX_DISK_SIZE;
        if (CustinsSupport.BIZ_TYPE_PARTITION.equals(bizType)) {
            resourceKey = ResourceKey.RESOURCE_PARTITION_MAX_DISK_SIZE;
        }
        Integer maxDiskSize = ResourceSupport.getInstance().getIntegerRealValue(resourceKey);
        custins.setDiskSize(
            CheckUtils.parseInt(diskSize, min, maxDiskSize, ErrorCode.INVALID_STORAGE)
                * 1024L);
    }

    //clone时使用
    public RequestParamsDO inflateDockerParamsWhenClone(CustInstanceDO custins) throws RdsException {

        RequestParamsDO r = new RequestParamsDO();
        r.setHostType(getAndCheckHostType());
        r.setRegionId(getParameterValue(ParamConstants.REGION_ID));
        r.setZoneId(getParameterValue(ParamConstants.ZONE_ID));
        r.setConnType(getAndCheckConnType(null));
        r.setPortStr(CustinsSupport.getConnPort(getParameterValue("port"), custins.getDbType()));
        r.setBizType(getAndCheckBizType());
        r.setNetType(CustinsSupport.getNetType(getParameterValue(ParamConstants.DB_INSTANCE_NET_TYPE, "1")));
        //只支持独占实例，不支持s
        r.setType("x");
        r.setStorage(getParameterValue(ParamConstants.STORAGE));
        r.setClassCode(getParameterValue(ParamConstants.DB_INSTANCE_CLASS));
        r.setIpSet(getAndCheckSecurityIpList());
        r.setWhitelistNetType(getAndCheckWhitelistNetType());
        r.setConnectionString(
            CheckUtils.checkNullForConnectionString(getParameterValue(ParamConstants.CONNECTION_STRING)));
        r.setRegion(getAndCheckRegion());
        if (hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            r.setDesc(SupportUtils.decode(getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION)));
        }
        r.setAction(getAction());
        r.setOperatorId(getOperatorId());
        if (CustinsSupport.isVpcNetType(r.getNetType())) {
            r.setUserVpcId(getParameterValue(ParamConstants.VPC_ID));
            r.setTunnelId(Integer.valueOf(getParameterValue(ParamConstants.TUNNEL_ID)));
            r.setVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            r.setIpaddress(getParameterValue(ParamConstants.IP_ADDRESS));
        }
        return r;
    }

    //创建时使用
    public RequestParamsDO inflateDockerParamsWhenCreate(CustInstanceDO characterCustIns, ShardInfo shardInfo, Boolean isUserCluster)
        throws RdsException {
        RequestParamsDO r = inflateDockerParamsWhenCreate(characterCustIns, isUserCluster);
        if (shardInfo != null) {
            if (CustinsSupport.isVpcNetType(r.getNetType())) {
                String tunnelId = shardInfo.getTunnelId();
                if (tunnelId == null) {
                    r.setTunnelId(null);
                } else {
                    r.setTunnelId(Integer.valueOf(tunnelId));
                }
                r.setUserVpcId(shardInfo.getVPCId());
                r.setVswitchId(shardInfo.getVSwitchId());
                r.setVpcInstanceId(shardInfo.getVpcInstanceID());
                r.setIpaddress(shardInfo.getIPAddress());
                r.setChildNetType(r.getNetType());
            }
            r.setCharacterInsName(shardInfo.getDBInstanceName());
        }
        return r;
    }

    //TODO:有很多inflateDockerParams方法，根据使用场景，加了后缀
    public RequestParamsDO inflateDockerParamsWhenCreate(CustInstanceDO characterCustIns,
                                                         Boolean isUserCluster) throws RdsException{
        CustInstanceDO parentCustIns
            = characterCustIns.getParentCustIns() != null ? characterCustIns.getParentCustIns(): characterCustIns;

        RequestParamsDO r = new RequestParamsDO();
        // validate ShardsInfo.
        JSONArray mysqls = null;
        String clusterName = null;
        String region = null;
        String shardInfo = getParameterValue("ShardsInfo");
        if (Validator.isNotNull(shardInfo)) {
            JSONObject shardJson = JSON.parseObject(shardInfo);
            mysqls = shardJson.getJSONArray("Mysqls");
            if (null != mysqls && mysqls.size() > 1) {
                throw new RdsException(ErrorCode.INVALID_SHARDS_INFO);
            }
        }

        region = getAndCheckRegion();
        if (null != mysqls) {
            JSONObject mysql = JSON.parseObject(mysqls.getString(0));
            CheckUtils.checkValidForNodeIns(CustinsSupport.CHARACTER_TYPE_MYSQL_MYSQLS, mysql);
            clusterName
                = mysql.get(ParamConstants.CLUSTER_NAME) == null ? getParameterValue(ParamConstants.CLUSTER_NAME)
                : mysql.get(ParamConstants.CLUSTER_NAME).toString();
            region
                = mysql.get(ParamConstants.REGION) == null ? getAndCheckRegion()
                : mysql.get(ParamConstants.REGION).toString();
        }
        r.setRegion(region);
        if (null != clusterName) {
            r.setClusterName(clusterName);
        }
        r.setHostType(getAndCheckHostType());
        r.setStorageType(getParameterValue(ParamConstants.DB_INSTANCE_STORAGE_TYPE));
        r.setRegionId(getParameterValue(ParamConstants.REGION_ID));
        r.setZoneId(getParameterValue(ParamConstants.ZONE_ID));
        r.setConnType(getAndCheckConnType(null));
        r.setPortStr(CustinsSupport.getConnPort(getParameterValue("port"), parentCustIns.getDbType()));
        r.setBizType(getAndCheckBizType());
        r.setNetType(CustinsSupport.getNetType(getParameterValue(ParamConstants.DB_INSTANCE_NET_TYPE,
            "1")));
        //只支持独占实例，不支持s
        r.setType("x");
        r.setStorage(getParameterValue(ParamConstants.STORAGE));
        r.setClassCode(getParameterValue(ParamConstants.DB_INSTANCE_CLASS));
        r.setIpSet(getAndCheckSecurityIpList());
        r.setWhitelistNetType(getAndCheckWhitelistNetType());
        r.setConnectionString(CheckUtils.checkValidForConnAddrCust
            (getParameterValue(ParamConstants.CONNECTION_STRING)));
        if (hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            r.setDesc(SupportUtils.decode(getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION)));
        }
        r.setAction(getAction());
        r.setOperatorId(getOperatorId());
        if (CustinsSupport.isVpcNetType(r.getNetType())) {
            r.setUserVpcId(getParameterValue(ParamConstants.VPC_ID));
            r.setVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            if (!CONN_TYPE_PHYSICAL.equals(getParameterValue(ParamConstants.DB_INSTANCE_CONN_TYPE))) {
                String tunnelId = getParameterValue(ParamConstants.TUNNEL_ID);
                if (tunnelId != null) {
                    r.setTunnelId(Integer.valueOf(tunnelId));
                }
                r.setIpaddress(getParameterValue(ParamConstants.IP_ADDRESS));
                // ignore api input，current add this.
                r.setVpcInstanceId(getParameterValue(VPC_INSTANCE_ID));
            }
        }

        InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByClassCode(
            getAndCheckClassCode(), parentCustIns.getDbType(), parentCustIns.getDbVersion(), parentCustIns.getTypeChar(), null);
        if (parentCustIns.isMbaseDockerOnEcs() && !instanceLevel.isBasicLevel() && !isUserCluster){
            String multiAVZExParamStr = getParameterValue(ParamConstants.MULTI_AVZ_EX_PARAM);
            if (StringUtils.isEmpty(multiAVZExParamStr) && characterCustIns.getMultiAVZExParams() == null) {
                throw new RdsException(ErrorCode.INVALID_PARAMETERS, ParamConstants.MULTI_AVZ_EX_PARAM + " is empty");
            }
            MultiAVZExParamDO multiAVZExParamDO = JSON.parseObject(multiAVZExParamStr, MultiAVZExParamDO.class);
            multiAVZExParamDO = multiAVZExParamDO == null ? characterCustIns.getMultiAVZExParams(): multiAVZExParamDO;
            r.setMultiAVZExParam(multiAVZExParamDO);
        }
        return r;
    }

    /**
     * 设置docker化参数
     * 评估资源，评估修改时使用
     * */
    public RequestParamsDO inflateDockerParamsWhenEvaluate(CustInstanceDO custins) throws RdsException {
        RequestParamsDO r = new RequestParamsDO();
        // 检查ShardsInfo参数
        JSONArray mysqls = null;
        String shardInfo = getParameterValue("ShardsInfo");
        if (Validator.isNotNull(shardInfo)) {
            JSONObject shardJson = JSON.parseObject(shardInfo);
            mysqls = shardJson.getJSONArray("Mysqls");
        }
        if (null != mysqls && mysqls.size() > 1) {
            throw new RdsException(ErrorCode.INVALID_SHARDS_INFO);
        }

        String clusterName = null;
        String region = null;

        if (null != mysqls) {
            JSONObject mysql = JSON.parseObject(mysqls.getString(0));
            CheckUtils.checkValidForNodeIns(CustinsSupport.CHARACTER_TYPE_MYSQL_MYSQLS, mysql);

            // clusterName or region.
            clusterName = mysql.get(ParamConstants.CLUSTER_NAME) == null ? getParameterValue(ParamConstants.CLUSTER_NAME) : mysql.get(
                ParamConstants.CLUSTER_NAME).toString();
            region = mysql.get(ParamConstants.REGION) == null ? getAndCheckRegion() : mysql.get(ParamConstants.REGION).toString();
        } else {
            region = getAndCheckRegion();
        }

        r.setHostType(getAndCheckHostType());
        r.setRegionId(getParameterValue(ParamConstants.REGION_ID));
        r.setZoneId(getParameterValue(ParamConstants.ZONE_ID));
        r.setConnType(getAndCheckConnType(null));
        r.setPortStr(CustinsSupport.getConnPort(getParameterValue("port"), custins.getDbType()));
        r.setBizType(getAndCheckBizType());
        r.setNetType(CustinsSupport.getNetType(getParameterValue(ParamConstants.DB_INSTANCE_NET_TYPE, "1")));
        r.setType("x");
        r.setStorage(getParameterValue(ParamConstants.STORAGE));
        r.setClassCode(getParameterValue(ParamConstants.DB_INSTANCE_CLASS));

        // set clusterName & region.
        r.setRegion(region);
        if (null != clusterName) {
            r.setClusterName(clusterName);
        }
        if (hasParameter(ParamConstants.DB_INSTANCE_DESCRIPTION)) {
            r.setDesc(SupportUtils.decode(getParameterValue(ParamConstants.DB_INSTANCE_DESCRIPTION)));
        }
        r.setAction(getAction());
        r.setOperatorId(getOperatorId());
        if (CustinsSupport.isVpcNetType(r.getNetType())) {
            r.setUserVpcId(getParameterValue(ParamConstants.VPC_ID));
            r.setTunnelId(Integer.valueOf(getParameterValue(ParamConstants.TUNNEL_ID)));
            r.setVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            r.setIpaddress(getParameterValue(ParamConstants.IP_ADDRESS));
        }
        return r;
    }

    public VipResModel initVipResModel(Integer netType, String connAddrCust, Integer port, Integer vportCount) {
        VipResModel vipResModel = new VipResModel(netType);
        List<Integer> vportList = new ArrayList<>();
        for (int k = 0; k < vportCount; k++) {
            vportList.add(port + k);
        }
        vipResModel.setVportList(vportList);
        vipResModel.setConnAddrCust(connAddrCust);
        return vipResModel;
    }

    public VipResModel initVipResModel(Integer netType, String connAddrCust, Integer port, Integer vportCount,
                                       Integer tunnelId,
                                       String vpcId, String vswitchId, String ipAddress, String vpcInstanceId) {
        VipResModel vipResModel = new VipResModel(netType);
        List<Integer> vportList = new ArrayList<>();
        for (int k = 0; k < vportCount; k++) {
            vportList.add(port + k);
        }
        vipResModel.setVportList(vportList);
        vipResModel.setConnAddrCust(connAddrCust);

        if (CustinsSupport.isVpcNetType(netType)) {
            vipResModel.setVpcId(vpcId);
            vipResModel.setVpcInstanceId(vpcInstanceId);
            vipResModel.setTunnelId(tunnelId);
            vipResModel.setVswitchId(vswitchId);
            vipResModel.setVip(ipAddress);
        }
        return vipResModel;
    }

    /**
     * 创建docker化实例依赖的rds服务
     */

    public void createRdsServiceForDocker(CustInstanceDO custins,
                                          DockerInsLevelParseConfig config,
                                          InstanceLevelDO characterInsLevel,
                                          Integer netType,
                                          ResourceContainer resourceContainer,
                                          RequestParamsDO params,
                                          String serviceType) throws RdsException {
        logger.error("create rds serice");
        //vip?vport 用户名:密码?,白名单--->任务阶段设置

        CustinsResModel custinsResModel = new CustinsResModel(custins.getId());
        List<RdsResModel> rdsResModelList = new ArrayList<>();
        Map<String, Object> custinsBasicConfig = null;
        RdsResModel rdsResModel = null;

        if (characterInsLevel.getDbType().equalsIgnoreCase("mysql")) {
            custinsBasicConfig = getMysqlResConfig();
            custinsBasicConfig.put("insName", custins.getInsName());
            rdsResModel = getRdsSrvResModel(params, characterInsLevel, custinsBasicConfig);
        } else {
            logger.error("Didn't support type");
        }
        rdsResModelList.add(rdsResModel);
        custinsResModel.setRdsResModelList(rdsResModelList);
        resourceContainer.addCustinsResModel(custinsResModel);
    }

    public VipResModel initLogicInsVipResMode(CustInstanceDO logicIns) throws RdsException {

        Integer netType = getAndCheckNetType();
        String connType = CustinsSupport.CONN_TYPE_LVS;
        if (CustinsSupport.isVpcNetType(netType)) {
            connType = CustinsSupport.getAndCheckConnTypeForVPC(connType,
                logicIns.getDbType());
        }

        String portStr = CustinsSupport
            .getConnPort(getParameterValue(ParamConstants.PORT),
                logicIns.getDbType());

        String connPort = CheckUtils.parseInt(portStr, 1000, 5999, ErrorCode.INVALID_PORT)
            .toString();

        String connectionString = CheckUtils
            .checkNullForConnectionString(
                getParameterValue(ParamConstants.CONNECTION_STRING));

        String connAddrCust = getConnAddrCust(connectionString, getRegionIdByClusterName(logicIns.getClusterName()), logicIns.getDbType());
        CustinsConnAddrDO custinsConnAddr = null;
        if (CustinsSupport.isVpcNetType(netType)) {
            // 校验VPC网络类型实例的链路类型
            connType = CustinsSupport.getAndCheckConnTypeForVPC(connType,
                logicIns.getDbType());

            // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
            String vpcInstanceId = logicIns.getInsName();
            String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(ParamConstants.TUNNEL_ID));
            String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            String ipaddress = CheckUtils.checkValidForIPAddress(getParameterValue(ParamConstants.IP_ADDRESS));
            vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(getParameterValue(ParamConstants.VPC_INSTANCE_ID));

            custinsConnAddr = ConnAddrSupport
                .createCustinsConnAddr(connAddrCust, connPort,
                    netType, CustinsValidator.getRealNumber(tunnelId, -1),
                    vpcId,
                    vswitchId,
                    ipaddress,
                    vpcInstanceId);

        } else {
            // 创建实例连接对象
            custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(connAddrCust, connPort,
                netType, -1, null, null, null, null);
        }

        VipResModel vipResModel = new VipResModel(custinsConnAddr.getNetType());
        vipResModel.setUserVisible(custinsConnAddr.getUserVisible());
        vipResModel.setConnAddrCust(custinsConnAddr.getConnAddrCust());
        vipResModel.setVip(custinsConnAddr.getVip());
        vipResModel.setVport(Integer.valueOf(custinsConnAddr.getVport()));
        vipResModel.setVpcId(custinsConnAddr.getVpcId());
        vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
        vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
        vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
        return vipResModel;
    }

    public CheckBaksetDO validRestoreByUser(CustInstanceDO custins) throws RdsException {
        if (custins.isCustinsOnEcs()) {
            throw new RdsException(ErrorCode.INVALID_RESTORE_TYPE);
        }
        String downloadUrl = getAndCheckDownloadUrl();
        CheckBaksetDO bakset = getAndCheckCheckBakset();
        bakset.setDownloadUrl(downloadUrl);
        return bakset;
    }

    public AccountPriviledgeType getAdminPriviledgeType(AccountPriviledgeType defaultValue) throws RdsException {
        int adminType = getAdminType();
        AccountPriviledgeType apt = AccountPriviledgeType.getAccountPriviledgeTypeByAdminType(
            adminType);
        if (apt == null) {//未指定adminType，则返回默认值defaultValue
            return defaultValue;
        } else {
            return apt;
        }
    }

    public int getAdminType() {
        String adminType = getParameterValue("AdminType", "");
        int atValue = -1;
        try {
            atValue = Integer.parseInt(adminType);
        } catch (NumberFormatException ignore) {
        }

        return atValue;
    }

    //检查设置实例KindCode
    public void checkAndSetKindCode(CustInstanceDO custins, String hostType) {

        if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_ClOUD_SSD)) {
            custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS);
        } else if (hostType != null && hostType.equals(CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_POLARSTORE)) {
            custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_POLARSTORE);
        } else if (hostType != null && hostType.equals(
            CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_ECS_LOCAL_SSD)) {
            custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD);
        } else {
            custins.setKindCode(CustinsSupport.KIND_CODE_DOCKER);
        }
    }


    public Long checkRestoreIllegalAndGetSize(CustInstanceDO custins, String bakIdStr, String restoreType) throws RdsException{
        Long bakDataSize = 0L;
        if (restoreType != null) {
            restoreType = this.getAndCheckRestoreType();
            if (RESTORE_TYPE_OSSBAK.equals(restoreType) && custins.isCustinsDockerOnPolarStore()){
                if (bakIdStr != null) {
                    Long bakId = CheckUtils.parseLong(bakIdStr, null, null, ErrorCode.BACKUPSET_NOT_FOUND);
                    // -1 as 10G space resolved, bakDataSize for Mbyte
                    bakDataSize = (bakService.getOssBakSizeByBackupSetId(bakId) - 1) * 10 *  1024;
                    if (bakDataSize <= 0) {
                        throw new RdsException(ErrorCode.INVALID_BAKSET);
                    }
                } else {
                    throw new RdsException(ErrorCode.INVALID_BAKSET);
                }
            } else if (RESTORE_TYPE_BAKID.equals(restoreType)
                && (custins.isCustinsDockerOnPolarStore() || custins.isKeplerCstore())) {
                Long bakId = CheckUtils.parseLong(bakIdStr, null,
                    null, ErrorCode.BACKUPSET_NOT_FOUND);
                // for polarstore, baksetSize means disk size, Mbyte
                bakDataSize = this.getAndCheckBakhistoryById(custins, bakId).getBaksetSize();
            }
        }
        return bakDataSize;
    }

    public BakhistoryDO getAndCheckBakhistoryById(CustInstanceDO custins, Long bakId)
        throws RdsException {
        BakhistoryDO history = bakService.getBakhistoryById(bakId);

        if (history == null) {
            throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
        }
        if (!history.isBakForInstance()) {//仅支持实例级别备份
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (!history.getStatus().equals("OK") || history.getIsAvail() == 0) {//加判断is avail
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETSTATUS);
        }
        if (custins.isMysql() && custins.isExcluse() && BAKWAY_MYSQLDUMP
            .equals(history.getBakWay())) {
            throw new RdsException(ErrorCode.UNSUPPORTED_BACKUPSETMETHOD);
        }
        if (BAKTYPE_INCREMENT.equals(history.getBakType())) {
            Integer countBakHis = bakService
                .countBakHisotryBeforeBakId(custins.getId(), bakId, BAKTYPE_FULL, 1);
            if (countBakHis <= 0) {
                throw new RdsException(ErrorCode.BACKUPSET_NOT_FOUND);
            }
        }
        return history;
    }

    public void setRestoreStorageSize(String restoreType, String hostType, Long bakDataSize) throws RdsException{
        if (CustinsParamSupport.CUSTINS_PARAM_VALUE_HOST_TYPE_POLARSTORE.equals(hostType) && bakDataSize != 0) {
            String storage = getParameterValue(ParamConstants.STORAGE);
            Long storageSize = 0L;
            //非0，以用户为准
            if (StringUtils.isNotEmpty(storage)) {
                storageSize = Long.parseLong(storage);
                // bakDataSize from Mbyte to Gbyte
                if (storageSize < bakDataSize / 1024 / 1024) {
                    throw new RdsException(ErrorCode.INVALID_STORAGE);
                }
            } else {
                String sourceDbInstanceName = getParameterValue(ParamConstants.SOURCE_DB_INSTANCE_NAME);
                CustInstanceDO cutins = custinsService.getCustInstanceByInsName(getAndCreateUserId(), sourceDbInstanceName);
                if (cutins == null) {
                    // bakDataSize from KByte to GByte
                    storageSize = bakDataSize / 1024 / 1024;
                } else {
                    if (cutins.getDiskSize() < (bakDataSize / 1024)) {
                        throw new RdsException(ErrorCode.INVALID_SOURCE_DB);
                    }
                    storageSize = cutins.getDiskSize() / 1024;
                }
            }
            setParameter(ParamConstants.STORAGE, Long.toString(storageSize));
        }
    }

    public CustInstanceDO validAndGetSrcCust(String specifiedDbType, String specifiedEngineVersion) throws RdsException{

        String sourceDbInstanceName = getParameterValue(ParamConstants.SOURCE_DB_INSTANCE_NAME);
        if (StringUtils.isEmpty(sourceDbInstanceName)) {
            logger.error("SourceDBInstanceName is NULL or Empty");
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }
        CustInstanceDO cutins = custinsService.getCustInstanceByInsName(getAndCreateUserId(), sourceDbInstanceName);
        if (null == cutins) {
            logger.error("Cust instance is not found");
            throw new RdsException(ErrorCode.DBINSTANCE_NOT_FOUND);
        }

        if(!cutins.getDbType().equals(specifiedDbType)){
            throw new RdsException(ErrorCode.UNSUPPORTED_DB_TYPE);
        }

        if(!cutins.getDbVersion().equals(specifiedEngineVersion)){
            throw new RdsException(ErrorCode.INVALID_MINOR_VERSION);
        }

        return cutins;
    }

    public Date getAndCheckTimeByDateStr(String dateStr, DateUTCFormat format, ErrorCode errorCode, String dataSource) throws RdsException {
        return getAndCheckTimeByDateStr(dateStr, format, errorCode, 1, dataSource);
    }

    public Date getAndCheckTimeByDateStr(String dateStr, DateUTCFormat format, ErrorCode errorCode, Integer factor, String dataSource) throws RdsException {
        Integer timeZoneDiffSec = getMetaDBTimeZoneDiffSeconds(dataSource);
        return CheckUtils.getAndCheckDateTime(dateStr, format, errorCode, factor * timeZoneDiffSec);
    }

    private Integer getMetaDBTimeZoneDiffSeconds(String dataSource) {
        return dtzSupport.getMetaDBTimeZoneDiffSeconds(dataSource);
    }

    public String getAndCheckDBInstanceClassCode(CustInstanceDO custins, String dbType) throws RdsException {
        String classCode = getParameterValue(ParamConstants.DB_INSTANCE_CLASS);
        if (StringUtils.isBlank(classCode)) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }

        Boolean isAnalyticdb = false;
        if (custins == null && dbType == null) {
            return classCode;
        } else if (custins != null) {
            isAnalyticdb = custins.isAnalyticDB();
        } else if (dbType != null) {
            isAnalyticdb = CustinsSupport.isAnalyticDB(dbType);
        }

        Integer groupCount = getAndCheckDBInstanceGroupCount(custins, dbType);
        if (groupCount == null || !isAnalyticdb) {
            return classCode;
        }

        Integer length = classCode.length();
        Integer endIndex = length;
        for (Integer i=length-1; i>0; i--) {
            if (Character.isDigit(classCode.charAt(i))) {
                endIndex--;
                continue;
            }
            break;
        }

        if (endIndex == length) {
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }

        return classCode.substring(0, endIndex) + groupCount;
    }

    public Integer getAndCheckDBInstanceGroupCount(CustInstanceDO custins, String dbType) throws RdsException {
        if (custins != null) {
            return getAndCheckDBInstanceGroupCount(custins);
        } else if (dbType != null) {
            return getAndCheckDBInstanceGroupCount(dbType);
        } else {
            throw new RdsException(ErrorCode.INVALID_PARAM);
        }
    }

    public Integer getAndCheckDBInstanceGroupCount(String dbType) throws RdsException {
        String groupCountStr = getParameterValue(ParamConstants.DB_INSTANCE_GROUP_COUNT);
        Integer groupCount = CustinsValidator.getRealNumber(groupCountStr, -1);
        if (groupCountStr == null) {
            return null;
        }
        return null;
    }


    public void fillRestoreByTimeTaskParam(Map<String, Object> taskQueueParam, CustInstanceDO srcCustins) throws RdsException{
        Date restoreTimeDate = getAndCheckTimeByParam(ParamConstants.RESTORE_TIME,
            DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME, DataSourceMap.DATA_SOURCE_DBAAS);
        taskQueueParam.put("recover_time", DateSupport.second2str(restoreTimeDate));
        taskQueueParam.put("recover_custins_id", srcCustins.getId());
        taskQueueParam.put("restore_type", RESTORE_TYPE_TIME);
    }

    public void fillRestoreBySrcCustTaskParam(Map<String, Object> taskQueueParam, CustInstanceDO srcCustins) throws RdsException{
        taskQueueParam.put("recover_custins_id", srcCustins.getId());
        taskQueueParam.put("restore_type", RESTORE_TYPE_SRCCUST);
    }

    public Map<String, Object> getTaskQueueParamWithBakperiodDefault(String bakPeriod) throws RdsException {
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        Map<String, Object> taskQueueParam = new HashMap<String, Object>(2);
        Map<String, Object> baklistParam = new HashMap<String, Object>(3);
        baklistParam.put("retention", CheckUtils
            .parseInt(this.getParameterValue("backupretentionperiod"), 1, 730,
                ErrorCode.INVALID_BACKUPRETENTIONPERIOD));
        baklistParam.put("bak_period", CheckUtils.checkValidForBackupPeriod(
            this.getParameterValue("preferredbackupperiod", bakPeriod)));
        String preferredBackupTime = this.getParameterValue("preferredbackuptime");
        if (!DateSupport.isminuteonly_utc(preferredBackupTime)) {
            return ResponseSupport.createErrorResponse(ErrorCode.INVALID_PREFERREDBACKUPTIME);
        }
        baklistParam.put("bak_begin", preferredBackupTime);
        taskQueueParam.put("backup", baklistParam);

        setEcsSgRelParams(taskQueueParam);
        return taskQueueParam;
    }

    public void setEcsSgRelParams(Map<String, Object> taskQueueParam) {
        String ecsSecurityGroupId = this.getParameterValue(ParamConstants.ECS_SECURITY_GROUP_ID);
        if (ecsSecurityGroupId != null && !ecsSecurityGroupId.isEmpty()) {
            Map<String, Object> ecsSGParams = new HashMap<String, Object>(2);
            ecsSGParams.put("region_id", this.getParameterValue(ParamConstants.REGION_ID));
            ecsSGParams.put("sg_id", ecsSecurityGroupId);
            taskQueueParam.put("ecs_sg", ecsSGParams);
        }
    }

    public Map<String, Object> getTaskQueueParam() throws RdsException {
        // 获取备份策略参数，下发实例创建时，这些参数将被写入parameter字段中
        return getTaskQueueParamWithBakperiodDefault("1111100");
    }

    public Map<String, List<ShardsInfo.Node>> SplitNodesFromShardsInfobyDbTypeAndDbVersion(String shardsInfo)
        throws RdsException {
        Map<String, List<ShardsInfo.Node>> nodesMap = new HashMap<String, List<ShardsInfo.Node>>();
        if (shardsInfo == null){
            return nodesMap;
        }
        ShardsInfo info = new Gson().fromJson(shardsInfo, ShardsInfo.class);
        try{
            List<ShardsInfo.Node> nodes = info.getNodes();
            if (nodes == null){
                return nodesMap;
            }
            for (ShardsInfo.Node node: nodes){
                String unique_key = node.getEngine() + node.getEngineVersion();
                if (nodesMap.containsKey(unique_key)){
                    nodesMap.get(unique_key).add(node);
                }else{
                    nodesMap.put(unique_key, new ArrayList<ShardsInfo.Node>());
                    nodesMap.get(unique_key).add(node);
                }
            }
        }catch (Exception e){
            String message = "Parse shardsInfo " + shardsInfo + "\nfailed: " + e;
            logger.error(message, e);
            throw new RdsException(ErrorCode.INVALID_SHARDS_INFO, message);
        }
        return nodesMap;
    }

    public Map<String, List<ShardInfo>> getShardsInfoMap(String strShardsInfo) {
        Map<String, List<ShardInfo>> mapShardsInfo = new HashMap<>();
        if (strShardsInfo == null){
            return mapShardsInfo;
        }
        JSONObject jsonShardsInfo = JSON.parseObject(strShardsInfo);
        for(String key: jsonShardsInfo.keySet()) {
            List<ShardInfo> listShardInfo = new ArrayList<>();
            for (Object shard: jsonShardsInfo.getJSONArray(key)){
                listShardInfo.add(new Gson().fromJson(JSON.toJSONString(shard), ShardInfo.class));
            }
            mapShardsInfo.put(key.toLowerCase(), listShardInfo);
        }
        return mapShardsInfo;
    }

    public List<VpcInfo> getVpcInfos(Map<String, List<ShardInfo>> shardInfos) {
        List<VpcInfo> vpcInfos = new ArrayList<>();
        for (Map.Entry<String, List<ShardInfo>> entry: shardInfos.entrySet()) {
            for (ShardInfo shardInfo: entry.getValue()) {
                if (shardInfo.getVSwitchId() != null) {
                    VpcInfo vpcInfo = new VpcInfo();
                    vpcInfo.setVswitchId(shardInfo.getVSwitchId());
                    vpcInfo.setVpcId(shardInfo.getVPCId());
                    vpcInfo.setIpAddress(shardInfo.getIPAddress());
                    vpcInfo.setTunnelId(shardInfo.getTunnelId() != null ? Integer.valueOf(shardInfo.getTunnelId()): 0);
                    vpcInfo.setVpcInstanceId(shardInfo.getVpcInstanceID());
                    vpcInfos.add(vpcInfo);
                }
            }
        }
        return vpcInfos;
    }

    public MultiAVZExParamDO fakeMultiAvzExParam(List<VpcInfo> vpcInfos) {
        if (vpcInfos == null || vpcInfos.size() == 0) {
            return null;
        }

        MultiAVZExParamDO multiAVZExParamDO = new MultiAVZExParamDO();
        List<AvailableZoneInfoDO>  availableZoneInfos = new ArrayList<>();
        AvailableZoneInfoDO  mAvailableZoneInfo = new AvailableZoneInfoDO();
        mAvailableZoneInfo.setRole("master");
        mAvailableZoneInfo.setZoneID(getParameterValue(ZONE_ID));
        mAvailableZoneInfo.setVSwitchID(vpcInfos.get(0).getVswitchId());
        availableZoneInfos.add(mAvailableZoneInfo);

        AvailableZoneInfoDO  sAvailableZoneInfo = new AvailableZoneInfoDO();
        sAvailableZoneInfo.setRole("slave");
        sAvailableZoneInfo.setZoneID(getParameterValue(ZONE_ID));
        sAvailableZoneInfo.setVSwitchID(vpcInfos.get(0).getVswitchId());
        availableZoneInfos.add(sAvailableZoneInfo);

        multiAVZExParamDO.setAvailableZoneInfoList(availableZoneInfos);
        return multiAVZExParamDO;
    }

    public boolean needSqllogNewVersion(CustInstanceDO custins){
        if(custinsParamService.isNewSqlLogSwitch(custins)){
            if(CustinsSupport.isDockerOnPolarStore(custins.getKindCode())){
                return true;
            }
            if(custins.isMysql() && custins.isLogic() &&
                    (custins.isCustinsDockerOnEcs() || custins.isCustinsOnDockerOnEcsLocalSSD())){
                return true;
            }
        }
        return false;
    }

    public VipResModel initLogicInsVipResModel(CustInstanceDO logicIns, String connType)
        throws RdsException {

        Integer netType = getAndCheckNetType();

        String connPort = CustinsSupport.getConnPort(
            getParameterValue(ParamConstants.PORT), logicIns.getDbType(), logicIns.getKindCode());
        String connectionString = CheckUtils.checkNullForConnectionString(getParameterValue(ParamConstants.CONNECTION_STRING));

        String connAddrCust = getConnAddrCust(connectionString, getRegionIdByClusterName(logicIns.getClusterName()), logicIns.getDbType());
        CustinsConnAddrDO custinsConnAddr = null;
        if (CustinsSupport.isVpcNetType(netType)) {
            // 校验VPC网络类型实例的链路类型
            connType = CustinsSupport.getAndCheckConnTypeForVPC(connType, logicIns.getDbType());

            // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
            String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(ParamConstants.TUNNEL_ID));
            String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            String ipaddress = CheckUtils.checkValidForIPAddress(getParameterValue(ParamConstants.IP_ADDRESS));
            String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(getParameterValue(ParamConstants.VPC_INSTANCE_ID));

            custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(connAddrCust, connPort,
                netType, CustinsValidator.getRealNumber(tunnelId, -1),
                vpcId,
                vswitchId,
                ipaddress,
                vpcInstanceId);

        } else {
            // 创建实例连接对象
            custinsConnAddr = ConnAddrSupport
                .createCustinsConnAddr(connAddrCust, connPort, netType, -1, null, null, null, null);
        }
        VipResModel vipResModel = new VipResModel(netType);
        vipResModel.setVport(Integer.valueOf(connPort));
        vipResModel.setConnAddrCust(connAddrCust);
        if (netType.equals(CustinsSupport.NET_TYPE_PUBLIC)
            && connType.equals(CustinsSupport.CONN_TYPE_LVS)) {
            vipResModel.setMode(NetMode.FNAT);
        }
        vipResModel.setVpcId(custinsConnAddr.getVpcId());
        vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
        vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
        vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
        vipResModel.setVip(custinsConnAddr.getVip());

        return vipResModel;
    }


    public VipResModel initGeneralLogicInsVipResModel(CustInstanceDO logicIns, String connType)
            throws RdsException {

        Integer netType = getAndCheckNetType();

        String connPort = CustinsSupport.getConnPort(
                getParameterValue(ParamConstants.PORT), logicIns.getDbType(), logicIns.getKindCode());
        String connectionString = CheckUtils.checkNullForConnectionString(getParameterValue("GeneralGroupName"));

        String connAddrCust = getConnAddrCust(connectionString,
                getRegionIdByClusterName(logicIns.getClusterName()), logicIns.getDbType());
        CustinsConnAddrDO custinsConnAddr = null;
        if (CustinsSupport.isVpcNetType(netType)) {
            // 校验VPC网络类型实例的链路类型
            connType = CustinsSupport.getAndCheckConnTypeForVPC(connType, logicIns.getDbType());

            // VPC 实例， 必须传入tunnelid， vswitchid， ipaddress
            String tunnelId = CheckUtils.checkValidForTunnelId(getParameterValue(ParamConstants.TUNNEL_ID));
            String vpcId = CheckUtils.checkValidForVPCId(getParameterValue(ParamConstants.VPC_ID));
            String vswitchId = CheckUtils.checkValidForVswitchId(getParameterValue(ParamConstants.VSWITCH_ID));
            String ipaddress = CheckUtils.checkValidForIPAddress(getParameterValue("GeneralGroupVpcIp"));
            String vpcInstanceId = CheckUtils.checkValidForVpcInstanceId(getParameterValue("GeneralGroupVpcInstanceId"));

            custinsConnAddr = ConnAddrSupport.createCustinsConnAddr(connAddrCust, connPort,
                    netType, CustinsValidator.getRealNumber(tunnelId, -1),
                    vpcId,
                    vswitchId,
                    ipaddress,
                    vpcInstanceId);

        } else {
            // 创建实例连接对象
            custinsConnAddr = ConnAddrSupport
                    .createCustinsConnAddr(connAddrCust, connPort, netType, -1, null, null, null, null);
        }
        VipResModel vipResModel = new VipResModel(netType);
        vipResModel.setVport(Integer.valueOf(connPort));
        vipResModel.setConnAddrCust(connAddrCust);
        if (netType.equals(CustinsSupport.NET_TYPE_PUBLIC)
                && connType.equals(CustinsSupport.CONN_TYPE_LVS)) {
            vipResModel.setMode(NetMode.FNAT);
        }
        vipResModel.setVpcId(custinsConnAddr.getVpcId());
        vipResModel.setVpcInstanceId(custinsConnAddr.getVpcInstanceId());
        vipResModel.setTunnelId(custinsConnAddr.getTunnelId());
        vipResModel.setVswitchId(custinsConnAddr.getVswitchId());
        vipResModel.setVip(custinsConnAddr.getVip());

        return vipResModel;
    }

    /**
     * 获取NeedMaxscaleLink参数值
     *
     * @return True/False
     * @throws RdsException
     */
    public boolean getAndCheckNeedMaxscaleLink() throws RdsException {
        String needMaxscaleLink = getParameterValue(ParamConstants.NEED_MAXSCALE_LINK);
        if (StringUtils.isBlank(needMaxscaleLink)) {
            return false;
        }

        if (needMaxscaleLink.toLowerCase().equals("false")) {
            return false;
        }
        else if (needMaxscaleLink.toLowerCase().equals("true")) {
            return true;
        }
        else {
            throw new RdsException(ErrorCode.INVALID_NEED_MAXSCALE_LINK);
        }
    }

    public String transferZoneId(String zoneId) {
        return transferDisplay(DockerOnEcsConstants.DOCKER_FINANCE_ECS_ZONE_MAPPING, zoneId);
    }

    public String transferRegionId(String regionId) {
        return transferDisplay(DockerOnEcsConstants.DOCKER_FINANCE_ECS_REGION_MAPPING, regionId);
    }

    private String transferDisplay(String key, String value) {
        return resourceService.getRealValueByKeyAndDisplay(key, value);
    }

    public boolean isEcsBasicToDockerStandard(CustInstanceDO custins) throws RdsException{

        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if ( !isMysql57EcsBasic(custins, oldLevel)) {
            return false;
        }

        String levelCode = this.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
        InstanceLevelDO newLevel;

        if (Validator.isNull(levelCode)) {
            newLevel = oldLevel;
        } else {
            newLevel = instanceService
                .getInstanceLevelByClassCode(levelCode, custins.getDbType(), custins.getDbVersion(),
                    custins.getTypeChar(), null);
        }
        boolean newLevelOnDocker = isOnEcsDocker(newLevel);
        boolean newLevelStandard = newLevel.isStandardLevel();
        return newLevelOnDocker && newLevelStandard;
    }

    public boolean isClusterCategory(CustInstanceDO custins) throws RdsException {
        String levelCode = this.getParameterValue(TARGET_DB_INSTANCE_CLASS);
        if (StringUtils.isNotEmpty(levelCode)) {
            InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(levelCode, custins.getDbType(), custins.getDbVersion(), custins.getTypeChar(), null);
            return newLevel.isClusterLevel();
        }
        return false;

    }

    public boolean is80BasicToStandardPhysical(CustInstanceDO custins) throws RdsException {
        InstanceLevelDO oldLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
        if(!isMysql80Basic(custins, oldLevel)){
            return false;
        }
        String levelCode = this.getParameterValue(ParamConstants.TARGET_DB_INSTANCE_CLASS);
        InstanceLevelDO newLevel;
        if (Validator.isNull(levelCode)) {
            newLevel = oldLevel;
        } else {
            newLevel = instanceService
                .getInstanceLevelByClassCode(levelCode, custins.getDbType(), custins.getDbVersion(),
                    custins.getTypeChar(), null);
        }
        return isStandardPhyscial(newLevel);
    }

    /**
     * dbVersion=8.0,dbType=mysql,kindCode=3,instanceLevel.category=basic
     * */
    public boolean isMysql80Basic(CustInstanceDO custins, InstanceLevelDO oldLevel){
        return custins.isMysql80() && custins.isCustinsDockerOnEcs() && oldLevel.isBasicLevel();
    }

    public boolean isMysql57EcsBasic(CustInstanceDO custins, InstanceLevelDO oldLevel){
        return custins.isMysql57() && custins.isCustinsOnEcs() && oldLevel.isBasicLevel();
    }

    public boolean isStandardPhyscial(InstanceLevelDO levelDO){
        return levelDO.getHostType() == 0 && levelDO.isStandardLevel();
    }

    //判断是否是 ecs docker 形态规格
    public boolean isOnEcsDocker(InstanceLevelDO newLevel) throws RdsException{
        if (newLevel.getHostType() != 2){
            return false;
        }

        String extraInfo = newLevel.getExtraInfo();
        if (StringUtils.isBlank(extraInfo)){
            return false;
        }

        DockerInsLevelParseConfig config = null;
        try{
            config = custinsService.parseDockerInsExtraInfo(newLevel.getExtraInfo());
        }catch (RdsException ex){
            logger.error("judgeIsEcsOnDocker fail", ex);
            return false;
        }
        return DB_TYPE_DOCKER.equalsIgnoreCase(config.getContainerType());
    }

    /**
     * 资源评估是否支持 ecsbasic to dockerStandard
     * 当前业务: mysql 57
     *
     */
    public boolean supportBasicToDockerStandard(CustInstanceDO srcCustins) throws RdsException{

        Map<String,String> map = ActionParamsProvider.ACTION_PARAMS_MAP.get();

        // 1 暂不支持跨可用区, todo 跨可用区下vpc一致
        AVZInfo avzInfo = avzSupport.getAVZInfo(map);
        String region = avzSupport.getMainLocation(map);
        AVZInfo oldAvzInfo = avzSupport.getAVZInfoFromCustInstance(srcCustins);
        String oldRegion = oldAvzInfo.getMainLocation();

        if (!StringUtils.isBlank(region) && !region.equals(oldRegion)) {
            // 设置跨 Region 标记
            return false;
        }
        // 2 来源实例必须有vpc链路
        return checkLinkforEcsBasicToDocker(srcCustins.getId());
    }

    /**
     * check 链路
     * 1 包含vpc链路
     * 2 不能包含经典网络, 私网链路
     */
    public boolean checkLinkforEcsBasicToDocker(Integer custinsId) {
        List<CustinsConnAddrDO> normalConnections = connAddrCustinsService
            .getCustinsConnAddrByCustinsId(custinsId, null, CustinsSupport.RW_TYPE_NORMAL);
        boolean containsVpc = false;
        if (normalConnections == null || normalConnections.isEmpty()){
            //must contains vpc link
            return false;
        }else{
            for (CustinsConnAddrDO cutinsConnAddr : normalConnections){
                if (cutinsConnAddr.isVpcNetType()){
                    containsVpc = true;
                }else if(cutinsConnAddr.isPrivateNetType()){
                    // 不能包含私网
                    return false;
                }
            }
        }
        return containsVpc ;
    }

    /**
     * check ecs to docker 是否缩容
     */
    public void checkDiskReduction(Long oldSize, Long newSize) throws RdsException{
        if (oldSize == null||newSize == null){
            return ;
        }
        if (oldSize.compareTo(newSize) > 0){
            throw new RdsException(ErrorCode.INVALID_STORAGE);
        }
    }

    /**
     * 获得 target classcode hostType
     */
    public String getTargetHostType(String logicClassCode, String dbType, String dbVersion){
        InstanceLevelDO logicLevel = instanceService.getInstanceLevelByClassCode(logicClassCode, dbType, dbVersion,
            'x', CustinsSupport.CHARACTER_TYPE_LOGIC);
        List<InstanceLevelRelDO> instanceLevelRels =
            instanceService.getInstanceLevelRelByParentLevelId(logicLevel.getId(), null);
        InstanceLevelDO characterLevel = instanceService.getInstanceLevelByLevelId(
            instanceLevelRels.get(0).getCharacterLevelId());
        return characterLevel.getHostType().toString();
    }

    /**
     * 构造 ecs2docker trans list
     */
    public TransListDO buildMysql57Ecs2DockerTransListParam(TransListDO transList, CustInstanceDO srcCustins, CustInstanceDO tmpCustins,
                                                             String targetClassCode, Long newDiskSizeMB, Date utcDate){
        List<InstanceDO> tmpInstanceList = instanceService.getInstanceByParentCustinsId(tmpCustins.getId());
        List<InstanceDO> srcInstanceList = instanceService.getInstanceByCustinsId(srcCustins.getId());

        List<Map<String, Object>> transferHostIdMapList = new ArrayList<>(1);
        Map<String, Object> transferHostIdMap = new HashMap<>(2);
        transferHostIdMap.put("s_hid", tmpInstanceList.get(0).getHostId());
        transferHostIdMap.put("d_hid", srcInstanceList.get(0).getHostId());
        transferHostIdMapList.add(transferHostIdMap);
        Map<String, Object> translistParamMap = new HashMap<>(1);
        translistParamMap.put("TransferHostId", transferHostIdMapList);
        transList.setParameter(JSON.toJSONString(translistParamMap));

        transList.setsHinsid1(srcInstanceList.get(0).getHostId());
        transList.setdHinsid1(tmpInstanceList.get(0).getHostId());
        transList.setdHinsid2(tmpInstanceList.get(1).getHostId());

        InstanceLevelDO newLevel = instanceService.getInstanceLevelByClassCode(targetClassCode, srcCustins.getDbType(),
            srcCustins.getDbVersion(), srcCustins.getTypeChar(), "logic");

        transList.setdLevelid(newLevel.getId());

        transList.setsDisksize(srcCustins.getDiskSize());
        transList.setdDisksize(newDiskSizeMB);

        //utcDate可能为null，即不是按照时间点切换
        Date metadbSwitchTime = null;
        if(utcDate != null){
            String utcSwitchTimeStr = dtzSupport.transDate2UtcStr(utcDate);
            metadbSwitchTime = dtzSupport.transUtcStr2MetadbDate(utcSwitchTimeStr, DataSourceMap.DATA_SOURCE_DBAAS);
        }
        transList.setSwitchTime(metadbSwitchTime);

        return transList;
    }

    /**
     * 逻辑中不允许缩盘
     * */
    public Long getAndCheckDiskSize(Long curDiskSize) throws RdsException{
        Long diskSize = -1L;
        String storage = this.getParameterValue("Storage");
        if (StringUtils.isNotEmpty(storage)) {
            logger.error("storage parameter is " + storage);
            try {
                diskSize = Long.parseLong(storage);
            } catch (Exception e) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
            logger.error("curDiskSize is " + String.valueOf(curDiskSize));
            if ((1024 * diskSize) < curDiskSize) {
                throw new RdsException(ErrorCode.INVALID_STORAGE);
            }
        }
        return diskSize;
    }

    public boolean isNotModifyDiskSize(long curDiskSize, long targetDiskSize){
        return (( -1 == targetDiskSize) || ((1024 * targetDiskSize) == curDiskSize));
    }

    //放到通用support类中
    public long convertGB2MB(long diskSizeMB){
        return 1024L * diskSizeMB;
    }

    //获取集群
    public ClustersDO getAndCheckCluster(String clusterName) throws RdsException{

        ClustersQuery clustersQuery = new ClustersQuery();
        clustersQuery.setClusterName(clusterName);
        List<ClustersDO> clustersDOList = hostService.getClusters(clustersQuery);
        if(clustersDOList == null || clustersDOList.isEmpty()){
            throw new RdsException(ErrorCode.INVALID_DEDICATED_HOST_GROUP_ID);
        }
        return clustersDOList.get(0);
    }

    public InstanceLevelDO getPhyscialNewLevel(String newLogicClassCode, CustInstanceDO physcialCustins) throws RdsException{

        String newPhyscialClassCode = newLogicClassCode + ".physical";
        InstanceLevelDO newPhyscialLevel = instanceService.getInstanceLevelByClassCode(newPhyscialClassCode, physcialCustins.getDbType(), physcialCustins.getDbVersion(), physcialCustins.getTypeChar(), null);
        if(newPhyscialLevel == null){
            throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
        }
        return newPhyscialLevel;
    }

    public boolean isXdbEnterprise(CustInstanceDO custInstanceDO){
        InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(custInstanceDO.getLevelId());
        return DB_TYPE_MYSQL.equalsIgnoreCase(custInstanceDO.getDbType())
            && DB_VERSION_MYSQL_57.equalsIgnoreCase(custInstanceDO.getDbVersion())
            && InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevelDO.getCategory());
    }

    /**
     * 判断是否是缩容
     */
    public boolean judgeDiskReduction(Long oldSize, Long newSize){
        if (oldSize == null||newSize == null){
            return false;
        }
        if (oldSize.compareTo(newSize) > 0){
            return true;
        }
        return false;
    }

    public boolean isActiveOperation(String statusDesc){
        if (CustinsState.STATE_MAINTAINING.getComment().equals(statusDesc)){
            return true;
        }
        return false;
    }

    /**
     * 只读实例的主动运维, 使用 状态码 6, ACTIVATION
     * 备用只读的主动运维, 使用 状态码 6, INS_MAINTAINING
     * 主实例的主动运维, 使用 状态码 6, INS_MAINTAINING
     * 入参 statusDesc api请求中 DBInstanceStatusDesc 参数
     */
    public String getStatusDescForReadins(CustInstanceDO custins, String statusDesc){
        if (custins.isRead() && isActiveOperation(statusDesc)){
            return CustinsState.STATE_MAINTAIN_ACTIVITION.getComment();
        }
        return statusDesc;
    }

    //to #21218111, 云盘只读跨机备库重搭空指针问题
    public EngineCompose getRebuildEngineCompose(CustInstanceDO custins){
        EngineCompose engineCompose = null;
        String composeTag = selectComposeTag(custins.getClusterName());
        if (custins.isRead()){
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                custins.getDbType(),
                custins.getDbVersion(),
                insLevel.getCategory(), composeTag);
        }else{
            Integer parentId = custins.getParentId();
            CustInstanceDO logicCustins = custinsService.getCustInstanceByCustinsId(parentId);
            InstanceLevelDO insLevel = instanceService.getInstanceLevelByLevelId(logicCustins.getLevelId());
            engineCompose = custinsService.getEngineComposeByDbTypeAndDbVersionAndCategoryAndTag(
                logicCustins.getDbType(),
                logicCustins.getDbVersion(),
                insLevel.getCategory(), composeTag);
        }
        return engineCompose;
    }

    public Set<Integer> getAndCheckHostIdSetByDedicatedHostNames(String dedicatedHostNames, String clusterName) throws RdsException {
        Set<Integer> hostIdSet = new HashSet<Integer>();
        List<String> ecsInsIds = Arrays.asList(dedicatedHostNames.split(","));
        List<Map<String, Object>> hostIds = hostIDao.getHostIdsByDedicatedHostNames(ecsInsIds, clusterName);
        if (hostIds.size() != ecsInsIds.size()) {
            // 不支持跨集群
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }
        for (Map<String, Object> hostMap : hostIds) {
            hostIdSet.add(CustinsValidator.getRealNumber(hostMap.get("id").toString().trim()));
        }
        return hostIdSet;
    }
    public Set<Integer> getAndCheckHostIdSetOnlyByDedicatedHostNames(String dedicatedHostNames) throws RdsException {
        Set<Integer> hostIdSet = new HashSet<Integer>();
        List<String> dedicatedHostNameList = Arrays.asList(dedicatedHostNames.split(","));
        List<Map<String, Object>> hostIds = hostIDao.getHostIdsOnlyByDedicatedHostNames(dedicatedHostNameList);
        if (hostIds.size() != dedicatedHostNameList.size()) {
            // not support cross cluster
            throw new RdsException(ErrorCode.UNSUPPORTED_CLUSTER_NOT_SUPPORT);
        }
        for (Map<String, Object> hostMap : hostIds) {
            hostIdSet.add(CustinsValidator.getRealNumber(hostMap.get("id").toString().trim()));
        }
        return hostIdSet;
    }



    public CustInstanceDO getPhyscialCustinsByParentId(Integer parentCustinsId) {

        List<CustInstanceDO> physcialCustinsList = custinsService.getCustInstanceByParentId(parentCustinsId);
        if (physcialCustinsList == null || physcialCustinsList.size() <= 0) {
            return null;
        }
        return physcialCustinsList.get(0);
    }
    public boolean checkCloneBeforeUpgrade(Integer custinsId) throws RdsException {

        Date restoreTime = checkService.getAndCheckTimeByDateStr(
                this.getParameterValue(ParamConstants.RESTORE_TIME),
                DateUTCFormat.SECOND_UTC_FORMAT, ErrorCode.INVALID_RESTORE_TIME);
        // 5.6 升级到5.7 后，在下发实例克隆到时间点，克隆的时间不能晚于升级成功时间.
        long times = restoreTime.getTime();
        List<Integer> custinsIdList = new ArrayList<>();
        List<String> paramList = new ArrayList<>();
        custinsIdList.add(custinsId);
        paramList.add("upgrade_time");
        List<CustinsParamDO> cinsParamList = custinsParamService.getCustinsParamsByCustinsIds(custinsIdList, paramList);
        if (cinsParamList != null && cinsParamList.size() == 1) {
            CustinsParamDO cinsParamDO = cinsParamList.get(0);
            long upgradeTimeMillis = Long.parseLong(cinsParamDO.getValue());
            return times < upgradeTimeMillis ;
//          throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
        }
        return true;

    }

    public TransListDO getDockerOnEcsLocalSSDTrans(CustInstanceDO custins, CustInstanceDO physicalCustins) throws Exception {
        BakhistoryDO history = null;
        Long realBacksetSize = null;

        TransListDO trans = new TransListDO(physicalCustins, TRANS_STATUS_RECOVER_NB, TRANS_TYPE_RECOVER);
        Map<String, Object> translistParamMap = new HashMap<>(8);

        trans.setsCinsReserved(1);
        List<Integer> insIds = custinsService.getInstanceIdsByCustinsId(physicalCustins.getId());
        trans.setsHinsid1(insIds.get(0));
        if (insIds.size() > 1) {
            trans.setsHinsid2(insIds.get(1));
        }

        /**
         * trans_list param_map set up
         */
        String restoreType = this.getAndCheckRestoreType();
        translistParamMap.put("restoreType", restoreType);

        if (RESTORE_TYPE_TIME.equals(restoreType)) {
            if (!this.checkCloneBeforeUpgrade(custins.getId())){
                throw new RdsException(ErrorCode.INVALID_RESTORE_TIME);
            }
        }

        if (RESTORE_TYPE_BAKID.equals(restoreType)) {
            // 根据备份集Id查询备份集大小
            Long bakId = CheckUtils.parseLong(this.getParameterValue("BackupSetID"), null,
                    null, ErrorCode.BACKUPSET_NOT_FOUND);
            getAndCheckBakhistory(custins, bakId);
            history = bakService.getBakhistoryByBackupSetId(custins.getId(), bakId);
            realBacksetSize = history.getBaksetSize();
            trans.setBakhisId(bakId);

        } else if (RESTORE_TYPE_TIME.equals(restoreType)) {
            // 根据备份时间点查询备份集大小
            DateTime restoreTimeUTC = this.validRestoreByTimeSafe(custins);
            //转化为Bak库时间进行比较
            Date restoreTime = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_BAK);
            history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, custins.getKindCode().equals(KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD)?BAKWAY_XTRABACKUP:BAKWAY_SNAPSHOT, BAKTYPE_FULL);
            realBacksetSize = history.getBaksetSize();

            trans.setIsBaseTime(1);
            //写入dbaas库，使用dbaas库时间
            Date recoverTimeDbaas = dtzSupport.getSpecificTimeZoneDate(restoreTimeUTC, DATA_SOURCE_DBAAS);
            trans.setRecoverTime(recoverTimeDbaas);

        } else if (RESTORE_TYPE_LASTEST.equals(restoreType)){
            // 克隆到最新时间
            DateTime nowUTC = DateTime.now(DateTimeZone.UTC);
            Date nowTime = dtzSupport.getSpecificTimeZoneDate(nowUTC, DATA_SOURCE_BAK);
            history = bakService.getBakhistoryByRecoverTime(custins.getId(), nowTime, BAKWAY_XTRABACKUP, BAKTYPE_FULL);
            realBacksetSize = history.getBaksetSize();
            trans.setIsBaseTime(0);
        } else {
            CheckBaksetDO checkBakset = custinsService.validRestoreByUser(custins,
                    this.getParameterValue(ParamConstants.DOWNLOAD_URL),
                    this.getParameterValue(ParamConstants.BAKSET_NAME),
                    this.getParameterValue(ParamConstants.CHECKSUM));
            Date restoreTime = checkBakset.getGmtCreated();
            history = bakService.getBakhistoryByRecoverTime(custins.getId(), restoreTime, BAKWAY_XTRABACKUP,
                    BAKTYPE_FULL);
            realBacksetSize = history.getBaksetSize();

            translistParamMap.put("downloadUrl", checkBakset.getDownloadUrl());
            translistParamMap.put("baksetName", checkBakset.getName());
        }

        String storage = this.getParameterValue("storage");
        if (storage != null) {
            //库表恢复场景下，检验用户传递的storage大小是否大于需要的空间
            if (Long.parseLong(storage) * CustinsSupport.GB_TO_KB < realBacksetSize ){
                throw new RdsException(ErrorCode.UNSUPPORTED_REDUCE_DISK_SIZE);
            }
        }

        trans.setParameter(JSON.toJSONString(translistParamMap));

        return trans;
    }

    public DateTime validRestoreByTimeSafe(CustInstanceDO custins) throws RdsException {
        if (custins.isMysql() || custins.isSqlserver()) {
            LogPlanDO logPlan = this.bakService.getLogPlanByCustinsId(custins.getId());
            if (!custins.isMysql() || logPlan != null && logPlan.isEnableBackupLog()) {
                DateTime restoreTimeUTC = this.dtzSupport.getUTCDateByDateStr(this.getParameterValue("RestoreTime"));
                this.checkService.checkRestoreTimeValidSafe(custins, restoreTimeUTC, logPlan);
                return restoreTimeUTC;
            } else {
                throw new RdsException(ErrorCode.UNSUPPORTED_BACKUP_LOG_STATUS);
            }
        } else {
            throw new RdsException(ErrorCode.UNSUPPORTED_ENGINE);
        }
    }

    public String getCfgDbVersion(CustInstanceDO custins) throws RdsException {
        return instanceService.getCfgDbVersion(custins, null);
    }

    public boolean isMysqlXDBEnterprise(CustInstanceDO custins, InstanceLevelDO instanceLevelDO){
        return DB_TYPE_MYSQL.equalsIgnoreCase(custins.getDbType()) && DB_VERSION_MYSQL_57.equalsIgnoreCase(custins.getDbVersion())
            && CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevelDO.getCategory());
    }

    public Map<String, Object> getAndCheckParameters() throws RdsException {
        Map<String, Object> parameterMap = null;
        String parameters = getParameterValue("Parameters");
        if (Validator.isNull(parameters)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
        try {
            parameterMap = JSON.parseObject(parameters);
        } catch (Exception e) {
            logger.error(parameters, e);
        }
        if (parameterMap == null || parameterMap.size() == 0) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }
        return parameterMap;
    }

    public Map getAndCheckParameterGroupId(ReplicaSet replicaSet) throws RdsException {
        String parameterGroupId = getParameterValue("ParameterGroupId");
        if (Validator.isNull(parameterGroupId)) {
            throw new RdsException(ErrorCode.INVALID_PARAMETERS);
        }

        return parameterGroupTemplateGenerator.paramGroupMatchValidate(
                replicaSet.getService(),
                replicaSet.getServiceVersion(),
                replicaSet.getCategory(),
                parameterGroupId, false
        );
    }

    public Boolean checkAccountIsReserved(String dbVersion, String accountName) {
        //如果使用了rds内部保留账号，则不创建
        if (DB_VERSION_MYSQL_57.compareTo(dbVersion) <= 0 && Pattern.compile(REGEX_MYSQL_GTE_57_RESERVED_ACCOUNTS).matcher(accountName).matches()) {
            return true;
        }
        else if(Pattern.compile(REGEX_MYSQL_LT_57_RESERVED_ACCOUNTS).matcher(accountName).matches()) {
            return true;
        }
        return false;
    }


    public String getAndCheckSuperAccountPassword() throws RdsException {
        try {
            return CheckUtils.checkValidForAccountPassword(AesCfb.decrypt(getParameterValue(ENCRYPT_SUPER_ACCOUNT_PASSWORD)));
        } catch (Exception e) {
            String customizedErrorDesc = "fail to decrypt password";
            throw new RdsException(ErrorCode.INTERNAL_FAILURE, customizedErrorDesc);
        }
    }


    public String selectComposeTag(String clusterName) {
        ClustersDO cluster = clusterService.getClusterByClusterName(clusterName);
        if(cluster == null){
            return COMPOSE_TAG_ALIOS;
        }
        boolean isUserCluster = cluster.getType().equals(DEDICATED_HOST_GOURP_TYPE);
        return selectComposeTag(clusterName, isUserCluster);
    }

    public String selectComposeTag(String clusterName, boolean isUserCluster) {
        if(!isUserCluster){
            return COMPOSE_TAG_ALIOS;
        }
        ClusterParamDO clusterParam = clusterService.getClusterParam(null, null, clusterName,
                CLUSTER_PARAM_VALUE_OPEN_HOST_PERMISSION);
        if(clusterParam != null){
            try{
                int i = Integer.parseInt(clusterParam.getValue());
                if(i>0){
                    return COMPOSE_TAG_ALIYUN;
                }
            }catch (Exception e){
                logger.error("The OPEN_HOST_PERMISSION is invalid. value=" + clusterParam.getValue());
            }
        }
        return COMPOSE_TAG_ALIOS;
    }

    enum CustinsLockMode {
        MANUAL_LOCK(CustinsSupport.CUSTINS_LOCK_YES, 5),
        AUTO_LOCK(CustinsSupport.CUSTINS_LOCK_YES_AUTO, 4),
        BEFORE_RECOVER_LOCK(CustinsSupport.CUSTINS_LOCK_BEFORE_RECOVER, 3),
        DISK_FULL_LOCK(CustinsSupport.CUSTINS_LOCK_DISK_FULL, 2),
        READINS_DISK_FULL_LOCK(CustinsSupport.CUSTINS_LOCK_READINS_DISK_FULL, 2),
        USER_LOCK_WRITE(CustinsSupport.CUSTINS_LOCK_WRITE_BY_USER, 1), //用户锁
        USER_LOCK_READ_WRITE(CustinsSupport.CUSTINS_LOCK_READ_WRITE_BY_USER, 1),
        NO_LOCK(CustinsSupport.CUSTINS_LOCK_NO, 0);

        private int mode;
        private int priority;

        CustinsLockMode(int mode, int priority) {
            this.mode = mode;
            this.priority = priority;
        }

        public int getMode() {
            return this.mode;
        }

        public int getPriority() {
            return this.priority;
        }

        public static CustinsLockMode valueOf(Integer lockMode) {
            if (lockMode == null) return null;

            if (MANUAL_LOCK.getMode() == lockMode) {
                return MANUAL_LOCK;
            } else if (AUTO_LOCK.getMode() == lockMode) {
                return AUTO_LOCK;
            } else if (DISK_FULL_LOCK.getMode() == lockMode) {
                return DISK_FULL_LOCK;
            } else if (READINS_DISK_FULL_LOCK.getMode() == lockMode) {
                return READINS_DISK_FULL_LOCK;
            } else if (BEFORE_RECOVER_LOCK.getMode() == lockMode) {
                return BEFORE_RECOVER_LOCK;
            } else if (NO_LOCK.getMode() == lockMode) {
                return NO_LOCK;
            } else if (USER_LOCK_WRITE.getMode() == lockMode) {
                return USER_LOCK_WRITE;
            } else if (USER_LOCK_READ_WRITE.getMode() == lockMode) {
                return USER_LOCK_READ_WRITE;
            } else {
                // Unknown lock mode
                return null;
            }
        }

    }
    /**
     * Check the conditions of locking. targetLockMode priority must be greater than current one.
     *
     * @param targetLockMode The mode that we want to set.
     * @param custins
     * @return True if the conditions are met.
     */
    public static boolean checkLockPrecondition(int targetLockMode, CustInstanceDO custins) {
        CustinsLockMode targetCustinsLockMode = CustinsLockMode.valueOf(targetLockMode);
        CustinsLockMode curCustinsLockMode = CustinsLockMode.valueOf(custins.getLockMode());

        if (targetCustinsLockMode == null || curCustinsLockMode == null) {
            // We never define these locks priority, return True for compatibility.
            return true;
        }

        return targetCustinsLockMode.getPriority() > curCustinsLockMode.getPriority();
    }

    /**
     * Check the conditions of unlocking. expectedUnlockMode must be equal to current one.
     *
     * @param expectedUnlockMode
     * @param custins
     * @return
     */
    public static boolean checkUnlockPrecondition(int expectedUnlockMode, CustInstanceDO custins) {
        CustinsLockMode expectedCustinsUnlockMode = CustinsLockMode.valueOf(expectedUnlockMode);
        CustinsLockMode curCustinsLockMode = CustinsLockMode.valueOf(custins.getLockMode());

        if (expectedCustinsUnlockMode == null || curCustinsLockMode == null) {
            // We never define these locks priority, return True for compatibility.
            return true;
        }

        return curCustinsLockMode != CustinsLockMode.NO_LOCK
                && expectedCustinsUnlockMode == curCustinsLockMode;
    }

    /**
     * dns 链路是否打开磁盘锁检查 true:检查, false: 不检查
     */
    public boolean checkDiskFullStatusForDnsConn(){
        try{
            ResourceDO resourceDO = resourceService.getResourceByResKey("DNS_CHECK_DISK_FULL_STATUS");
            if(resourceDO != null &&  resourceDO.getRealValue().equals("1")){
                return true;
            }
        }catch (Exception e){
            logger.error("not register reskey");
        }
        return false;
    }

    public boolean isMysqlXDB(CustInstanceDO custins) throws RdsException{

        String dbType = custins.getDbType();
        String dbVersion = custins.getDbVersion();

        if(CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(dbType) &&
            (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(dbVersion) ||
                CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(dbVersion))){
            InstanceLevelDO instanceLevel = instanceService.getInstanceLevelByLevelId(custins.getLevelId());
            if(instanceLevel == null){
                throw new RdsException(ErrorCode.INVALID_DBINSTANCECLASS);
            }
            return InstanceSupport.CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevel.getCategory());
        }

        return false;
    }

    public boolean isPolarxHatp(Integer custinsId){
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custinsId, CUSTINS_PARAM_NAME_IS_POLARX_HATP);
        return custinsParamDO != null && CUSTINS_PARAM_VALUE_IS_POLARX_HATP_YES.equalsIgnoreCase(custinsParamDO.getValue());
    }

    public boolean isXDBReadins(CustInstanceDO custins){
        int insType = custins.getInsType().intValue();
        if(insType == CustInsType.CUST_INS_TYPE_READ.getValue() || insType == CustInsType.CUST_INS_TYPE_READ_BACKUP.getValue()){

            CustInstanceDO primaryCustins = custinsService.getCustInstanceByCustinsId(custins.getPrimaryCustinsId());
            //XDB只读的临时实例，其主实例是只读，还需要转化下
            if(custins.getIsTmp().intValue() == 1){
                primaryCustins = custinsService.getCustInstanceByCustinsId(primaryCustins.getPrimaryCustinsId());
            }
            if(primaryCustins != null){
                InstanceLevelDO instanceLevelDO = instanceService.getInstanceLevelByLevelId(primaryCustins.getLevelId());
                return instanceLevelDO != null && CATEGORY_ENTERPRISE.equalsIgnoreCase(instanceLevelDO.getCategory());
            }
        }
        return false;
    }

    public boolean isMysqlXDBByLevel(InstanceLevelDO instanceLevel){
        return InstanceSupport.CATEGORY_ENTERPRISE.equals(instanceLevel.getCategory())
            && CustinsSupport.DB_TYPE_MYSQL.equalsIgnoreCase(instanceLevel.getDbType())
            && (CustinsSupport.DB_VERSION_MYSQL_57.equalsIgnoreCase(instanceLevel.getDbVersion()) ||
            CustinsSupport.DB_VERSION_MYSQL_80.equalsIgnoreCase(instanceLevel.getDbVersion()));
    }

    public boolean isMysqlGeneral(Integer instanceLevelId) {
        InstanceLevelDO level = instanceService.getInstanceLevelByLevelId(instanceLevelId);
        return "general".equalsIgnoreCase(level.getCategory());
    }

    public static void main(String[] args) {
        Map<String, String> o = JSON.parseObject("{\"a\":\"b\",\"c\":\"d\"}").toJavaObject(new TypeReference<Map<String, String>>() {
        });
        System.out.println(o);
    }

    @Autowired
    private MysqlParameterHelper mysqlParaHelper;

    @Autowired
    protected EcsService ecsService;

    /*
     * CloneMode:
     * - quick: 直接在当前实例上复制一份配置，配置全部从源实例上copy
     * - default: 默认的Clone方式，兼容已有的方式
     */
    public static final String CLONE_MODE = "clonemode";
    private static final String DEFAULT_MODE = "default";
	public static final String QUICK_MODE = "quick";
	private static final String SOURCE_DB_INSTANCE_NAME = "sourcedbinstancename";
	private static final String DB_INSTANCE_NAME = "dbinstancename";


	public Map<String, String> buildShortcutCloneParams(CustInstanceDO custins, Map<String, String> actionParams) throws RdsException {
    	/*
    	 * 上游会传下来的参数有:
    	 * Engine: MySQL
    	 * DBInstanceName: 源实例ID，kind_code=3时应该是logic节点，必传
    	 * User_ID
    	 * UID
    	 * OperatorID: 工号，必填

    	 * ConnectionString: 必填，rm-bp15m977vk0063x43-clone1，应该查询某个ID是否存在，
    	 *                   后缀cloneX中的X编号需要查询当前是否有这个实例ID，
    	 *                   如果有冲突就报错，没有冲突就继续.
    	 * RestoreType: 8 恢复到当前时间(8), 恢复到指定时间(0)， 选填
    	 * RestoreTime: 2021-04-14T08:00:00Z， 选填
    	 * DBTimeZone: +8:00，选填
    	 * BackupRetentionPeriod: 7, 选填
    	 * DBInstanceType: x, 选填
    	 * PreferredBackupPeriod: 0101010, 选填
    	 * AutoUpgradeMinorVersion: Manual，选填
    	 * IPAddress: 必填
    	 * Port: 3306, 可选
    	 * SecurityIPList: 127.0.0.1, 选填
    	 * Storage: 200, 必传
    	 * DispenseMode: 0，选填
    	 * ServiceType: 0, 选填
    	 * HostType: 0, 选填
    	 * OptmizationService: 1，选填
    	 * DBInstanceNetType: 2， 选填
    	 * PreferredBackupTime: 15:45Z，选填
    	 * AutoUpgradeMinorVersion: Manual
    	 * DBInstanceClass: rds.mysql.s2.large
    	 * PreferredBackupPeriod: 0101010
    	 * CharacterSetName: utf8
    	 * AccountType: 0
    	 * DBIsIgnoreCase: true
    	 *
    	 * 需要主动构造的参数列表
    	 * Region: cn-hangzhou-h-aliyun
    	 * regionId: cn-hangzhou
    	 * ZoneId: cn-hangzhou-h


    	 * EngineVersion: 5.7 - Done
    	 * DBParamGroupID: rpg-sys-**************

    	 * MultiAVZExParam: {"availableZoneInfoList":[{"isUserSpecified":true,"region":"cn-hangzhou-h-aliyun","role":"master","vSwitchID":"vsw-bp16b36b0osdq8dq6jm5q","zoneID":"cn-hangzhou-h"}]}

    	 * IsUserVpc: true
    	 * VPCId: vpc-bp1mflvzsfbqcjm4v65kg - Done
    	 * TunnelId: 40439 - Done
    	 * VSwitchId: vsw-bp16b36b0osdq8dq6jm5q Done
    	 */

		String cloneMode = actionParams.getOrDefault(CLONE_MODE, DEFAULT_MODE);


		if (QUICK_MODE.equalsIgnoreCase(cloneMode)) {
			// 1. 构造 SourceDBInstanceName, DBInstanceName
			// 快捷模式下杜康下发下来传的只有当前要克隆的实例 SourceDBInstanceName = DBInstanceName
			String srcCustinsName = actionParams.getOrDefault(SOURCE_DB_INSTANCE_NAME, actionParams.get(DB_INSTANCE_NAME));
			actionParams.put(SOURCE_DB_INSTANCE_NAME, srcCustinsName);

			String targetCustinsName = srcCustinsName;
			// FIXME: 写死了只探索10000次快速克隆，感觉应该用不了这么多.
			for (int i = 1; i < 10000; i++) {
				targetCustinsName = String.format("%s-clone%d", srcCustinsName, i);
				List<Map<String, Object>> existCustins = custinsService.getCustinsByInsName(targetCustinsName);
				// 检查当前是否已经存在这个实例了
				if (existCustins == null || existCustins.size() <= 0) {
					break;
				}
			}
			actionParams.put("connectionstring", targetCustinsName);
			actionParams.put(DB_INSTANCE_NAME, targetCustinsName);

			/*
			// 2. Region, regionId, ZoneId, 看起来Region不是必传的，先不管试试看
			String regionId = custins.getRegionId();
			logger.info(String.format("buildShortcutCloneParams: regionId=%s", regionId));
			actionParams.put("regionid", regionId);
			String mvzParams = JSON.toJSONString(custins.getMultiAVZExParams());
			logger.info(String.format("buildShortcutCloneParams: multiavzexparam=%s", mvzParams));
			actionParams.put("multiavzexparam", mvzParams);

			// 3. EngineVersion
			actionParams.put("engineversion", custins.getDbVersion());
			logger.info(String.format("buildShortcutCloneParams: engineversion=%s", custins.getDbVersion()));

			List<CustinsConnAddrDO> custinsConnAddrList = connAddrCustinsService
					.getCustinsConnAddrByCustinsId(custins.getId(), null, CustinsSupport.RW_TYPE_NORMAL);

			CustinsConnAddrDO vpcConnAddr = ConnAddrSupport.getVPCConnAddr(custinsConnAddrList);
			Integer tunnelId = vpcConnAddr.getTunnelId();
			actionParams.put("tunnelid", String.valueOf(tunnelId));
			String vpcId = vpcConnAddr.getVpcId();
			actionParams.put("vpcid", vpcId);
			String vswitchId = vpcConnAddr.getVswitchId();
			actionParams.put("vswitchid", vswitchId);
			*/
		}

		return actionParams;
    }

    /**
     * 过滤连接数参数
     */
    public void filterMaxConnParams(List<Map<String, Object>> oriParams) {
        if (null == oriParams || 0 == oriParams.size()) {
            return;
        }
        ListIterator<Map<String, Object>> iterator = oriParams.listIterator();
        while (iterator.hasNext()) {
            Map<String, Object> tempConfig = iterator.next();
            if (tempConfig.get("ParameterName").equals("max_connections") || tempConfig.get("ParameterName").equals("max_user_connections")) {
                iterator.remove();
                logger.info("filter max conn parameters success");
            }
        }
    }

    /**
     * 老架构连接数展示和修改的开关
     * 默认未配置就是不开放
     */
    public boolean isFilterMaxConn() {
        ResourceDO resourceDO = resourceService.getResourceByResKey("MYSQL_NO_FILTER_MAX_CONN_PARAMS");
        return null == resourceDO || !StringUtils.equalsIgnoreCase(resourceDO.getRealValue(),"true");
    }

    public void filterTemplteParams(List<Map<String, Object>> templateList, Set<String> filterPrams) {
        if (null == templateList || 0 == templateList.size()) {
            return;
        }
        templateList.removeIf(template -> filterPrams.contains(template.get("ParameterName")));
    }

    /**
     * 过滤分析型只读实例参数 - 只保留分析型相关参数
     */
    public void filterAnalyticReadOnlyParams(List<Map<String, Object>> oriParams) {
        if (CollectionUtils.isEmpty(oriParams)) {
            return;
        }
        ListIterator<Map<String, Object>> iterator = oriParams.listIterator();
        while (iterator.hasNext()) {
            Map<String, Object> tempConfig = iterator.next();
            String paramName = (String) tempConfig.get("ParameterName");
            if (!isAnalyticReadOnlyParam(paramName)) {
                iterator.remove();
                logger.info("filter non-analytic parameter: " + paramName);
            }
        }
    }

    /**
     * 过滤非分析型只读实例参数 - 移除分析型相关参数
     */
    public void filterNonAnalyticReadOnlyParams(List<Map<String, Object>> oriParams) {
        if (CollectionUtils.isEmpty(oriParams)) {
            return;
        }
        ListIterator<Map<String, Object>> iterator = oriParams.listIterator();
        while (iterator.hasNext()) {
            Map<String, Object> tempConfig = iterator.next();
            String paramName = (String) tempConfig.get("ParameterName");
            if (isAnalyticReadOnlyParam(paramName)) {
                iterator.remove();
                logger.info("filter analytic parameter: " + paramName);
            }
        }
    }

    /**
     * 分析型只读实例参数排序 - 将 DuckDB 参数排在最前面
     */
    public void sortAnalyticReadOnlyParams(List<Map<String, Object>> oriParams) {
        if (CollectionUtils.isEmpty(oriParams)) {
            logger.info("Parameter list is empty, skipping sort");
            return;
        }

        logger.info("Starting to sort " + oriParams.size() + " parameters");

        // 分离 DuckDB 参数和其他参数
        List<Map<String, Object>> duckdbParams = new ArrayList<>();
        List<Map<String, Object>> otherParams = new ArrayList<>();

        for (Map<String, Object> param : oriParams) {
            String paramName = (String) param.get("ParameterName");
            if (isAnalyticReadOnlyParam(paramName)) {
                duckdbParams.add(param);
                logger.info("Found DuckDB parameter: " + paramName);
            } else {
                otherParams.add(param);
            }
        }

        // 清空原列表，先添加 DuckDB 参数，再添加其他参数
        oriParams.clear();
        oriParams.addAll(duckdbParams);
        oriParams.addAll(otherParams);

        logger.info("sorted " + duckdbParams.size() + " DuckDB parameters to front, " + otherParams.size() + " other parameters");
    }

    private static final String LOOSE_ = "loose_";

    /**
     * 判断参数是否为分析型只读实例相关参数
     * 支持 loose_ 前缀的参数
     */
    private boolean isAnalyticReadOnlyParam(String paramName) {
        if (paramName == null) {
            return false;
        }

        String normalizedParamName = paramName;
        if (paramName.startsWith(LOOSE_)) {
            normalizedParamName = normalizedParamName.replace(LOOSE_, "");
            logger.info("Normalized parameter name from " + paramName + " to " + normalizedParamName);
        }

        boolean isAnalytic = MySQLParamConstants.ANALYTIC_READ_ONLY_PARAMS.contains(normalizedParamName);
        logger.info("Parameter " + paramName + " (normalized: " + normalizedParamName + ") is analytic: " + isAnalytic);

        return isAnalytic;
    }

    /*
     * 如果用户修改sync_binlog，需要额外记录到custins_param的标记当中
     */
    public void checkAddUserSyncBinlogCustinsParam(Map<String, Object> parameterMap, Integer custinsId) {
        if (parameterMap.containsKey("sync_binlog") || parameterMap.containsKey("innodb_flush_log_at_trx_commit")) {
            custinsParamService.setCustinsParam(custinsId, custinsParamUserSyncBinlogValueKey, String.valueOf(parameterMap.get("sync_binlog")));
        }
    }

    public boolean isSupportModifySSLDynamic(CustInstanceDO custins, boolean sslUpdate) {
        if (!sslUpdate) {
            return false;
        }
        if (!custins.isMysql80()) {
            return false;
        }
//        ResourceDO resourceDO = resourceService.getResourceByResKey("MYSQL_NO_FILTER_MODIFY_SSL_DYNAMIC");
//        if (null == resourceDO || !StringUtils.equalsIgnoreCase(resourceDO.getRealValue(),"true")) {
//            return false;
//        }
        Integer minorVersion = mysqlParamMinorVerionHelper.getNumMinorVersionByCustinsId(custins.getId());
        Integer minSatisfiedMinorVersion = 20201031;
        return minorVersion >= minSatisfiedMinorVersion;
    }





    public boolean isInstanceLevelSupportInnerRDS(String instanceLevelExtraInfo) {
        if (StringUtils.isNotEmpty(instanceLevelExtraInfo)) {
            try {
                JSONObject extraInfo = JSON.parseObject(instanceLevelExtraInfo);
                final String isUsedForInnerRDS = extraInfo.getString("isUsedForInnerRDS");
                if ("1".equalsIgnoreCase(isUsedForInnerRDS)) {
                    return true;
                }
            } catch (JSONException e) {
                logger.warn("parse extra_info error", e);
            }
        }
        return false;
    }

    public boolean isInnerRDS(Integer custinsId){
        CustinsParamDO custinsParamDO = custinsParamService.getCustinsParam(custinsId, CustinsParamSupport.CUSTINS_PARAM_IS_INNER_RDS);
        return custinsParamDO != null && "1".equalsIgnoreCase(custinsParamDO.getValue());
    }

    /**
     *  查看是否为原生复制实例
     */
    public boolean isExternalReplication(Integer custinsId){
        CustinsParamDO externalReplicationParam = custinsParamService.getCustinsParam(custinsId, "externalReplication");
        if (!Objects.isNull(externalReplicationParam)) {
            String mode = externalReplicationParam.getValue();
            return mode.equalsIgnoreCase("on");
        }
        return false;
    }

}
