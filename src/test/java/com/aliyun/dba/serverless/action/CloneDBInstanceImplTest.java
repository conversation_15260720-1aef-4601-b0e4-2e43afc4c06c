package com.aliyun.dba.serverless.action;

import com.aliyun.apsaradb.activityprovider.model.GeneralCloudDisk;
import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.GdnInstanceService;
import com.aliyun.dba.base.service.MysqlEncryptionService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.CustinsSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.serverless.action.service.ServerlessCreateReadDBInstanceService;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.CheckUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.inject.Inject;

import static com.aliyun.dba.bak.support.BakSupport.RESTORE_TYPE_BAKID;
import static com.aliyun.dba.custins.support.CustinsSupport.KIND_CODE_NEW_ARCH;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2024/11/13 19:24
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({CloneDBInstanceImpl.class, CustinsSupport.class, CheckUtils.class, PodParameterHelper.class})
public class CloneDBInstanceImplTest {
    private CloneDBInstanceImpl cloneDBInstanceImpl;
    private CustInstanceDO custInstanceDO;
    protected MysqlParamSupport mysqlParamSupport;
    private MysqlParamSupport paramSupport;
    private AliyunInstanceDependency dependency;
    private DBaasMetaService metaService;
    private PodParameterHelper podParameterHelper;
    private PodAvzSupport podAvzSupport;
    private MysqlParameterHelper mysqlParameterHelper;

    private ReplicaSetService replicaSetService;
    private GdnInstanceService gdnInstanceService;
    private PodCommonSupport podCommonSupport;
    private MysqlEncryptionService mysqlEncryptionService;
    private CustinsService custinsService;
    private CustinsParamService custinsParamService;
    private DefaultApi defaultApi;
    private MinorVersionServiceHelper minorVersionServiceHelper;
    private ServerlessResourceService serverlessResourceService;
    private RundPodSupport rundPodSupport;
    private BakService bakService;
    private BackupService backupService;
    private KmsService kmsService;
    private PodTemplateHelper podTemplateHelper;

    @Before
    public void setUp() throws Exception {
        cloneDBInstanceImpl = PowerMockito.spy(new CloneDBInstanceImpl());
        custInstanceDO = new CustInstanceDO();
        custInstanceDO.setId(0);

        serverlessResourceService = PowerMockito.mock(ServerlessResourceService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "serverlessResourceService", serverlessResourceService);

        mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "mysqlParamSupport", mysqlParamSupport);
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), any(), any())).thenReturn("200");
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), eq("RequestId"))).thenReturn("123456");
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.RESTORE_TYPE))).thenReturn(RESTORE_TYPE_BAKID);
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), eq("PreferredBackupTime"))).thenReturn("06:35Z");
        PowerMockito.when(mysqlParamSupport.getAndCheckSourceDBInstanceName(any())).thenReturn("sourceDBInstanceName");

        mysqlParameterHelper = PowerMockito.mock(MysqlParameterHelper.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "mysqlParameterHelper", mysqlParameterHelper);
        PowerMockito.when(mysqlParameterHelper.getAndCreateUserId()).thenReturn(1);
        PowerMockito.when(mysqlParameterHelper.getAndCheckSourceCustInstance()).thenReturn(custInstanceDO);

        metaService = PowerMockito.mock(DBaasMetaService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "dBaasMetaService", metaService);

        podParameterHelper = PowerMockito.mock(PodParameterHelper.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "podParameterHelper", podParameterHelper);
        PowerMockito.when(podParameterHelper.getDiskType(any())).thenReturn("cloud_essd");
        PowerMockito.when(podParameterHelper.getParameterValue(eq(ParamConstants.UID))).thenReturn("1232313");

        podTemplateHelper = PowerMockito.mock(PodTemplateHelper.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "podTemplateHelper", podTemplateHelper);
        Pair<String, ScheduleTemplate> scheduleTemplatePair = Pair.of("1232313", new ScheduleTemplate());
        PowerMockito.when(podTemplateHelper.getBizSysScheduleTemplate(
            any(PodType.class),
            any(ReplicaSet.BizTypeEnum.class),
            anyString(),
            any(InstanceLevel.class),
            anyBoolean(),
            anyString(),
            anyString(),
            anyString(),
            anyString())).thenReturn(scheduleTemplatePair);

        minorVersionServiceHelper = PowerMockito.mock(MinorVersionServiceHelper.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "minorVersionServiceHelper", minorVersionServiceHelper);

        bakService = PowerMockito.mock(BakService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "bakService", bakService);
        backupService = PowerMockito.mock(BackupService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "backupService", backupService);
        kmsService = PowerMockito.mock(KmsService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "kmsService", kmsService);
        PowerMockito.when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), anyString())).thenReturn(true);

        CustinsParamDO minorVersionParam = new CustinsParamDO();
        minorVersionParam.setName("minor_version");
        minorVersionParam.setValue("mysql80_20201112");

        custinsParamService = PowerMockito.mock(CustinsParamService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "custinsParamService", custinsParamService);
        PowerMockito.when(custinsParamService.getCustinsParam(any(), eq("minor_version"))).thenReturn(minorVersionParam);


        podAvzSupport = PowerMockito.mock(PodAvzSupport.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "avzSupport", podAvzSupport);
        rundPodSupport = PowerMockito.mock(RundPodSupport.class);
        podCommonSupport = PowerMockito.mock(PodCommonSupport.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "podCommonSupport", podCommonSupport);
        AVZInfo avzInfo = PowerMockito.mock(AVZInfo.class);
        PowerMockito.when(avzInfo.getDispenseMode()).thenReturn(ParamConstants.DispenseMode.ClassicDispenseMode);

        ReplicaSet sourceReplicaSet = PowerMockito.mock(ReplicaSet.class);

        dependency = PowerMockito.spy(new AliyunInstanceDependency());
        Whitebox.setInternalState(cloneDBInstanceImpl, "dependency", dependency);
        Whitebox.setInternalState(dependency, "mysqlParamSupport", mysqlParamSupport);
        Whitebox.setInternalState(dependency, "dBaasMetaService", metaService);
        Whitebox.setInternalState(dependency, "podParameterHelper", podParameterHelper);
        Whitebox.setInternalState(dependency, "avzSupport", podAvzSupport);
        Whitebox.setInternalState(dependency, "rundPodSupport", rundPodSupport);
        Whitebox.setInternalState(dependency, "podCommonSupport", podCommonSupport);
        PowerMockito.doReturn(PodType.POD_RUNC).when(rundPodSupport, "getPodTypeByGrayConfig", any(), any(), any(), any());
        PowerMockito.doReturn("optimizedTest").when(podCommonSupport, "getOptimizedWritesInfo", any(), any(), any(), any());
        PowerMockito.doReturn(true).when(podCommonSupport, "isInitOptimizedWrites", any());
        PowerMockito.doReturn(false).when(podCommonSupport, "isXEngine", any());
        PowerMockito.doReturn(avzInfo).when(podAvzSupport, "getAVZInfo", anyMap());


        replicaSetService = PowerMockito.mock(ReplicaSetService.class);
        Whitebox.setInternalState(cloneDBInstanceImpl, "replicaSetService", replicaSetService);
        Whitebox.setInternalState(dependency, "replicaSetService", replicaSetService);
        ReplicaSet primaryReplicaSet = new ReplicaSet();
        primaryReplicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        primaryReplicaSet.setDiskSizeMB(1024 * 200);
        PowerMockito.doReturn(primaryReplicaSet).when(replicaSetService, "getAndCheckUserReplicaSet", any());
        PowerMockito.doReturn(1L).when(replicaSetService, "getAutoConfigProvisionedIops", any(), any(), any(), any(), any(), any());
        PowerMockito.doReturn(true).when(replicaSetService, "getAutoConfigBurstingEnabled", any(), any(), any(), any());




        defaultApi = PowerMockito.mock(DefaultApi.class);
        PowerMockito.when(metaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.when(defaultApi.getReplicaSet(any(), any(), any())).thenReturn(null);
        PowerMockito.when(defaultApi.getReplicaSet(any(), eq("sourceDBInstanceName"), any())).thenReturn(sourceReplicaSet);
        User user = new User();
        user.setBid("bid-123");
        user.setAliUid("uid-123");
        PowerMockito.when(defaultApi.getUser("123456", "1", null)).thenReturn(user);

//        PowerMockito.doReturn(defaultApi).when(metaService, "getRegionClient", any());
//        PowerMockito.doReturn(new User()).when(defaultApi, "getUserById", any(), anyInt(), anyBoolean());


        InstanceLevel instanceLevel = new InstanceLevel();;
        instanceLevel.setHostType(0);
        instanceLevel.setIsolationType(InstanceLevel.IsolationTypeEnum.COMMON);
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.SERVERLESS_STANDARD);
        PowerMockito.doReturn(instanceLevel).when(defaultApi, "getInstanceLevel", any(), any(), any(), any(), any());



        PowerMockito.mockStatic(CheckUtils.class);
        PowerMockito.when(CheckUtils.checkValidForInsName(any())).thenReturn("0");
        PowerMockito.when(CheckUtils.parseInt(any(), anyInt(), anyInt(), any())).thenReturn(200);


    }



    @Test
    public void doActionTestServiceSpecNull() throws Exception{
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "0");
        params.put("user_id", "0");
        params.put("uid", "1");
        params.put("storagetype", "0");
        params.put("Storage", "200");

        PowerMockito.when(minorVersionServiceHelper.getServiceSpecTag(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                anyBoolean(),
                anyBoolean(),
                anyMap()
        )).thenReturn("");

        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custInstanceDO, params);
        Object[] objects = (Object[])result.get("errorCode");
        System.out.println(objects[1].toString());
        assertNotNull(result.get("errorCode"));
    }


    @Test
    public void doActionTestServiceSpecOk() throws Exception{
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "0");
        params.put("user_id", "0");
        params.put("uid", "1");
        params.put("storagetype", "0");
        params.put("Storage", "200");

        PowerMockito.when(minorVersionServiceHelper.getServiceSpecTag(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                anyBoolean(),
                anyBoolean(),
                anyMap()
        )).thenReturn("abc");

        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custInstanceDO, params);
        Object[] objects = (Object[])result.get("errorCode");
        System.out.println(objects[1].toString());
        assertNotNull(result.get("errorCode"));
    }


    @Test
    public void setReplicaSetLabelsSuccess() {
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();
        Map<String, String> srcReplicaSetLabels = new HashMap<>();
        String dbEngine = "Mysql";
        String rsTemplateName = "rsTemplateName";
        String resourceGroupId = "resourceGroupId";

        Double rcu = 0.5;
        ServerlessSpec serverlessSpec = PowerMockito.mock(ServerlessSpec.class);
        PowerMockito.when(serverlessSpec.getRcu()).thenReturn(rcu);
        PowerMockito.when(serverlessSpec.getLabels()).thenReturn(new HashMap<>());

        PodCreateInsParam metaParam = PowerMockito.mock(PodCreateInsParam.class);
        PowerMockito.when(metaParam.getServerlessSpec()).thenReturn(serverlessSpec);

        try {
            PowerMockito.when(defaultApi.updateReplicaSetLabels(any(), any(), any())).thenThrow(new ApiException("test"));
            cloneDBInstanceImpl.setReplicaSetLabels(requestId, replicaSet, srcReplicaSetLabels, dbEngine, rsTemplateName, resourceGroupId, metaParam, "true");
            Assert.fail();
        } catch (Exception e) {
            assertEquals("test", e.getMessage());
        }
    }


    @Test
    public void testGetOriCloneRcu() throws ApiException {
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();

        Double rcu = 0.5;
        Double currRcu = 3.0;
        Double min = 0.5;
        Double max = 8.0;
        ServerlessSpec serverlessSpec = PowerMockito.mock(ServerlessSpec.class);
        PowerMockito.when(serverlessSpec.getRcu()).thenReturn(rcu);


        PowerMockito.when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn(null);

        double result = cloneDBInstanceImpl.getCloneTmpRcu(requestId, replicaSet, serverlessSpec);
        assertEquals(rcu, result, 0.1);
    }

    @Test
    public void testGetCurrentRcu() throws ApiException {
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();

        Double rcu = 0.5;
        Double currRcu = 3.0;
        String currRcuString = "3.0";
        ServerlessSpec serverlessSpec = PowerMockito.mock(ServerlessSpec.class);
        PowerMockito.when(serverlessSpec.getRcu()).thenReturn(rcu);


        PowerMockito.when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenReturn(currRcuString);

        PowerMockito.when(serverlessResourceService.getRcuForTmpInsByCurrent(any(),any(),any())).thenReturn(currRcu);

        double result = cloneDBInstanceImpl.getCloneTmpRcu(requestId, replicaSet, serverlessSpec);
        assertEquals(currRcu, result, 0.1);
    }

    @Test
    public void testGetErrorRcu() throws ApiException {
        String requestId = "requestId";
        ReplicaSet replicaSet = new ReplicaSet();

        Double rcu = 0.5;
        Double currRcu = 3.0;
        ServerlessSpec serverlessSpec = PowerMockito.mock(ServerlessSpec.class);
        PowerMockito.when(serverlessSpec.getRcu()).thenReturn(rcu);
        PowerMockito.when(defaultApi.getReplicaSetLabel(any(), any(), any())).thenThrow(new ApiException("test"));

        double result = cloneDBInstanceImpl.getCloneTmpRcu(requestId, replicaSet, serverlessSpec);
        assertEquals(rcu, result, 0.1);
    }

    @Test
    public void doActionTestSuccess() throws Exception {
        // 1. 准备参数
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put("user_id", "bid");
        params.put("uid", "uid");
        params.put(ParamConstants.DB_INSTANCE_CLASS, "mysql.n1.medium");
        params.put(ParamConstants.STORAGE, "200");
        params.put(ParamConstants.DB_INSTANCE_STORAGE_TYPE, "cloud_essd");
        params.put(ParamConstants.BACKUP_SET_ID, "123456");
        params.put(ParamConstants.RESTORE_TYPE, RESTORE_TYPE_BAKID);
        params.put("PreferredBackupTime", "0");

        // 2. 模拟源实例与副本集
        ReplicaSet sourceReplicaSet = PowerMockito.mock(ReplicaSet.class);
        PowerMockito.when(sourceReplicaSet.getBizType()).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        PowerMockito.when(sourceReplicaSet.getService()).thenReturn("MySQL");
        PowerMockito.when(sourceReplicaSet.getServiceVersion()).thenReturn("8.0");
        PowerMockito.when(sourceReplicaSet.getClassCode()).thenReturn("mysql.n1.medium");
        PowerMockito.when(sourceReplicaSet.getUserId()).thenReturn("1");
        PowerMockito.when(sourceReplicaSet.getConnType()).thenReturn(ReplicaSet.ConnTypeEnum.PHYSICAL);
        PowerMockito.when(sourceReplicaSet.getStatus()).thenReturn(ReplicaSet.StatusEnum.ACTIVE);

        PowerMockito.when(defaultApi.getReplicaSet(any(), eq("sourceDBInstanceName"), any()))
            .thenReturn(sourceReplicaSet);

        // 3. 模拟小版本与服务规格
        PowerMockito.when(minorVersionServiceHelper.tryGetServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), anyString(), anyBoolean(), anyMap()))
            .thenReturn("serverless-spec-tag");

        // 4. 模拟备份历史与快照
        BakhistoryDO bakHistory = new BakhistoryDO();
        bakHistory.setHisId(123L);
        bakHistory.setBakBegin(new Date());
        bakHistory.setBaksetSize(100L * CustinsSupport.GB_TO_KB);
        bakHistory.setKindCode(KIND_CODE_NEW_ARCH);
        PowerMockito.when(bakService.getBakhistoryByBackupSetId(anyInt(), anyLong())).thenReturn(bakHistory);

        GetBackupSetResponse backupSet = PowerMockito.mock(GetBackupSetResponse.class);
        PowerMockito.when(backupSet.getSlaveStatusObj()).thenReturn(new GetBackupSetResponse.SlaveStatus());
        PowerMockito.when(backupService.getBackupSet(any())).thenReturn(backupSet);


        // 7. 执行测试
        Map<String, Object> result = cloneDBInstanceImpl.doActionRequest(custInstanceDO, params);

        // 8. 验证结果
        assertNotNull(result);
    }
}
