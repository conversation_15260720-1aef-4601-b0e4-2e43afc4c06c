package com.aliyun.dba.serverless.action.service;

import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.IPWhiteList;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.service.AligroupService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.ParamConstants;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CustinsParamSupport.class})
public class ServerlessCreateDBInstanceServiceTest {

    @InjectMocks
    private ServerlessCreateDBInstanceService createDBInstanceService;
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private AliyunInstanceDependency dependency;
    @Mock
    private DBaasMetaService dbaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonProviderApi;
    @Mock
    private ResourceService resourceService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private PodAvzSupport avzSupport;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private AligroupService aligroupService;
    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private ServerlessResourceService serverlessResourceService;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Mock
    private PodTemplateHelper podTemplateHelper;
    @Mock
    private WhitelistTemplateService whitelistTemplateService;
    @Mock
    private CommonProviderService commonProviderService;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private WorkFlowService workFlowService;

    private Map<String, String> params;

    private CustInstanceDO custins;

    @Before
    public void setUp() throws Exception {
        params = new HashMap<String, String>(){{
            put(ParamConstants.INSTANCE_ID, "test");
            put(ParamConstants.RESOURCE_GROUP_ID.toLowerCase(), "rg-xxxxx");
        }};
        custins = new CustInstanceDO();
        // dependencies
        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        PowerMockito.when(dependency.getAligroupService()).thenReturn(aligroupService);
        PowerMockito.when(dependency.getDBaasMetaService()).thenReturn(dbaasMetaService);
        PowerMockito.when(dependency.getResourceService()).thenReturn(resourceService);
        PowerMockito.when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        PowerMockito.when(dependency.getAvzSupport()).thenReturn(avzSupport);
        PowerMockito.when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        PowerMockito.when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        PowerMockito.when(dependency.getPodTemplateHelper()).thenReturn(podTemplateHelper);
        // mocks
        PowerMockito.when(rundPodSupport.getPodTypeByGrayConfig(any(), any(), any(InstanceLevel.class), any(AVZInfo.class))).thenReturn(PodType.POD_ECS_RUND);
        PowerMockito.when(rundPodSupport.minorVersionSupportRund(anyString())).thenReturn(true);
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        PowerMockito.when(dbaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.when(avzSupport.getAVZInfo(params)).thenReturn(new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "region", "zone", "vswiych", null));
        PowerMockito.when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(new IPWhiteList(){{
            setWhiteList("whiteList");
        }});
        PowerMockito.when(podParameterHelper.getBizType(anyString(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        PowerMockito.when(podParameterHelper.getDiskType(any())).thenReturn("cloud_essd");
        PowerMockito.when(commonProviderService.getDefaultApi()).thenReturn(commonProviderApi);
        PowerMockito.when(defaultApi.getInstanceLevel(anyString(), any(), any(), any(), any())).thenReturn(new InstanceLevel(){{
            setCategory(CategoryEnum.BASIC);
        }});
        PowerMockito.when(defaultApi.getReplicaSet(anyString(), any(), any())).thenReturn(new ReplicaSet(){{setId(123L);}});
        PowerMockito.when(defaultApi.getReplicaSet(anyString(), any(), eq(true))).thenReturn(null);
        PowerMockito.when(podTemplateHelper.getBizSysScheduleTemplate(any(), any(ReplicaSet.BizTypeEnum.class), any(), any(InstanceLevel.class), anyBoolean(), anyString(), any(), any(), any())).thenReturn(new Pair<String, ScheduleTemplate>() {
            @Override
            public String getLeft() {
                return "rsTemplateName";
            }

            @Override
            public ScheduleTemplate getRight() {
                return new ScheduleTemplate();
            }

            @Override
            public ScheduleTemplate setValue(ScheduleTemplate value) {
                return null;
            }
        });
        PowerMockito.when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.STORAGE), any())).thenReturn("10240");
        PowerMockito.when(minorVersionServiceHelper.getServiceSpecTag(any(), any(ReplicaSet.BizTypeEnum.class), any(), any(), any(), anyInt(), any(InstanceLevel.class), anyString(), anyBoolean(),anyBoolean())).thenReturn("serviceSpecTag");
        PowerMockito.mockStatic(CustinsParamSupport.class);
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.VPC_ID)).thenReturn("vpcId");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.VSWITCH_ID)).thenReturn("vswitchId");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.CONNECTION_STRING)).thenReturn("rm-2ze6v5w067pu4e7m2");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.RESOURCE_GROUP_ID, "")).thenReturn("rg-xxxxx");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.BACKUP_RETENTION, 7)).thenReturn("7");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, "PreferredBackupTime")).thenReturn("12:12Z");
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, "preferredbackupperiod")).thenReturn("1111111");
    }

    @Test
    public void createDBInstance() {
        Map<String, Object> res = createDBInstanceService.createDBInstance(custins, params);
        assertNotNull(res);
    }
}