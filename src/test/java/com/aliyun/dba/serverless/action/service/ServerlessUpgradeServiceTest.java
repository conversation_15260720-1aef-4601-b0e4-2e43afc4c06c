package com.aliyun.dba.serverless.action.service;

import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.service.modify.AllocateTmpResourceService;
import com.aliyun.dba.poddefault.action.support.AllocateTmpResourceResult;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.resource.dataobject.IpResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.serverless.action.module.ServerlessSpec;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.custins.support.CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2024/12/17 10:26
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({ServerlessUpgradeService.class, DBaasMetaService.class, PodCommonSupport.class})
public class ServerlessUpgradeServiceTest {
    private ServerlessUpgradeService serverlessUpgradeService;
    private MinorVersionServiceHelper minorVersionServiceHelper;
    private MysqlParamSupport mysqlParamSupport;
    private ResourceService resourceService;
    private AliyunInstanceDependency dependency;
    private DBaasMetaService dbaasMetaService;
    private DefaultApi defaultApi;
    private PodCommonSupport podCommonSupport;
    private ServerlessResourceService serverlessResourceService;
    private AllocateTmpResourceService allocateTmpResourceService;

    @Before
    public void setUp() throws Exception {
        serverlessUpgradeService = PowerMockito.spy(new ServerlessUpgradeService());

        mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        resourceService = PowerMockito.mock(ResourceService.class);
        dependency = PowerMockito.mock(AliyunInstanceDependency.class);
        dbaasMetaService = PowerMockito.mock(DBaasMetaService.class);
        defaultApi = PowerMockito.mock(DefaultApi.class);
        podCommonSupport = PowerMockito.mock(PodCommonSupport.class);
        serverlessResourceService = PowerMockito.mock(ServerlessResourceService.class);
        minorVersionServiceHelper = PowerMockito.mock(MinorVersionServiceHelper.class);
        allocateTmpResourceService = PowerMockito.mock(AllocateTmpResourceService.class);

        PowerMockito.when(mysqlParamSupport.getParameterValue(anyMap(), eq(ParamConstants.REQUEST_ID), eq(""))).thenReturn("requestId");
        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        PowerMockito.when(dbaasMetaService.getDefaultClient()).thenReturn(defaultApi);

        Whitebox.setInternalState(serverlessUpgradeService, "minorVersionServiceHelper", minorVersionServiceHelper);
        Whitebox.setInternalState(serverlessUpgradeService, "mysqlParamSupport", mysqlParamSupport);
        Whitebox.setInternalState(serverlessUpgradeService, "resourceService", resourceService);
        Whitebox.setInternalState(serverlessUpgradeService, "dBaasMetaService", dbaasMetaService);
        Whitebox.setInternalState(serverlessUpgradeService, "dependency", dependency);
        Whitebox.setInternalState(serverlessUpgradeService, "serverlessResourceService", serverlessResourceService);
        Whitebox.setInternalState(serverlessUpgradeService, "allocateTmpResourceService", allocateTmpResourceService);
    }

    @Test
    public void doGetTargetServiceTagNull() throws Exception {
        MysqlParamSupport mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        AliyunInstanceDependency dependency = PowerMockito.mock(AliyunInstanceDependency.class);
        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), any(), any())).thenReturn("123456");

        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setService("mysql");
        replicaSet.setServiceVersion("8.0");


        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        podModifyInsParam.setReplicaSetMeta(replicaSet);

        PowerMockito.when(minorVersionServiceHelper.tryGetServiceSpecTag(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                anyBoolean(),
                anyMap()
        )).thenReturn(null);

        String serviceTag = serverlessUpgradeService.getTargetServiceSpecTag(podModifyInsParam);
        assertNull(serviceTag);
    }

    @Test
    public void doGetTargetServiceTagNotNull() throws Exception {
        MysqlParamSupport mysqlParamSupport = PowerMockito.mock(MysqlParamSupport.class);
        AliyunInstanceDependency dependency = PowerMockito.mock(AliyunInstanceDependency.class);
        PowerMockito.when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        PowerMockito.when(mysqlParamSupport.getParameterValue(any(), any(), any())).thenReturn("123456");

        Map<String, String> params = new HashMap<>();
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        replicaSet.setService("mysql");
        replicaSet.setServiceVersion("8.0");


        PodModifyInsParam podModifyInsParam = new PodModifyInsParam(dependency, params);
        podModifyInsParam.setReplicaSetMeta(replicaSet);

        PowerMockito.when(minorVersionServiceHelper.tryGetServiceSpecTag(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                anyBoolean(),
                anyMap()
        )).thenReturn("mysql8");

        String serviceTag = serverlessUpgradeService.getTargetServiceSpecTag(podModifyInsParam);
        assertEquals("mysql8", serviceTag);
    }

    @Test
    public void migrateProvisionToServerless() throws Exception {
        Map<String, String> params = new HashMap<>();
        PodModifyInsParam metaParam = new PodModifyInsParam(dependency, params);
        metaParam.setReplicaSetMeta(new ReplicaSet(){{
            setCategory("basic");
            setService("mysql");
            setServiceVersion("8.0");
            setId(1L);
            setInsType(ReplicaSet.InsTypeEnum.MAIN);
        }});
        metaParam.setTargetInstanceLevel(new InstanceLevel(){{
            setCategory(CategoryEnum.SERVERLESS_BASIC);
        }});
        metaParam.setSrcMinorVersion("99");
        metaParam.setTargetServerlessSpec(new ServerlessSpec(){{
            setScaleMax(8.0);
        }});
        metaParam.setRcu(4.0);
        metaParam.setCustins(new CustInstanceDO(){{setId(1);}});
        doReturn(metaParam).when(serverlessUpgradeService).initServerlessSccModifyInsParam(params);
        when(defaultApi.listReplicaSetLabels(anyString(), any())).thenReturn(new HashMap<String, String>(){{
            put(CUSTINS_PARAM_NAME_PARAM_GROUP_ID, "123");
        }});
        when(defaultApi.listReplicaSetSubIns(anyString(), any(), anyString())).thenReturn(new ReplicaSetListResult(){{
            setItems(null);
        }});
        when(defaultApi.listReplicaSetEndpoints(anyString(), any(), any(), any(), any(), any())).thenReturn(new EndpointListResult(){{
            setItems(ImmutableList.of(new Endpoint(){{
                setNetType(Endpoint.NetTypeEnum.VPC);
                setType(Endpoint.TypeEnum.NORMAL);
                setVpcId("vpcId");
                setVip("***********");
            }}));
        }});
        when(serverlessResourceService.getMaxScaleID(anyString(), any())).thenReturn(null);
        when(resourceService.getIpResourceByIpAndVpc(anyString(), anyString())).thenReturn(new IpResourceDO(){{
            setvSwitchId("vSwitchId");
        }});
        when(allocateTmpResourceService.make(anyString(), any(), any())).thenReturn(new AllocateTmpResourceResult(){{
            setResourceRequest(new ReplicaSetResourceRequest(){{setReplicaSetName("rm-xxx");}});
            setReplicaSet(new ReplicaSet(){{setName("rm-xxx");setId(1L);}});
        }});
        doReturn(true).when(serverlessUpgradeService).isStorageSupportToServerless(any());
        doReturn("serviceSpecId").when(serverlessUpgradeService).getTargetServiceSpecTag(any());
        doReturn(1L).when(serverlessUpgradeService).createMaxScaleIns(anyMap(), anyString(), any(), any());
        try {
            serverlessUpgradeService.migrateProvisionToServerless(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    public void migrateServerlessToProvision() throws Exception {
        Map<String, String> params = new HashMap<>();
        PodModifyInsParam metaParam = new PodModifyInsParam(dependency, params);
        metaParam.setReplicaSetMeta(new ReplicaSet(){{
            setCategory("serverless_basic");
            setService("mysql");
            setServiceVersion("8.0");
            setId(1L);
            setInsType(ReplicaSet.InsTypeEnum.MAIN);
        }});
        metaParam.setTargetInstanceLevel(new InstanceLevel(){{
            setCategory(CategoryEnum.BASIC);
        }});
        metaParam.setSrcMinorVersion("99");
        metaParam.setTargetServerlessSpec(new ServerlessSpec(){{
            setScaleMax(8.0);
        }});
        metaParam.setRcu(4.0);
        metaParam.setCustins(new CustInstanceDO(){{setId(1);}});
        doReturn(metaParam).when(serverlessUpgradeService).initServerlessSccModifyInsParam(params);
        when(defaultApi.listReplicaSetLabels(anyString(), any())).thenReturn(new HashMap<String, String>(){{
            put(CUSTINS_PARAM_NAME_PARAM_GROUP_ID, "123");
        }});
        when(defaultApi.listReplicaSetSubIns(anyString(), any(), anyString())).thenReturn(new ReplicaSetListResult(){{
            setItems(null);
        }});
        when(defaultApi.listReplicaSetEndpoints(anyString(), any(), any(), any(), any(), any())).thenReturn(new EndpointListResult(){{
            setItems(ImmutableList.of(new Endpoint(){{
                setNetType(Endpoint.NetTypeEnum.VPC);
                setType(Endpoint.TypeEnum.NORMAL);
                setVpcId("vpcId");
                setVip("***********");
            }}));
        }});
        when(serverlessResourceService.getMaxScaleID(anyString(), any())).thenReturn(null);
        when(resourceService.getIpResourceByIpAndVpc(anyString(), anyString())).thenReturn(new IpResourceDO(){{
            setvSwitchId("vSwitchId");
        }});
        when(allocateTmpResourceService.make(anyString(), any(), any())).thenReturn(new AllocateTmpResourceResult(){{
            setResourceRequest(new ReplicaSetResourceRequest(){{setReplicaSetName("rm-xxx");}});
            setReplicaSet(new ReplicaSet(){{setName("rm-xxx");setId(1L);}});
        }});
        doReturn(true).when(serverlessUpgradeService).isStorageSupportToServerless(any());
        doReturn("serviceSpecId").when(serverlessUpgradeService).getTargetServiceSpecTag(any());
        doReturn(1L).when(serverlessUpgradeService).createMaxScaleIns(anyMap(), anyString(), any(), any());
        try {
            serverlessUpgradeService.migrateServerlessToProvision(params);
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
}
