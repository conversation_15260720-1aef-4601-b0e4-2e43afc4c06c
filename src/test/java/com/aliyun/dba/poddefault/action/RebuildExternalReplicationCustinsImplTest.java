package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.activityprovider.model.RebuildReplicaResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.poddefault.action.support.PodReplicaSetResourceHelper;
import com.aliyun.dba.poddefault.action.support.PodType;
import com.aliyun.dba.poddefault.action.support.RundPodSupport;
import com.aliyun.dba.serverless.action.service.ServerlessResourceService;
import com.aliyun.dba.service.ClusterBackUpService;
import com.aliyun.dba.service.MySQLService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.oss.OSSClient;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse;
import com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CustinsParamSupport.class, RebuildExternalReplicationCustinsImpl.class})
public class RebuildExternalReplicationCustinsImplTest {

    @InjectMocks
    @Spy
    private RebuildExternalReplicationCustinsImpl rebuildExternalReplicationCustinsImpl = new RebuildExternalReplicationCustinsImpl();
    @Mock
    private MysqlParamSupport mysqlParamSupport;
    @Mock
    private MySQLServiceImpl mySQLService;
    @Mock
    private DBaasMetaService dBaasMetaService;
    @Mock
    private DefaultApi defaultApi;
    @Mock
    private SlrCheckService slrCheckService;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private CommonProviderService commonProviderService;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonProviderApi;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private ServerlessResourceService serverlessResourceService;
    @Spy
    private ReplicaSetService replicaSetService = mock(ReplicaSetService.class);
    @Mock
    private ClusterBackUpService clusterBackUpService;

    private CustInstanceDO custins;
    private Map<String, String> params;

    @Before
    public void setUp() throws Exception {
        custins = new CustInstanceDO();
        params = new HashMap<>();

        PowerMockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        PowerMockito.when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(new ReplicaSet(){{setName("rm-xxx");setCategory("basic");}});
        PowerMockito.when(mySQLService.getReplicaByRole(anyString(), anyString(), any())).thenReturn(new Replica(){{setId(1L);}});
        PowerMockito.when(defaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(new ReplicaResource(){{setVpod(new Vpod(){{setRuntimeType("rund");}});}});
        PowerMockito.when(slrCheckService.getCredentials(anyString(), anyString(), anyLong())).thenReturn(new AssumeRoleWithServiceIdentityResponse.Credentials(){{setAccessKeyId("ak");setAccessKeySecret("sk");setSecurityToken("token");}});
        PowerMockito.when(commonProviderService.getDefaultApi()).thenReturn(commonProviderApi);
        PowerMockito.when(podReplicaSetResourceHelper.allocateRebuildResource4Basic(any(), any(PodType.class))).thenReturn(new RebuildReplicaResourceRequest(){{setTmpReplicaSetName("rm-yyy");}});
        PowerMockito.when(defaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ReplicaListResult(){{setItems(ImmutableList.of(new Replica(){{setId(1L);}}));}});
        PowerMockito.when(workFlowService.dispatchTask(anyString(), anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(1);
        PowerMockito.when(serverlessResourceService.getRcuForTmpIns(anyString(), any())).thenReturn(0.5);

        doNothing().when(replicaSetService).preCheckForExternalReplication(any(), anyString(), any());

        PowerMockito.mockStatic(CustinsParamSupport.class);
        PowerMockito.when(CustinsParamSupport.getParameterValue(params, ParamConstants.UID)).thenReturn("uid");
    }

    @Test
    public void doActionRequest() throws RdsException, ApiException {
        assertNotNull(rebuildExternalReplicationCustinsImpl.doActionRequest(custins, params));
        PowerMockito.when(replicaSetService.getAndCheckUserReplicaSet(params)).thenReturn(new ReplicaSet(){{setName("rm-xxx");setCategory("serverless_basic");}});
        assertNotNull(rebuildExternalReplicationCustinsImpl.doActionRequest(custins, params));
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, "Mode")).thenReturn("stream");
        assertNotNull(rebuildExternalReplicationCustinsImpl.doActionRequest(custins, params));
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, "IP")).thenReturn("127.0.0.1");
        PowerMockito.when(mysqlParamSupport.getAndCheckIpAddress(params)).thenReturn("127.0.0.1");
        PowerMockito.when(mysqlParamSupport.getParameterValue(params, "Port")).thenReturn("9090");
        assertNotNull(rebuildExternalReplicationCustinsImpl.doActionRequest(custins, params));
        PowerMockito.when(mysqlParamSupport.isBuildReplication(params)).thenReturn(true);
        try {
            rebuildExternalReplicationCustinsImpl.doActionRequest(custins, params);
        } catch (RdsException e) {
            assertNotNull(e);
        }
    }
}