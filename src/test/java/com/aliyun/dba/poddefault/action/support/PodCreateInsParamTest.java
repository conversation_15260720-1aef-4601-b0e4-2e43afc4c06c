package com.aliyun.dba.poddefault.action.support;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.DbsGateWayService;
import com.aliyun.dba.base.parameter.backup.DescribeRestoreBackupSetParam;
import com.aliyun.dba.base.response.backup.DescribeRestoreBackupSetResponse;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class PodCreateInsParamTest {

    @Mock
    private AliyunInstanceDependency dependency;

    @Mock
    DbsGateWayService dbsGateWayService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private PodParameterHelper podParameterHelper;

    @InjectMocks
    private PodCreateInsParam param;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private CustinsService custinsService;

    @Mock
    private MySQLServiceImpl mySQLservice;

    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dependency.getDBaasMetaService().getDefaultClient()).thenReturn(defaultApi);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getDbsGateWayService()).thenReturn(dbsGateWayService);
        when(dependency.getMySQLservice()).thenReturn(mySQLservice);
        when(dependency.getMinorVersionServiceHelper()).thenReturn(minorVersionServiceHelper);
    }

    @Test
    public void testInitVbmConfig_Success() throws Exception {
        // Given
        String uid = "testUid";
        String bid = "testBid";
        Integer userId = 1;
        when(mysqlParamSupport.getAndCheckBID(any())).thenReturn(bid);
        when(mysqlParamSupport.getUID(any())).thenReturn(uid);
        when(mysqlParamSupport.getAndCreateUserId(any())).thenReturn(userId);
        param.setUserId();


        String dbType ="MySQL";
        when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn(dbType);
        param.setDBType();

        String dbVersion ="8.0";
        when(mysqlParamSupport.getAndCheckDBVersion(any(), anyString(), anyBoolean())).thenReturn(dbVersion);
        param.setDBVersion();
        param.setClassCode();
        param.setInstanceLevel();

    }


    @Test
    public void setDiskSize_InstanceLevelNull_DiskSizeSetToDefault() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.STORAGE, "100");
        param.setParams(params);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE, null)).thenReturn("100");

        param.setDiskSize();

        assertEquals(100, param.getDiskSize().intValue());
    }

    @Test
    public void setDiskSize_InstanceLevelNotNull_DiskSizeSetFromInstanceLevel() throws Exception {
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setDiskSizeMB(20480);
        param.setInstanceLevel(instanceLevel);

        Map<String, String> params = new HashMap<>();
        param.setParams(params);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE, 20)).thenReturn("20");

        param.setDiskSize();

        assertEquals(20, param.getDiskSize().intValue());
    }

    @Test
    public void setDiskSize_CompressionModeOn_DiskSizeAdjusted() throws Exception {
        param.setCompressionMode("on");
        param.setCompressionRatio(2.0);

        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.STORAGE, "100");
        param.setParams(params);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.STORAGE, null)).thenReturn("100");

        param.setDiskSize();

        assertEquals(200, param.getDiskSize().intValue());
        assertEquals(100, param.getDiskSizeGBBeforeCompression().intValue());
    }

    @Test
    public void setCompressionMode_CompressionModeOn_CompressionRatioSet() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(CloudDiskCompressionHelper.COMPRESSION_MODE, "on");
        params.put(CloudDiskCompressionHelper.COMPRESSION_RATIO, "0.5");
        param.setParams(params);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_MODE)).thenReturn("on");
        when(mysqlParamSupport.getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_RATIO)).thenReturn("0.5");
        when(cloudDiskCompressionHelper.getCompressionMode("requestId", null, "on")).thenReturn("on");
        when(cloudDiskCompressionHelper.getCompressionRatio("requestId", null, "0.5")).thenReturn(0.5);

        param.setCompressionMode();

        assertEquals("on", param.getCompressionMode());
        assertEquals(0.5, param.getCompressionRatio(), 0.0);
    }

    @Test
    public void setCompressionMode_CompressionModeOff_CompressionRatioNotSet() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(CloudDiskCompressionHelper.COMPRESSION_MODE, "off");
        param.setParams(params);
        when(mysqlParamSupport.getParameterValue(params, ParamConstants.REQUEST_ID)).thenReturn("requestId");
        when(mysqlParamSupport.getParameterValue(params, CloudDiskCompressionHelper.COMPRESSION_MODE)).thenReturn("off");
        when(cloudDiskCompressionHelper.getCompressionMode("requestId", null, "off")).thenReturn("off");

        param.setCompressionMode();

        assertEquals("off", param.getCompressionMode());
        assertEquals(1.0, param.getCompressionRatio(), 0.0);
    }

    @Test
    public void initOptimizedWritesFromBackUpSetTest() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("requestid", "requestId");
        params.put(ParamConstants.REQUEST_ID, "requestId");
        params.put(CloudDiskCompressionHelper.COMPRESSION_MODE, "off");
        param.setParams(params);
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();

        Map<String, String> variables = new HashMap<>();
        extraInfo.setVariables(new Gson().toJson(variables));
        param.initOptimizedWritesFromBackUpSetVariables(variables);


        variables.put("innodb_doublewrite", "OFF");
        extraInfo.setVariables(new Gson().toJson(variables));
        param.initOptimizedWritesFromBackUpSetVariables(variables);

        assertTrue(param.isInitOptimizedWrites());
    }


    @Test
    public void setRebuildDeleteInsConfig() throws Exception {
        Map<String, String> params = new HashMap<>();
        PodCreateInsParam PodCreateInsParamSpy = new PodCreateInsParam(dependency, params);
        params.put(ParamConstants.SOURCE_DBINSTANCE_ID, "123");
        params.put("sourceDBInstanceId", "1231231");
        params.put("BackupSetId", "rbh-aaaaa");
        params.put("userId", "123");
        GetBackupSetResponse backupSetResponse = new GetBackupSetResponse();
        backupSetResponse.setEngine("correctEngine");
        backupSetResponse.setEngineVersion("correctVersion");
//        when(dependency.getMysqlParamSupport()
//            .getParameterValue(params, ParamConstants.SOURCE_DBINSTANCE_ID)).thenReturn(params.get("sourceDBInstanceId"));
        CustInstanceDO sourceCustInstanceDO = new CustInstanceDO();
        sourceCustInstanceDO.setInsName("rm-test");
        when(dependency.getMySQLservice().isRebuildBackupSet(null)).thenReturn(true);
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
        DescribeRestoreBackupSetResponse response = new DescribeRestoreBackupSetResponse();
        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
        extraInfo.setSNAPSHOT_ID("123");
        extraInfo.setTdeEnabled(false);
        Map<String, String> variables = new HashMap<>();
        variables.put("innodb_doublewrite", "OFF");
        extraInfo.setVariables(new Gson().toJson(variables));
        backupSetInfo.setExtraInfoObj(extraInfo);
        backupSetInfo.setInstanceKindCode(String.valueOf(KindCodeParser.KIND_CODE_NEW_ARCH));
        response.setBackupSetInfoObj(backupSetInfo);
        when(dependency.getDbsGateWayService().describeRestoreBackupSet(null)).thenReturn(response);
        //when(dependency.getMinorVersionServiceHelper().getServiceSpecTag(null, null, null, null, null, 18, null, null, false, true)).thenReturn("bakServiceSpecTag");
        param.setParams(params);
        param.setRebuildDeleteInsConfig();
        assertNull(PodCreateInsParamSpy.getSnapshotId());
    }
}
