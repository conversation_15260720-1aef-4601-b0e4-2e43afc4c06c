package com.aliyun.dba.poddefault.action;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.instance.dataobject.InstanceLevelDO;
import com.aliyun.dba.instance.service.InstanceService;
import com.aliyun.dba.poddefault.action.service.MinorVersionServiceHelper;
import com.aliyun.dba.poddefault.action.support.PodParameterHelper;
import com.aliyun.dba.resource.dataobject.ResourceDO;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.service.KmsService;
import com.aliyun.dba.support.utils.RequestSession;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(RequestSession.class)
public class UpgradeDBVersionImplTest {
    @InjectMocks
    private UpgradeDBVersionImpl upgradeDBVersion;
    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;
    @Mock
    private MysqlParamSupport paramSupport;
    @Mock
    private ReplicaSetService replicaSetService;
    @Mock
    private KmsService kmsService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;
    @Mock
    private CustinsService custinsService;
    @Mock
    private InstanceService instanceService;
    @Mock
    private ResourceService resourceService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(RequestSession.class);
        PowerMockito.when(RequestSession.getRequestId()).thenReturn("testRequestId");
        MockitoAnnotations.initMocks(this);
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        ResourceDO resource = new ResourceDO();
        resource.setRealValue("{\"alisql_docker_image\":{\"legalTargetTag\":[\"alisql_xc_docker_image\"],\"uids\":[\"1875785612443619\"],\"kindCode\":[18]},\"alisql_xc_docker_image\":{\"legalTargetTag\":[\"alisql_docker_image\"],\"uids\":[\"1875785612443619\"],\"kindCode\":[18]}}");
        when(resourceService.getResourceByResKey("CROSS_TAG_RULES")).thenReturn(resource);
    }

    @Test
    public void test_upgradeDBVersion_primary_targetxc() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_ro_srcxc() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_ro_targetxc() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_primary_srcxc() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_primary_targetxc_basic() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("basic");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_ro_srcxc_basic() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setCategory("basic");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_ro_targetxc_basic() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setCategory("basic");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_primary_srcxc_basic() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("basic");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"x86hg\"}");
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_primary_srcxc_x86hg() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("basic");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setExtraInfo("{\"instructionSetArch\":\"x86hg\"}");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_primary_targetxc_x86hg() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.MAIN);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        instanceLevelDO.setCategory("standard");
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }

    @Test
    public void test_upgradeDBVersion_ro_srcxc1() throws Exception {
        CustInstanceDO custins = new CustInstanceDO();
        Map<String, String> params = new HashMap<>();
        params.put("uid", "187");
        ReplicaSet replicaSet = new ReplicaSet();
        when(replicaSetService.getAndCheckUserReplicaSet(any())).thenReturn(replicaSet);
        replicaSet.setLockMode(ReplicaSet.LockModeEnum.NOLOCK);
        replicaSet.setStatus(ReplicaSet.StatusEnum.ACTIVATION);
        replicaSet.setConnType(ReplicaSet.ConnTypeEnum.LVS);
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.READONLY);
        replicaSet.setCategory("standard");
        replicaSet.setName("replicaSet");
        replicaSet.setId(1L);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.STANDARD);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"x86hg\"}");
        when(dBaasMetaService.getDefaultClient().getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        when(minorVersionServiceHelper.getServiceSpecTagByReplicaSetName(any(), any())).thenReturn("alisql_xc_docker_image_cloud_disk_20241231");
        when(kmsService.isCustinsByokAndKeyEnableOrNoByok(any(ReplicaSet.class), any())).thenReturn(true);
        when(minorVersionServiceHelper.getServiceSpecTag(
                anyString(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), null))
                .thenReturn("alisql_docker_image_cloud_disk_20241231");
        List<CustInstanceDO> readinsList = new ArrayList<>();
        CustInstanceDO custInstanceDO = new CustInstanceDO();
        readinsList.add(custInstanceDO);
        when(custinsService.getReadCustInstanceListByPrimaryCustinsId(anyInt(), anyBoolean())).thenReturn(readinsList);
        InstanceLevelDO instanceLevelDO = new InstanceLevelDO();
        when(instanceService.getInstanceLevelByLevelId(any())).thenReturn(instanceLevelDO);
        Map<String, Object> stringObjectMap = upgradeDBVersion.doActionRequest(custins, params);
        Assert.assertNotNull(stringObjectMap);
    }
}
