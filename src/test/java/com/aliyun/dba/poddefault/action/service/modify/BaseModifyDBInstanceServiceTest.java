package com.aliyun.dba.poddefault.action.service.modify;

import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.poddefault.action.service.AliyunInstanceDependency;
import com.aliyun.dba.poddefault.action.service.PoddefaultResourceGuaranteeModelService;
import com.aliyun.dba.poddefault.action.support.PodDefaultConstants;
import com.aliyun.dba.poddefault.action.support.PodModifyInsParam;
import com.aliyun.dba.poddefault.action.support.ResourceScheduleHelper;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BaseModifyDBInstanceServiceTest {

    @InjectMocks
    private BaseModifyDBInstanceService baseModifyDBInstanceService;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @InjectMocks
    private AliyunInstanceDependency dependency;

    @Mock
    private PoddefaultResourceGuaranteeModelService poddefaultResourceGuaranteeModelService;
    @Mock
    private ResourceScheduleHelper resourceScheduleHelper;

    @Before
    public void setUp() {
    }

    @Test
    public void testBuildTransListForLocalModify_Success() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("RequestId", "testRequestId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");


        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);
        modifyInsParam.setReplicaSetMeta(replicaSet);
        modifyInsParam.setClassCode("testClassCode");
        modifyInsParam.setDiskSizeGB(10);
        modifyInsParam.setTargetClassCode("testTargetClassCode");
        modifyInsParam.setTargetDiskSizeGB(20);
        modifyInsParam.setUid("testUid");
        modifyInsParam.setSrcDiskType("testSrcDiskType");
        modifyInsParam.setTargetDiskType("testTargetDiskType");
        modifyInsParam.setSrcPerformanceLevel("testSrcPerformanceLevel");
        modifyInsParam.setTargetPerformanceLevel("testTargetPerformanceLevel");
        ;


        DefaultApi defaultApi = Mockito.mock(DefaultApi.class);
        Mockito.when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);


        TransferTask transferTask = new TransferTask();
        transferTask.setId(1);

        when(defaultApi.createTransferTask(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(transferTask);


        Map<String, String> allResourceGuaranteeLevelMap = new HashMap<>();
        allResourceGuaranteeLevelMap.put("testKey", "testValue");

        when(poddefaultResourceGuaranteeModelService.getAllResourceGuaranteeLevelMap(any(),any())).thenReturn(allResourceGuaranteeLevelMap);

        // Act
        Integer transListId = baseModifyDBInstanceService.buildTransListForLocalModify(modifyInsParam);

        // Assert
        assertEquals(Integer.valueOf(1), transListId);
    }

    @Test
    public void test_modifyWithAllocateResource() throws Exception {
        // Arrange
        Map<String, String> params = new HashMap<>();
        params.put("RequestId", "testRequestId");

        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("testReplicaSetName");

        PodModifyInsParam modifyInsParam = new PodModifyInsParam(dependency, params);
        modifyInsParam.setReplicaSetMeta(replicaSet);
        modifyInsParam.setClassCode("testClassCode");
        modifyInsParam.setDiskSizeGB(10);
        modifyInsParam.setTargetClassCode("testTargetClassCode");
        modifyInsParam.setTargetDiskSizeGB(20);
        modifyInsParam.setUid("testUid");
        modifyInsParam.setSrcDiskType("testSrcDiskType");
        modifyInsParam.setTargetDiskType("testTargetDiskType");
        modifyInsParam.setSrcPerformanceLevel("testSrcPerformanceLevel");
        modifyInsParam.setTargetPerformanceLevel("testTargetPerformanceLevel");

        Method setParamInitDone = PodModifyInsParam.class.getDeclaredMethod("setParamInitDone", String.class);
        setParamInitDone.setAccessible(true);
        setParamInitDone.invoke(modifyInsParam, "targetDiskType");
        Field isTargetSingleTenant = PodModifyInsParam.class.getDeclaredField("isTargetSingleTenant");
        isTargetSingleTenant.setAccessible(true);
        isTargetSingleTenant.set(modifyInsParam, true);
        Field isSrcSingleTenant = PodModifyInsParam.class.getDeclaredField("isSrcSingleTenant");
        isSrcSingleTenant.setAccessible(true);
        isSrcSingleTenant.set(modifyInsParam, true);

        Pair<String, String> resourceEnsurancePair = Pair.of(PodDefaultConstants.LABEL_ALIGNMENT_STRATEGY, PodDefaultConstants.SINGLE_TENANT_RESOURCE_STRATEGY_COMPUTING);
        when(resourceScheduleHelper.makeResourceGuaranteeStrategy(any(),any())).thenReturn(resourceEnsurancePair);


        List<Replica> currentReplicas = new ArrayList<>();
        List<String> targetReplicas = new ArrayList<>();
        try {
            Object o = baseModifyDBInstanceService.modifyWithAllocateResource(modifyInsParam, currentReplicas, targetReplicas);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }
}
