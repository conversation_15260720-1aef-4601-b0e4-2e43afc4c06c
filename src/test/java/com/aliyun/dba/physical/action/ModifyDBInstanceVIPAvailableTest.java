package com.aliyun.dba.physical.action;

import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.support.MysqlErrorCode;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.dataobject.CustinsConnAddrDO;
import com.aliyun.dba.custins.service.ConnAddrCustinsService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.task.dataobject.TaskQueueDO;
import com.aliyun.dba.task.service.TaskService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
public class ModifyDBInstanceVIPAvailableTest {

    @InjectMocks
    private ModifyDBInstanceVIPAvailableImpl modifyDBInstanceVIPAvailable;

    @Autowired
    @Mock
    protected MysqlParamSupport mysqlParamSupport;
    @Mock
    private ResourceService resourceService;
    @Mock
    private ConnAddrCustinsService connAddrCustinsService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private TaskService taskService;
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 初始化 OPERATION_TYPE_SET
        Set<String> operationTypes = new HashSet<>();
        operationTypes.add("enable");
        operationTypes.add("disable");
        modifyDBInstanceVIPAvailable.OPERATION_TYPE_SET = operationTypes;
    }
    /**
     * TC01: 实例不活跃时抛出异常
     */
    @Test(expected = RdsException.class)
    public void testProcessActionRequest_InstanceNotActive() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(false);
        try {
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams);
        } catch (RdsException e) {
            assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_STATUS.getDesc(), e.getMessage());
            throw e;
        }
    }
    /**
     * TC02: 共享实例抛出异常
     */
    @Test(expected = RdsException.class)
    public void testProcessActionRequest_InstanceIsShared() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(true);
        try {
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams);
        } catch (RdsException e) {
            assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_TYPE.getDesc(), e.getMessage());
            throw e;
        }
    }
    /**
     * TC03: 实例被锁定抛出异常
     */
    @Test(expected = RdsException.class)
    public void testProcessActionRequest_InstanceIsLocked() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(true);
        try {
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams);
        } catch (RdsException e) {
            assertEquals(ErrorCode.UNSUPPORTED_DBINSTANCE_LOCKMODE.getDesc(), e.getMessage());
            throw e;
        }
    }
    /**
     * TC04: 存在冲突任务抛出异常
     */
    @Test(expected = RdsException.class)
    public void testProcessActionRequest_ConflictTaskExists() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);
        // 模拟存在冲突任务
        when(taskService.countTaskQueueByCondition(anyMap())).thenReturn(1);
        try {
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams);
        } catch (RdsException e) {
            assertEquals(ErrorCode.TASK_HAS_EXIST.getDesc(), e.getMessage());
            throw e;
        }
    }
    /**
     * TC05: LbId 参数为空抛出异常
     */
    @Test
    public void testProcessActionRequest_MissingLbId() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);
        when(mysqlParamSupport.getParameterValue(actionParams,"LbId")).thenReturn(null);
        // Act & Assert
        RdsException exception = assertThrows(RdsException.class, () ->
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams));
        System.out.println(exception.getMessage());
        System.out.println(MysqlErrorCode.MISSING_PARAM_LBID.getDesc());
        assertEquals(MysqlErrorCode.MISSING_PARAM_LBID.getDesc(), exception.getMessage());
    }

    /**
     * TC06: Type 参数为空抛出异常
     */
    @Test
    public void testDoActionRequest_MissingTypeParameter_ThrowsException() throws Exception {
        // Arrange
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);

        Map<String, String> actionParams = new HashMap<>();
        when(mysqlParamSupport.getParameterValue(actionParams, "LbId")).thenReturn("testLbId");
        when(mysqlParamSupport.getParameterValue(actionParams, "Type")).thenReturn(null);

        // Act & Assert
        RdsException exception = assertThrows(RdsException.class, () ->
                modifyDBInstanceVIPAvailable.doActionRequest(custins, actionParams));

        System.out.println(exception.getMessage());
        System.out.println(MysqlErrorCode.MISSING_PARAM_TYPE.getDesc());
        assertEquals(MysqlErrorCode.MISSING_PARAM_TYPE.getDesc(), exception.getMessage());
    }
    /**
     * TC07: Type 参数非法抛出异常
     */
    @Test
    public void testProcessActionRequest_InvalidType() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);
        when(mysqlParamSupport.getParameterValue(actionParams,"LbId")).thenReturn("lb-123");
        when(mysqlParamSupport.getParameterValue(actionParams,"Type")).thenReturn("UNKNOWN");
        // Act & Assert
        RdsException exception = assertThrows(RdsException.class, () ->
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams));
        System.out.println(exception.getMessage());
        System.out.println(MysqlErrorCode.INVALID_OPERATION_TYPE.getDesc());
        assertEquals(MysqlErrorCode.INVALID_OPERATION_TYPE.getDesc(), exception.getMessage());

    }
    /**
     * TC08: 无法找到匹配的 VIP 地址
     */
    @Test
    public void testProcessActionRequest_NoMatchingVip() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);
        when(custins.getId()).thenReturn(1);
        when(mysqlParamSupport.getParameterValue(actionParams,"LbId")).thenReturn("lb-123");
        when(mysqlParamSupport.getParameterValue(actionParams,"Type")).thenReturn("enable");
        List<CustinsConnAddrDO> connAddrList = new ArrayList<>();
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(anyInt(), anyInt(), anyInt())).thenReturn(connAddrList);
        // Act & Assert
        RdsException exception = assertThrows(RdsException.class, () ->
            modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams));
        System.out.println(exception.getMessage());
        System.out.println(MysqlErrorCode.INVALID_LBID_PARAM.getDesc());
        assertEquals(MysqlErrorCode.INVALID_LBID_PARAM.getDesc(), exception.getMessage());
    }
    /**
     * TC09: 正常流程测试
     */
    @Test
    public void testProcessActionRequest_Success() throws Exception {
        Map<String, String> actionParams = new HashMap<>();
        CustInstanceDO custins = mock(CustInstanceDO.class);
        when(custins.isActive()).thenReturn(true);
        when(custins.isShare()).thenReturn(false);
        when(custins.isLock()).thenReturn(false);
        when(custins.getId()).thenReturn(1);
        when(custins.getInsName()).thenReturn("test-instance");
        when(mysqlParamSupport.getParameterValue(actionParams,"LbId")).thenReturn("lb-123");
        when(mysqlParamSupport.getParameterValue(actionParams,"Type")).thenReturn("enable");
        when(mysqlParamSupport.getAction(actionParams)).thenReturn("ModifyVIP");
        when(mysqlParamSupport.getOperatorId(actionParams)).thenReturn(1001);
        when(mysqlParamSupport.getPenginePolicyID(actionParams)).thenReturn(2001);
        // 构造连接地址
        CustinsConnAddrDO connAddr = new CustinsConnAddrDO();
        connAddr.setVpcId("vpc-1");
        connAddr.setVip("***********");
        connAddr.setConnAddrCust("redis://***********:6379");
        connAddr.setNetType(1);
        List<CustinsConnAddrDO> connAddrList = new ArrayList<>();
        connAddrList.add(connAddr);
        when(connAddrCustinsService.getCustinsConnAddrByCustinsId(custins.getId(), null, 0)).thenReturn(connAddrList);
        when(resourceService.getLbIdByVip(eq("vpc-1"), eq("***********"))).thenReturn("lb-123");
        // 模拟任务创建
        TaskQueueDO taskQueue = mock(TaskQueueDO.class);
        when(taskQueue.getId()).thenReturn(3001);
        // 执行
        Map<String, Object> result = modifyDBInstanceVIPAvailable.doActionRequest(custins,actionParams);
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.get("DBInstanceID"));
        assertEquals("test-instance", result.get("DBInstanceName"));
        assertEquals("redis://***********:6379", result.get("ConnectionString"));
        assertEquals("lb-123", result.get("LbId"));
        assertEquals(1, result.get("DBInstanceNetType"));
    }
}
